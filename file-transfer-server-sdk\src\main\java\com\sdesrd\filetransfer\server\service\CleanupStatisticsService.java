package com.sdesrd.filetransfer.server.service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

import org.springframework.stereotype.Service;

import com.sdesrd.filetransfer.server.config.CleanupConstants;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 清理统计服务
 * 
 * 负责收集和管理清理操作的统计信息，提供线程安全的统计数据访问
 * 
 * <AUTHOR> Transfer SDK
 * @since 1.0.0
 */
@Slf4j
@Service
public class CleanupStatisticsService {
    
    /**
     * 累计清理的传输记录数
     */
    private final AtomicLong totalCleanedTransferRecords = new AtomicLong(0);
    
    /**
     * 累计清理的分块记录数
     */
    private final AtomicLong totalCleanedChunkRecords = new AtomicLong(0);
    
    /**
     * 累计清理的物理文件数
     */
    private final AtomicLong totalCleanedPhysicalFiles = new AtomicLong(0);
    
    /**
     * 累计清理操作执行次数
     */
    private final AtomicLong totalCleanupOperations = new AtomicLong(0);
    
    /**
     * 累计清理操作失败次数
     */
    private final AtomicLong totalCleanupFailures = new AtomicLong(0);
    
    /**
     * 最后一次清理操作信息
     */
    private final AtomicReference<CleanupOperationInfo> lastCleanupOperation = 
        new AtomicReference<>(new CleanupOperationInfo());
    
    /**
     * 服务启动时间
     */
    private final Instant serviceStartTime = Instant.now();
    
    /**
     * 记录清理操作开始
     * 
     * @return 操作信息对象，用于后续更新
     */
    public CleanupOperationInfo startCleanupOperation() {
        CleanupOperationInfo operation = new CleanupOperationInfo();
        operation.setStartTime(Instant.now());
        operation.setStatus(CleanupOperationStatus.RUNNING);
        
        totalCleanupOperations.incrementAndGet();
        lastCleanupOperation.set(operation);
        
        log.debug("清理操作开始 - 操作ID: {}", operation.getOperationId());
        return operation;
    }
    
    /**
     * 记录清理操作完成
     * 
     * @param operation 操作信息
     * @param cleanedTransferRecords 清理的传输记录数
     * @param cleanedChunkRecords 清理的分块记录数
     * @param cleanedPhysicalFiles 清理的物理文件数
     */
    public void completeCleanupOperation(CleanupOperationInfo operation, 
                                       long cleanedTransferRecords, 
                                       long cleanedChunkRecords, 
                                       long cleanedPhysicalFiles) {
        operation.setEndTime(Instant.now());
        operation.setStatus(CleanupOperationStatus.COMPLETED);
        operation.setCleanedTransferRecords(cleanedTransferRecords);
        operation.setCleanedChunkRecords(cleanedChunkRecords);
        operation.setCleanedPhysicalFiles(cleanedPhysicalFiles);
        operation.setDurationMs(operation.getEndTime().toEpochMilli() - operation.getStartTime().toEpochMilli());
        
        // 更新累计统计
        totalCleanedTransferRecords.addAndGet(cleanedTransferRecords);
        totalCleanedChunkRecords.addAndGet(cleanedChunkRecords);
        totalCleanedPhysicalFiles.addAndGet(cleanedPhysicalFiles);
        
        // 记录日志
        long totalCleaned = cleanedTransferRecords + cleanedChunkRecords;
        if (totalCleaned >= CleanupConstants.CLEANUP_LOG_THRESHOLD) {
            log.info("清理操作完成 - 传输记录: {}, 分块记录: {}, 物理文件: {}, 耗时: {}ms", 
                    cleanedTransferRecords, cleanedChunkRecords, cleanedPhysicalFiles, operation.getDurationMs());
        } else {
            log.debug("清理操作完成 - 传输记录: {}, 分块记录: {}, 物理文件: {}, 耗时: {}ms", 
                    cleanedTransferRecords, cleanedChunkRecords, cleanedPhysicalFiles, operation.getDurationMs());
        }
    }
    
    /**
     * 记录清理操作失败
     * 
     * @param operation 操作信息
     * @param error 错误信息
     */
    public void failCleanupOperation(CleanupOperationInfo operation, String error) {
        operation.setEndTime(Instant.now());
        operation.setStatus(CleanupOperationStatus.FAILED);
        operation.setErrorMessage(error);
        operation.setDurationMs(operation.getEndTime().toEpochMilli() - operation.getStartTime().toEpochMilli());
        
        totalCleanupFailures.incrementAndGet();
        
        log.error("清理操作失败 - 操作ID: {}, 错误: {}, 耗时: {}ms", 
                operation.getOperationId(), error, operation.getDurationMs());
    }
    
    /**
     * 获取清理统计信息
     * 
     * @return 统计信息
     */
    public CleanupStatistics getStatistics() {
        CleanupStatistics stats = new CleanupStatistics();
        stats.setTotalCleanedTransferRecords(totalCleanedTransferRecords.get());
        stats.setTotalCleanedChunkRecords(totalCleanedChunkRecords.get());
        stats.setTotalCleanedPhysicalFiles(totalCleanedPhysicalFiles.get());
        stats.setTotalCleanupOperations(totalCleanupOperations.get());
        stats.setTotalCleanupFailures(totalCleanupFailures.get());
        stats.setServiceStartTime(serviceStartTime);
        stats.setLastCleanupOperation(lastCleanupOperation.get());
        
        // 计算成功率
        long totalOps = stats.getTotalCleanupOperations();
        if (totalOps > 0) {
            double successRate = (double) (totalOps - stats.getTotalCleanupFailures()) / totalOps * 100;
            stats.setSuccessRate(successRate);
        } else {
            stats.setSuccessRate(100.0);
        }
        
        return stats;
    }
    
    /**
     * 重置统计信息
     * 仅重置累计数据，不影响当前操作信息
     */
    public void resetStatistics() {
        totalCleanedTransferRecords.set(0);
        totalCleanedChunkRecords.set(0);
        totalCleanedPhysicalFiles.set(0);
        totalCleanupOperations.set(0);
        totalCleanupFailures.set(0);
        
        log.info("清理统计信息已重置");
    }
    
    /**
     * 清理操作信息
     */
    @Data
    public static class CleanupOperationInfo {
        /**
         * 操作ID（使用时间戳生成）
         */
        private String operationId = String.valueOf(System.currentTimeMillis());
        
        /**
         * 开始时间
         */
        private Instant startTime;
        
        /**
         * 结束时间
         */
        private Instant endTime;
        
        /**
         * 操作状态
         */
        private CleanupOperationStatus status = CleanupOperationStatus.PENDING;
        
        /**
         * 清理的传输记录数
         */
        private long cleanedTransferRecords = 0;
        
        /**
         * 清理的分块记录数
         */
        private long cleanedChunkRecords = 0;
        
        /**
         * 清理的物理文件数
         */
        private long cleanedPhysicalFiles = 0;
        
        /**
         * 操作耗时（毫秒）
         */
        private long durationMs = 0;
        
        /**
         * 错误信息（如果操作失败）
         */
        private String errorMessage;
        
        /**
         * 获取格式化的开始时间
         */
        public String getFormattedStartTime() {
            if (startTime == null) {
                return "未开始";
            }
            return LocalDateTime.ofInstant(startTime, ZoneId.systemDefault())
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        
        /**
         * 获取格式化的结束时间
         */
        public String getFormattedEndTime() {
            if (endTime == null) {
                return "未结束";
            }
            return LocalDateTime.ofInstant(endTime, ZoneId.systemDefault())
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
    }
    
    /**
     * 清理操作状态枚举
     */
    public enum CleanupOperationStatus {
        /**
         * 等待中
         */
        PENDING,
        
        /**
         * 运行中
         */
        RUNNING,
        
        /**
         * 已完成
         */
        COMPLETED,
        
        /**
         * 已失败
         */
        FAILED
    }
    
    /**
     * 清理统计信息
     */
    @Data
    public static class CleanupStatistics {
        /**
         * 累计清理的传输记录数
         */
        private long totalCleanedTransferRecords;
        
        /**
         * 累计清理的分块记录数
         */
        private long totalCleanedChunkRecords;
        
        /**
         * 累计清理的物理文件数
         */
        private long totalCleanedPhysicalFiles;
        
        /**
         * 累计清理操作执行次数
         */
        private long totalCleanupOperations;
        
        /**
         * 累计清理操作失败次数
         */
        private long totalCleanupFailures;
        
        /**
         * 清理操作成功率（百分比）
         */
        private double successRate;
        
        /**
         * 服务启动时间
         */
        private Instant serviceStartTime;
        
        /**
         * 最后一次清理操作信息
         */
        private CleanupOperationInfo lastCleanupOperation;
        
        /**
         * 获取累计清理的总记录数
         */
        public long getTotalCleanedRecords() {
            return totalCleanedTransferRecords + totalCleanedChunkRecords;
        }
        
        /**
         * 获取格式化的服务启动时间
         */
        public String getFormattedServiceStartTime() {
            return LocalDateTime.ofInstant(serviceStartTime, ZoneId.systemDefault())
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
    }
}
