# 文件传输SDK清理机制深度分析报告

## 🔍 1. 数据库自动重建机制分析

### 1.1 触发条件和执行逻辑

**触发条件：**
```java
// 1. 手动触发（通过API）
POST /filetransfer/api/database/rebuild

// 2. 数据库健康检查失败时的自动触发
public boolean isDatabaseHealthy() {
    // 每30秒检查一次数据库健康状态
    if (currentTime - lastCheck < DATABASE_HEALTH_CHECK_INTERVAL_MS) {
        return databaseHealthy.get();
    }
    return performDatabaseHealthCheck();
}
```

**执行逻辑：**
```java
@Transactional
public DatabaseRebuildResult rebuildDatabaseFromDisk(String initiatorUser, String initiatorIp) {
    // 1. 并发控制：使用AtomicBoolean防止并发重建
    if (!rebuildInProgress.compareAndSet(false, true)) {
        return "重建操作已在进行中";
    }
    
    try {
        // 2. 清空现有记录
        transferRecordMapper.delete(null);
        
        // 3. 调用DatabaseFallbackService扫描重建
        Map<String, Object> scanResult = databaseFallbackService.scanAndRebuildFromMetadata();
        
        // 4. 处理多用户存储路径
        for (String storagePath : allStoragePaths) {
            scanDirectoryAndRebuild(storagePath);
        }
    } finally {
        rebuildInProgress.set(false); // 释放锁
    }
}
```

### 1.2 重建过程对传输操作的影响

**影响分析：**
- ✅ **读操作无影响**：重建过程中下载功能通过容错机制正常工作
- ⚠️ **写操作有影响**：新的上传操作可能会遇到数据库锁定
- 🔄 **自动恢复**：重建完成后所有功能自动恢复

**并发控制机制：**
```java
// 使用AtomicBoolean实现简单的分布式锁
private final AtomicBoolean rebuildInProgress = new AtomicBoolean(false);
private final AtomicReference<RebuildOperationInfo> currentRebuildInfo = new AtomicReference<>();
```

### 1.3 重建机制与清理机制的冲突分析

**潜在冲突：**
1. **时间窗口冲突**：清理操作删除记录后，重建操作可能立即重建相同记录
2. **资源竞争**：两个操作都需要访问数据库和文件系统

**解决方案：**
```java
// 建议在清理服务中添加重建状态检查
private boolean isRebuildInProgress() {
    return databaseManagementService.getRebuildStatus()
        .get("inProgress").equals(true);
}

private void cleanupExpiredRecords() {
    if (!properties.isCleanupEnabled() || isRebuildInProgress()) {
        log.debug("跳过清理操作：清理功能禁用或重建正在进行");
        return;
    }
    // 执行清理逻辑...
}
```

## 🎯 2. 秒传机制的数据保留策略优化

### 2.1 当前清理机制对秒传的影响

**跨用户秒传机制：**
```java
// 查找任意用户的已完成文件（用于跨用户秒传）
QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
query.eq("status", 2);
query.like("ext_info", fileMd5); // 在扩展信息中查找MD5
```

**影响评估：**
- ⚠️ **24小时后失效**：默认配置下，24小时后记录被清理，跨用户秒传失效
- ✅ **智能保护策略**：已实现保留50%较新记录的策略
- 🔄 **容错恢复**：通过info.json可以重建记录

### 2.2 优化建议

**策略1：分层保留策略**
```yaml
file:
  transfer:
    server:
      # 普通记录保留24小时
      record-expire-time: 86400000
      # 秒传候选记录保留7天
      fast-upload-candidate-retain-time: 604800000
      # 热门文件永久保留
      popular-file-permanent-retain: true
```

**策略2：基于文件热度的保留**
```java
/**
 * 基于文件访问频率的智能保留策略
 */
private List<FileTransferRecord> filterSafeToDeleteRecords(List<FileTransferRecord> expiredRecords) {
    // 统计文件的秒传使用频率
    Map<String, Integer> filePopularity = calculateFilePopularity(expiredRecords);
    
    return expiredRecords.stream()
        .filter(record -> {
            String fileMd5 = extractMd5FromExtInfo(record.getExtInfo());
            int popularity = filePopularity.getOrDefault(fileMd5, 0);
            
            // 热门文件（被秒传次数 > 3）保留更长时间
            if (popularity > 3) {
                return false; // 不删除
            }
            
            // 其他文件按正常策略清理
            return true;
        })
        .collect(Collectors.toList());
}
```

### 2.3 索引优化支持长期保留

**建议添加的索引：**
```sql
-- 优化秒传查询性能
CREATE INDEX idx_transfer_record_md5_status ON file_transfer_record(status) 
WHERE ext_info LIKE '%md5%';

-- 优化清理查询性能
CREATE INDEX idx_transfer_record_cleanup ON file_transfer_record(create_time, status);

-- 优化文件查找性能
CREATE INDEX idx_transfer_record_file_id ON file_transfer_record(file_id, status);
```

## 🧩 3. 分块记录清理策略评估

### 3.1 分块记录的生命周期

**分块记录用途：**
1. **断点续传**：记录已上传的分块，支持传输中断后继续
2. **完整性验证**：每个分块都有MD5校验
3. **故障排查**：保留传输过程的详细信息

**当前清理策略：**
```java
// 分块记录保留7天（比传输记录更长）
private long cleanupExpiredChunkRecords() {
    Instant expireTime = Instant.now().minus(properties.getChunkExpireTime(), ChronoUnit.MILLIS);
    // 清理所有过期的分块记录，不区分状态
    QueryWrapper<FileChunkRecord> query = new QueryWrapper<>();
    query.lt("create_time", expireTimeStr);
}
```

### 3.2 优化建议

**策略1：基于传输状态的分块清理**
```java
/**
 * 智能分块记录清理策略
 */
private long cleanupExpiredChunkRecords() {
    // 1. 立即清理已完成传输的分块记录
    long completedChunksCleared = cleanupCompletedTransferChunks();
    
    // 2. 保留失败传输的分块记录用于重试
    long failedChunksCleared = cleanupFailedTransferChunks();
    
    // 3. 清理孤立的分块记录（对应的传输记录已被删除）
    long orphanedChunksCleared = cleanupOrphanedChunks();
    
    return completedChunksCleared + failedChunksCleared + orphanedChunksCleared;
}

/**
 * 清理已完成传输的分块记录
 */
private long cleanupCompletedTransferChunks() {
    // 查找已完成的传输记录
    QueryWrapper<FileTransferRecord> transferQuery = new QueryWrapper<>();
    transferQuery.eq("status", CleanupConstants.TRANSFER_STATUS_COMPLETED)
                 .lt("complete_time", getExpireTimeString(properties.getRecordExpireTime()));
    
    List<FileTransferRecord> completedTransfers = transferRecordMapper.selectList(transferQuery);
    
    if (completedTransfers.isEmpty()) {
        return 0;
    }
    
    // 删除对应的分块记录
    List<String> transferIds = completedTransfers.stream()
        .map(FileTransferRecord::getId)
        .collect(Collectors.toList());
    
    QueryWrapper<FileChunkRecord> chunkQuery = new QueryWrapper<>();
    chunkQuery.in("transfer_id", transferIds);
    
    return chunkRecordMapper.delete(chunkQuery);
}
```

**策略2：传输完成后立即清理**
```java
// 在FileTransferService.completeUpload()方法中添加
@Transactional
public UploadCompleteResponse completeUpload(String transferId, String username) {
    // ... 现有逻辑 ...
    
    if (record.getStatus() == 2) { // 传输完成
        // 立即清理分块记录，释放存储空间
        cleanupChunkRecordsForTransfer(transferId);
        log.info("传输完成，已清理分块记录 - 传输ID: {}", transferId);
    }
    
    return response;
}

private void cleanupChunkRecordsForTransfer(String transferId) {
    QueryWrapper<FileChunkRecord> query = new QueryWrapper<>();
    query.eq("transfer_id", transferId);
    int deletedCount = chunkRecordMapper.delete(query);
    log.debug("清理分块记录 - 传输ID: {}, 清理数量: {}", transferId, deletedCount);
}
```

## 🗑️ 4. 失败记录的完整清理方案

### 4.1 当前失败记录处理

**现状分析：**
- ✅ 失败记录保留3天用于问题分析
- ⚠️ 只清理数据库记录，不清理物理文件
- ⚠️ 可能存在临时文件和分块文件残留

### 4.2 完整清理方案设计

**增强的失败记录清理：**
```java
/**
 * 完整清理失败的传输记录
 */
private long cleanupFailedTransferRecords() {
    Instant expireTime = Instant.now().minus(properties.getFailedRecordRetainTime(), ChronoUnit.MILLIS);
    String expireTimeStr = expireTime.toString();
    
    // 查询过期的失败记录
    QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
    query.eq("status", CleanupConstants.TRANSFER_STATUS_FAILED)
         .lt("create_time", expireTimeStr);
    
    List<FileTransferRecord> failedRecords = transferRecordMapper.selectList(query);
    
    if (failedRecords.isEmpty()) {
        return 0;
    }
    
    long cleanedCount = 0;
    
    for (FileTransferRecord record : failedRecords) {
        try {
            // 1. 清理物理文件（包括临时文件和分块文件）
            cleanupFailedTransferFiles(record);
            
            // 2. 清理分块记录
            cleanupChunkRecordsForTransfer(record.getId());
            
            // 3. 删除数据库记录
            transferRecordMapper.deleteById(record.getId());
            
            cleanedCount++;
            log.info("完整清理失败传输记录 - ID: {}, 文件: {}", 
                    record.getId(), record.getFileName());
            
        } catch (Exception e) {
            log.error("清理失败传输记录异常 - ID: {}, 错误: {}", 
                    record.getId(), e.getMessage());
        }
    }
    
    return cleanedCount;
}

/**
 * 清理失败传输的物理文件
 */
private void cleanupFailedTransferFiles(FileTransferRecord record) {
    try {
        // 1. 清理主文件
        File mainFile = new File(record.getFilePath());
        if (mainFile.exists()) {
            if (mainFile.delete()) {
                log.debug("删除失败传输主文件: {}", record.getFilePath());
            }
        }
        
        // 2. 清理临时目录和分块文件
        File parentDir = mainFile.getParentFile();
        if (parentDir != null && parentDir.exists()) {
            // 清理同目录下的临时文件
            File[] tempFiles = parentDir.listFiles((dir, name) -> 
                name.startsWith(record.getFileId() + ".tmp") || 
                name.startsWith(record.getFileId() + ".chunk"));
            
            if (tempFiles != null) {
                for (File tempFile : tempFiles) {
                    if (tempFile.delete()) {
                        log.debug("删除临时文件: {}", tempFile.getAbsolutePath());
                    }
                }
            }
            
            // 如果目录为空，删除目录
            if (isDirectoryEmpty(parentDir)) {
                if (parentDir.delete()) {
                    log.debug("删除空目录: {}", parentDir.getAbsolutePath());
                }
            }
        }
        
    } catch (Exception e) {
        log.warn("清理失败传输文件时发生异常 - 文件: {}, 错误: {}", 
                record.getFilePath(), e.getMessage());
    }
}

/**
 * 检查目录是否为空
 */
private boolean isDirectoryEmpty(File directory) {
    if (!directory.isDirectory()) {
        return false;
    }
    
    String[] files = directory.list();
    return files == null || files.length == 0;
}
```

### 4.3 并发安全保障

**并发访问控制：**
```java
/**
 * 线程安全的失败记录清理
 */
private final ConcurrentHashMap<String, Boolean> cleanupInProgress = new ConcurrentHashMap<>();

private void cleanupFailedTransferFiles(FileTransferRecord record) {
    String fileId = record.getFileId();
    
    // 防止同一文件的并发清理
    if (cleanupInProgress.putIfAbsent(fileId, true) != null) {
        log.debug("文件清理已在进行中，跳过 - fileId: {}", fileId);
        return;
    }
    
    try {
        // 执行清理逻辑...
        performFileCleanup(record);
        
    } finally {
        cleanupInProgress.remove(fileId);
    }
}
```

## 📊 5. 下载操作的日志记录和清理策略

### 5.1 当前下载日志机制

**现状分析：**
```java
// 当前只有性能统计，没有详细的下载日志记录
public void downloadFile(String fileId, String username, HttpServletResponse response) {
    // 记录下载统计
    PerformanceMonitor.recordDownload(totalRead, true);
    
    log.info("文件下载完成 - 用户: {}, 文件: {}, 大小: {}, 耗时: {}ms", 
            username, record.getFileName(), totalRead, duration);
}
```

**发现：**
- ✅ 有基本的性能统计（PerformanceMonitor）
- ⚠️ 缺少详细的下载日志表
- ⚠️ 无法追踪具体的下载历史
- ⚠️ 缺少审计功能

### 5.2 建议的下载日志方案

**新增下载日志表：**
```sql
CREATE TABLE file_download_log (
    id VARCHAR(36) PRIMARY KEY,
    file_id VARCHAR(36) NOT NULL,
    username VARCHAR(100) NOT NULL,
    client_ip VARCHAR(45),
    user_agent TEXT,
    download_start_time TEXT NOT NULL,
    download_end_time TEXT,
    download_size BIGINT DEFAULT 0,
    download_status INTEGER DEFAULT 0, -- 0:进行中, 1:成功, 2:失败
    error_message TEXT,
    create_time TEXT NOT NULL,
    INDEX idx_download_log_file_id (file_id),
    INDEX idx_download_log_username (username),
    INDEX idx_download_log_time (create_time)
);
```

**下载日志实体类：**
```java
@Data
@TableName("file_download_log")
public class FileDownloadLog {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    
    @TableField("file_id")
    private String fileId;
    
    @TableField("username")
    private String username;
    
    @TableField("client_ip")
    private String clientIp;
    
    @TableField("user_agent")
    private String userAgent;
    
    @TableField("download_start_time")
    private String downloadStartTime;
    
    @TableField("download_end_time")
    private String downloadEndTime;
    
    @TableField("download_size")
    private Long downloadSize;
    
    @TableField("download_status")
    private Integer downloadStatus; // 0:进行中, 1:成功, 2:失败
    
    @TableField("error_message")
    private String errorMessage;
    
    @TableField("create_time")
    private String createTime;
}
```

### 5.3 下载日志清理策略

**清理配置：**
```yaml
file:
  transfer:
    server:
      # 下载日志保留时间（毫秒），默认30天
      download-log-retain-time: 2592000000
      # 是否启用下载日志记录
      download-log-enabled: true
      # 下载日志清理批次大小
      download-log-cleanup-batch-size: 1000
```

**清理实现：**
```java
/**
 * 清理过期的下载日志
 */
private long cleanupExpiredDownloadLogs() {
    if (!properties.isDownloadLogEnabled()) {
        return 0;
    }
    
    Instant expireTime = Instant.now().minus(properties.getDownloadLogRetainTime(), ChronoUnit.MILLIS);
    String expireTimeStr = expireTime.toString();
    
    // 查询过期的下载日志
    QueryWrapper<FileDownloadLog> query = new QueryWrapper<>();
    query.lt("create_time", expireTimeStr);
    
    List<FileDownloadLog> expiredLogs = downloadLogMapper.selectList(query);
    
    if (expiredLogs.isEmpty()) {
        log.debug("没有找到过期的下载日志");
        return 0;
    }
    
    // 批量删除
    long cleanedCount = 0;
    int batchSize = Math.min(properties.getDownloadLogCleanupBatchSize(), expiredLogs.size());
    
    for (int i = 0; i < expiredLogs.size(); i += batchSize) {
        int endIndex = Math.min(i + batchSize, expiredLogs.size());
        List<FileDownloadLog> batch = expiredLogs.subList(i, endIndex);
        
        List<String> ids = batch.stream()
            .map(FileDownloadLog::getId)
            .collect(Collectors.toList());
        
        QueryWrapper<FileDownloadLog> deleteQuery = new QueryWrapper<>();
        deleteQuery.in("id", ids);
        
        cleanedCount += downloadLogMapper.delete(deleteQuery);
    }
    
    if (cleanedCount > 0) {
        log.info("清理过期下载日志完成，清理数量: {}", cleanedCount);
    }
    
    return cleanedCount;
}
```

## 🎯 总结和建议

### 核心优化建议

1. **重建与清理协调**：添加重建状态检查，避免冲突
2. **秒传保护策略**：实现基于文件热度的智能保留
3. **分块记录优化**：传输完成后立即清理分块记录
4. **失败记录完整清理**：包含物理文件和临时文件的清理
5. **下载日志系统**：建立完整的下载审计和清理机制

### 推荐配置

```yaml
file:
  transfer:
    server:
      # 基础清理配置
      cleanup-enabled: true
      cleanup-interval: 3600000
      
      # 分层保留策略
      record-expire-time: 172800000      # 48小时
      chunk-expire-time: 86400000        # 24小时（传输完成后立即清理）
      failed-record-retain-time: 259200000  # 3天
      download-log-retain-time: 2592000000  # 30天
      
      # 安全配置
      delete-physical-files: false       # 生产环境建议false
      max-batch-delete-size: 500
      
      # 新增配置
      fast-upload-candidate-retain-time: 604800000  # 7天
      popular-file-permanent-retain: true
      download-log-enabled: true
```

这个分析报告提供了全面的清理机制优化方案，确保在维护数据库性能的同时，最大程度保持系统功能的完整性。
