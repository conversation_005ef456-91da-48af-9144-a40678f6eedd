#!/bin/bash
# Java环境变量设置脚本
# 用途: 设置正确的Java 8环境变量

# Java 8环境路径
export JAVA_HOME="$HOME/.jdks/corretto-1.8.0_452"
export PATH="$JAVA_HOME/bin:$PATH"

# Maven环境变量
export MAVEN_OPTS="-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
export MAVEN_OPTS="$MAVEN_OPTS -Djava.security.policy=all.policy"
export MAVEN_OPTS="$MAVEN_OPTS -Djava.home=$JAVA_HOME"

echo "Java环境变量已设置:"
echo "  JAVA_HOME: $JAVA_HOME"
echo "  Java版本: $(java -version 2>&1 | head -n 1)"
