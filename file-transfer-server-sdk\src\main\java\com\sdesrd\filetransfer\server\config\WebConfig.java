package com.sdesrd.filetransfer.server.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.sdesrd.filetransfer.server.interceptor.AuthInterceptor;

/**
 * Web配置类
 */
@Configuration
@ConditionalOnProperty(prefix = "file.transfer.server", name = "enabled", havingValue = "true", matchIfMissing = true)
public class WebConfig implements WebMvcConfigurer {
    
    @Autowired
    private AuthInterceptor authInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authInterceptor)
                .addPathPatterns("/api/**")  // 拦截所有API请求
                .excludePathPatterns(
                    "/actuator/**",      // 排除健康检查
                    "/swagger-ui/**",    // 排除Swagger UI
                    "/v2/api-docs/**",   // 排除API文档
                    "/doc.html",         // 排除Knife4j文档
                    "/webjars/**",       // 排除静态资源
                    "/error"             // 排除错误页面
                );
    }
} 