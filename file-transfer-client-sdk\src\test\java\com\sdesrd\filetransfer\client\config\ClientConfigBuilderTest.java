package com.sdesrd.filetransfer.client.config;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * ClientConfigBuilder 增强版配置构建器单元测试
 * 验证统一配置Builder的功能和新增的便捷方法
 */
@DisplayName("客户端配置构建器测试")
class ClientConfigBuilderTest {
    
    // ==================== 基础构建测试 ====================
    
    @Test
    @DisplayName("基本配置构建")
    void testBasicConfigBuilding() {
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr("example.com")
                .serverPort(9090)
                .auth("testUser", "testSecret")
                .build();
        
        assertEquals("example.com", config.getServerAddr());
        assertEquals(9090, config.getServerPort());
        assertEquals("testUser", config.getUser());
        assertEquals("testSecret", config.getSecretKey());
        assertEquals("http://example.com:9090/filetransfer", config.getServerUrl());
    }
    
    @Test
    @DisplayName("HTTPS配置构建")
    void testHttpsConfigBuilding() {
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr("secure.example.com")
                .serverPort(443)
                .useHttps()
                .auth("testUser", "testSecret")
                .build();
        
        assertTrue(config.isUseHttps());
        assertEquals("https://secure.example.com:443/filetransfer", config.getServerUrl());
    }
    
    @Test
    @DisplayName("上下文路径配置构建")
    void testContextPathConfigBuilding() {
        ClientConfig config = ClientConfigBuilder.create()
                .contextPath("api/v1")
                .auth("testUser", "testSecret")
                .build();
        
        assertEquals("api/v1", config.getContextPath());
        assertEquals("http://localhost:49011/api/v1", config.getServerUrl());
    }
    
    @Test
    @DisplayName("完整配置构建")
    void testFullConfigBuilding() {
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr("api.example.com")
                .serverPort(8443)
                .contextPath("filetransfer")
                .useHttps()
                .auth("admin", "super-secret-key")
                .chunkSize(2 * 1024 * 1024) // 2MB
                .maxConcurrentTransfers(10)
                .retry(5, 2000)
                .build();
        
        // 验证服务器配置
        assertEquals("api.example.com", config.getServerAddr());
        assertEquals(8443, config.getServerPort());
        assertEquals("filetransfer", config.getContextPath());
        assertTrue(config.isUseHttps());
        
        // 验证认证配置
        assertEquals("admin", config.getUser());
        assertEquals("super-secret-key", config.getSecretKey());
        
        // 验证传输配置
        assertEquals(2 * 1024 * 1024, config.getChunkSize());
        assertEquals(10, config.getMaxConcurrentTransfers());
        assertEquals(5, config.getRetryCount());
        assertEquals(2000, config.getRetryIntervalMs());
        
        // 验证URL构建
        assertEquals("https://api.example.com:8443/filetransfer", config.getServerUrl());
    }
    
    // ==================== 新增功能测试 ====================
    
    @Test
    @DisplayName("用户认证分别设置")
    void testSeparateAuthSettings() {
        ClientConfig config = ClientConfigBuilder.create()
                .user("testUser")
                .secretKey("testSecret")
                .build();
        
        assertEquals("testUser", config.getUser());
        assertEquals("testSecret", config.getSecretKey());
    }
    
    @Test
    @DisplayName("HTTPS标志位设置")
    void testHttpsFlag() {
        ClientConfig config1 = ClientConfigBuilder.create()
                .useHttps(true)
                .auth("user", "secret")
                .build();
        assertTrue(config1.isUseHttps());
        
        ClientConfig config2 = ClientConfigBuilder.create()
                .useHttps(false)
                .auth("user", "secret")
                .build();
        assertFalse(config2.isUseHttps());
    }
    
    @Test
    @DisplayName("超时时间分别设置")
    void testSeparateTimeouts() {
        ClientConfig config = ClientConfigBuilder.create()
                .connectTimeout(45)
                .readTimeout(90)
                .writeTimeout(120)
                .auth("user", "secret")
                .build();
        
        assertEquals(45, config.getConnectTimeoutSeconds());
        assertEquals(90, config.getReadTimeoutSeconds());
        assertEquals(120, config.getWriteTimeoutSeconds());
    }
    
    @Test
    @DisplayName("统一超时时间设置")
    void testUnifiedTimeouts() {
        ClientConfig config = ClientConfigBuilder.create()
                .timeouts(60)
                .auth("user", "secret")
                .build();
        
        assertEquals(60, config.getConnectTimeoutSeconds());
        assertEquals(60, config.getReadTimeoutSeconds());
        assertEquals(60, config.getWriteTimeoutSeconds());
    }
    
    @Test
    @DisplayName("并发控制配置")
    void testConcurrencySettings() {
        ClientConfig config = ClientConfigBuilder.create()
                .maxConcurrentTransfers(8)
                .maxIdleConnections(12)
                .keepAliveDuration(10)
                .auth("user", "secret")
                .build();
        
        assertEquals(8, config.getMaxConcurrentTransfers());
        assertEquals(12, config.getMaxIdleConnections());
        assertEquals(10, config.getKeepAliveDurationMinutes());
    }
    
    @Test
    @DisplayName("重试配置分别设置")
    void testSeparateRetrySettings() {
        ClientConfig config = ClientConfigBuilder.create()
                .retryCount(5)
                .retryInterval(1500)
                .auth("user", "secret")
                .build();
        
        assertEquals(5, config.getRetryCount());
        assertEquals(1500, config.getRetryIntervalMs());
    }
    
    // ==================== 便捷构建方法测试 ====================
    
    @Test
    @DisplayName("默认配置创建")
    void testDefaultConfig() {
        ClientConfig config = ClientConfigBuilder.defaultConfig("user", "secret");
        
        assertEquals("localhost", config.getServerAddr());
        assertEquals(49011, config.getServerPort());
        assertEquals("user", config.getUser());
        assertEquals("secret", config.getSecretKey());
        assertFalse(config.isUseHttps());
        assertEquals("filetransfer", config.getContextPath());
    }
    
    @Test
    @DisplayName("本地开发配置创建")
    void testLocalConfig() {
        ClientConfig config = ClientConfigBuilder.localConfig("dev", "dev-secret");
        
        assertEquals("localhost", config.getServerAddr());
        assertEquals(49011, config.getServerPort());
        assertEquals("dev", config.getUser());
        assertEquals("dev-secret", config.getSecretKey());
        assertEquals(1024 * 1024, config.getChunkSize());
        assertEquals(3, config.getMaxConcurrentTransfers());
        assertEquals(3, config.getRetryCount());
        assertEquals(1000, config.getRetryIntervalMs());
    }
    
    @Test
    @DisplayName("生产环境配置创建")
    void testProductionConfig() {
        ClientConfig config = ClientConfigBuilder.productionConfig(
                "prod.example.com", 443, "prod-user", "prod-secret");
        
        assertEquals("prod.example.com", config.getServerAddr());
        assertEquals(443, config.getServerPort());
        assertTrue(config.isUseHttps());
        assertEquals("prod-user", config.getUser());
        assertEquals("prod-secret", config.getSecretKey());
        assertEquals(2 * 1024 * 1024, config.getChunkSize());
        assertEquals(5, config.getMaxConcurrentTransfers());
        assertEquals(5, config.getRetryCount());
        assertEquals(2000, config.getRetryIntervalMs());
    }
    
    @Test
    @DisplayName("快速连接配置创建")
    void testQuickConnect() {
        ClientConfig config = ClientConfigBuilder.quickConnect(
                "quick.example.com", "quick-user", "quick-secret");
        
        assertEquals("quick.example.com", config.getServerAddr());
        assertEquals("quick-user", config.getUser());
        assertEquals("quick-secret", config.getSecretKey());
        
        // 验证其他参数使用默认值
        assertEquals(49011, config.getServerPort());
        assertFalse(config.isUseHttps());
        assertEquals("filetransfer", config.getContextPath());
    }
    
    @Test
    @DisplayName("高性能配置创建")
    void testHighPerformanceConfig() {
        ClientConfig config = ClientConfigBuilder.highPerformanceConfig(
                "fast.example.com", "fast-user", "fast-secret");
        
        assertEquals("fast.example.com", config.getServerAddr());
        assertEquals("fast-user", config.getUser());
        assertEquals("fast-secret", config.getSecretKey());
        
        // 验证性能优化配置
        assertEquals(4 * 1024 * 1024, config.getChunkSize()); // 4MB分块
        assertEquals(8, config.getMaxConcurrentTransfers()); // 更高并发
        assertEquals(120, config.getConnectTimeoutSeconds()); // 更长超时
        assertEquals(120, config.getReadTimeoutSeconds());
        assertEquals(120, config.getWriteTimeoutSeconds());
        assertEquals(3, config.getRetryCount());
        assertEquals(500, config.getRetryIntervalMs()); // 更快重试
    }
    
    // ==================== 验证失败测试 ====================
    
    @Test
    @DisplayName("构建时验证失败")
    void testBuildValidationFailure() {
        // 缺少用户名
        assertThrows(IllegalStateException.class, () -> {
            ClientConfigBuilder.create()
                    .serverAddr("example.com")
                    .build();
        });
        
        // 缺少密钥
        assertThrows(IllegalStateException.class, () -> {
            ClientConfigBuilder.create()
                    .serverAddr("example.com")
                    .auth("user", "")
                    .build();
        });
        
        // 无效的分片大小
        assertThrows(IllegalStateException.class, () -> {
            ClientConfigBuilder.create()
                    .auth("user", "secret")
                    .chunkSize(-1)
                    .build();
        });
        
        // 无效的并发传输数
        assertThrows(IllegalStateException.class, () -> {
            ClientConfigBuilder.create()
                    .auth("user", "secret")
                    .maxConcurrentTransfers(0)
                    .build();
        });
        
        // 无效的超时时间
        assertThrows(IllegalStateException.class, () -> {
            ClientConfigBuilder.create()
                    .auth("user", "secret")
                    .connectTimeout(-1)
                    .build();
        });
    }
    
    // ==================== 链式调用验证测试 ====================
    
    @Test
    @DisplayName("链式调用验证")
    void testMethodChaining() {
        ClientConfigBuilder builder = ClientConfigBuilder.create();
        
        // 验证所有方法都返回构建器实例，支持链式调用
        assertSame(builder, builder.serverAddr("test"));
        assertSame(builder, builder.serverPort(49011));
        assertSame(builder, builder.contextPath("api"));
        assertSame(builder, builder.useHttps());
        assertSame(builder, builder.useHttps(true));
        assertSame(builder, builder.auth("user", "secret"));
        assertSame(builder, builder.user("user"));
        assertSame(builder, builder.secretKey("secret"));
        assertSame(builder, builder.chunkSize(1024));
        assertSame(builder, builder.connectTimeout(30));
        assertSame(builder, builder.readTimeout(60));
        assertSame(builder, builder.writeTimeout(60));
        assertSame(builder, builder.timeouts(60));
        assertSame(builder, builder.maxConcurrentTransfers(5));
        assertSame(builder, builder.maxIdleConnections(10));
        assertSame(builder, builder.keepAliveDuration(5));
        assertSame(builder, builder.retry(3, 1000));
        assertSame(builder, builder.retryCount(3));
        assertSame(builder, builder.retryInterval(1000));
    }
    

    
    // ==================== 复杂场景测试 ====================
    
    @Test
    @DisplayName("复杂配置场景")
    void testComplexConfigurationScenario() {
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr("complex.example.com")
                .serverPort(8443)
                .contextPath("api/v2")
                .useHttps(true)
                .user("complexUser")
                .secretKey("complexSecret123")
                .chunkSize(8 * 1024 * 1024) // 8MB
                .connectTimeout(60)
                .readTimeout(180)
                .writeTimeout(300)
                .maxConcurrentTransfers(12)
                .maxIdleConnections(20)
                .keepAliveDuration(15)
                .retryCount(7)
                .retryInterval(3000)
                .build();
        
        // 验证所有配置项
        assertEquals("complex.example.com", config.getServerAddr());
        assertEquals(8443, config.getServerPort());
        assertEquals("api/v2", config.getContextPath());
        assertTrue(config.isUseHttps());
        assertEquals("complexUser", config.getUser());
        assertEquals("complexSecret123", config.getSecretKey());
        assertEquals(8 * 1024 * 1024, config.getChunkSize());
        assertEquals(60, config.getConnectTimeoutSeconds());
        assertEquals(180, config.getReadTimeoutSeconds());
        assertEquals(300, config.getWriteTimeoutSeconds());
        assertEquals(12, config.getMaxConcurrentTransfers());
        assertEquals(20, config.getMaxIdleConnections());
        assertEquals(15, config.getKeepAliveDurationMinutes());
        assertEquals(7, config.getRetryCount());
        assertEquals(3000, config.getRetryIntervalMs());
        
        // 验证URL构建
        assertEquals("https://complex.example.com:8443/api/v2", config.getServerUrl());
        
        // 验证配置有效性
        assertDoesNotThrow(() -> config.validateConfig());
    }
}
