package com.sdesrd.filetransfer.server.util;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import com.google.common.util.concurrent.RateLimiter;

import lombok.extern.slf4j.Slf4j;

/**
 * 增强的限流工具类
 * 支持角色级别的精细化限速控制
 */
@Slf4j
public class RateLimitUtils {

    /** 限流器缓存 */
    private static final ConcurrentHashMap<String, RateLimiter> RATE_LIMITERS = new ConcurrentHashMap<>();

    /** 限流器最后使用时间缓存 */
    private static final ConcurrentHashMap<String, Long> LAST_USED_TIME = new ConcurrentHashMap<>();

    /** 限流器清理间隔（毫秒） */
    private static final long CLEANUP_INTERVAL_MS = 300000L; // 5分钟

    /** 限流器空闲超时时间（毫秒） */
    private static final long IDLE_TIMEOUT_MS = 600000L; // 10分钟
    
    /**
     * 应用限流
     * 
     * @param key 限流键（如用户名_upload或用户名_download）
     * @param permitsPerSecond 每秒允许的字节数
     * @param requestBytes 请求的字节数
     */
    public static void applyRateLimit(String key, long permitsPerSecond, long requestBytes) {
        if (permitsPerSecond <= 0) {
            return; // 不限速
        }
        
        RateLimiter rateLimiter = RATE_LIMITERS.computeIfAbsent(key, k -> {
            log.debug("创建限流器 - 键: {}, 速度: {} bytes/s", k, permitsPerSecond);
            return RateLimiter.create(permitsPerSecond);
        });
        
        // 更新限流器速度（如果配置有变化）
        rateLimiter.setRate(permitsPerSecond);

        // 更新最后使用时间
        LAST_USED_TIME.put(key, System.currentTimeMillis());

        // 申请令牌
        if (requestBytes > 0) {
            double waitTime = rateLimiter.acquire((int) requestBytes);
            if (waitTime > 0) {
                log.debug("限流等待 - 键: {}, 等待时间: {}s, 请求字节: {}", key, waitTime, requestBytes);
            }
        }
    }

    /**
     * 应用限流（带超时）
     *
     * @param key 限流键
     * @param permitsPerSecond 每秒允许的字节数
     * @param requestBytes 请求的字节数
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否成功获取令牌
     */
    public static boolean applyRateLimitWithTimeout(String key, long permitsPerSecond, long requestBytes, long timeoutMs) {
        if (permitsPerSecond <= 0) {
            return true; // 不限速
        }

        RateLimiter rateLimiter = RATE_LIMITERS.computeIfAbsent(key, k -> {
            log.debug("创建限流器 - 键: {}, 速度: {} bytes/s", k, permitsPerSecond);
            return RateLimiter.create(permitsPerSecond);
        });

        // 更新限流器速度
        rateLimiter.setRate(permitsPerSecond);

        // 更新最后使用时间
        LAST_USED_TIME.put(key, System.currentTimeMillis());

        // 尝试获取令牌
        if (requestBytes > 0) {
            boolean acquired = rateLimiter.tryAcquire((int) requestBytes, timeoutMs, TimeUnit.MILLISECONDS);
            if (!acquired) {
                log.warn("限流超时 - 键: {}, 超时时间: {}ms, 请求字节: {}", key, timeoutMs, requestBytes);
            }
            return acquired;
        }

        return true;
    }

    /**
     * 获取限流器当前速率
     *
     * @param key 限流键
     * @return 当前速率（permits/second），如果不存在返回-1
     */
    public static double getCurrentRate(String key) {
        RateLimiter rateLimiter = RATE_LIMITERS.get(key);
        return rateLimiter != null ? rateLimiter.getRate() : -1;
    }

    /**
     * 清理指定用户的限流器
     *
     * @param username 用户名
     */
    public static void clearUserRateLimiters(String username) {
        String uploadKey = username + "_upload";
        String downloadKey = username + "_download";

        RATE_LIMITERS.remove(uploadKey);
        RATE_LIMITERS.remove(downloadKey);
        LAST_USED_TIME.remove(uploadKey);
        LAST_USED_TIME.remove(downloadKey);

        log.debug("清理用户限流器 - 用户: {}", username);
    }

    /**
     * 清理限流器
     */
    public static void clearRateLimiter(String key) {
        RATE_LIMITERS.remove(key);
        LAST_USED_TIME.remove(key);
    }

    /**
     * 清理所有限流器
     */
    public static void clearAllRateLimiters() {
        log.info("清理所有限流器，当前数量: {}", RATE_LIMITERS.size());
        RATE_LIMITERS.clear();
        LAST_USED_TIME.clear();
    }

    /**
     * 清理空闲的限流器
     */
    public static void cleanupIdleRateLimiters() {
        long currentTime = System.currentTimeMillis();
        int cleanedCount = 0;

        LAST_USED_TIME.entrySet().removeIf(entry -> {
            String key = entry.getKey();
            long lastUsed = entry.getValue();

            if (currentTime - lastUsed > IDLE_TIMEOUT_MS) {
                RATE_LIMITERS.remove(key);
                log.debug("清理空闲限流器 - 键: {}, 空闲时间: {}ms", key, currentTime - lastUsed);
                return true;
            }
            return false;
        });

        if (cleanedCount > 0) {
            log.info("清理空闲限流器完成，清理数量: {}, 剩余数量: {}", cleanedCount, RATE_LIMITERS.size());
        }
    }

    /**
     * 获取当前限流器数量
     */
    public static int getRateLimiterCount() {
        return RATE_LIMITERS.size();
    }

    /**
     * 获取限流器统计信息
     */
    public static String getRateLimiterStats() {
        return String.format("限流器统计 - 总数: %d, 活跃数: %d",
                RATE_LIMITERS.size(),
                LAST_USED_TIME.size());
    }
}