package com.sdesrd.filetransfer.server.exception;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件完整性异常测试
 */
@Slf4j
@DisplayName("文件完整性异常测试")
class FileIntegrityExceptionTest {
    
    private static final String TEST_INFO_PREFIX = "[测试信息]";
    
    @Test
    @DisplayName("基本构造函数测试")
    void testBasicConstructor() {
        log.info("{} 开始测试FileIntegrityException基本构造函数", TEST_INFO_PREFIX);
        
        // 准备测试数据
        String message = "文件完整性校验失败";
        String transferId = "test-transfer-123";
        String expectedMd5 = "abc123def456";
        String actualMd5 = "xyz789uvw012";
        
        // 创建异常实例
        FileIntegrityException exception = new FileIntegrityException(
                message, transferId, expectedMd5, actualMd5);
        
        // 验证异常属性
        assertEquals(message, exception.getMessage());
        assertEquals(transferId, exception.getTransferId());
        assertEquals(expectedMd5, exception.getExpectedMd5());
        assertEquals(actualMd5, exception.getActualMd5());
        assertNull(exception.getCause());
        
        log.info("{} 基本构造函数测试通过", TEST_INFO_PREFIX);
    }
    
    @Test
    @DisplayName("带原因异常的构造函数测试")
    void testConstructorWithCause() {
        log.info("{} 开始测试FileIntegrityException带原因异常的构造函数", TEST_INFO_PREFIX);
        
        // 准备测试数据
        String message = "文件完整性校验失败";
        String transferId = "test-transfer-456";
        String expectedMd5 = "expected123";
        String actualMd5 = "actual456";
        RuntimeException cause = new RuntimeException("底层IO异常");
        
        // 创建异常实例
        FileIntegrityException exception = new FileIntegrityException(
                message, transferId, expectedMd5, actualMd5, cause);
        
        // 验证异常属性
        assertEquals(message, exception.getMessage());
        assertEquals(transferId, exception.getTransferId());
        assertEquals(expectedMd5, exception.getExpectedMd5());
        assertEquals(actualMd5, exception.getActualMd5());
        assertEquals(cause, exception.getCause());
        
        log.info("{} 带原因异常的构造函数测试通过", TEST_INFO_PREFIX);
    }
    
    @Test
    @DisplayName("toString方法测试")
    void testToStringMethod() {
        log.info("{} 开始测试FileIntegrityException的toString方法", TEST_INFO_PREFIX);
        
        // 准备测试数据
        String message = "MD5校验失败";
        String transferId = "transfer-789";
        String expectedMd5 = "expected789";
        String actualMd5 = "actual123";
        
        // 创建异常实例
        FileIntegrityException exception = new FileIntegrityException(
                message, transferId, expectedMd5, actualMd5);
        
        // 验证toString输出
        String toStringResult = exception.toString();
        
        assertTrue(toStringResult.contains("FileIntegrityException"));
        assertTrue(toStringResult.contains(transferId));
        assertTrue(toStringResult.contains(expectedMd5));
        assertTrue(toStringResult.contains(actualMd5));
        assertTrue(toStringResult.contains(message));
        
        log.info("{} toString方法输出: {}", TEST_INFO_PREFIX, toStringResult);
        log.info("{} toString方法测试通过", TEST_INFO_PREFIX);
    }
    
    @Test
    @DisplayName("异常继承关系测试")
    void testExceptionInheritance() {
        log.info("{} 开始测试FileIntegrityException继承关系", TEST_INFO_PREFIX);
        
        // 创建异常实例
        FileIntegrityException exception = new FileIntegrityException(
                "测试消息", "test-id", "expected", "actual");
        
        // 验证继承关系
        assertTrue(exception instanceof FileTransferException);
        assertTrue(exception instanceof RuntimeException);
        assertTrue(exception instanceof Exception);
        assertTrue(exception instanceof Throwable);
        
        log.info("{} 异常继承关系测试通过", TEST_INFO_PREFIX);
    }
    
    @Test
    @DisplayName("空值参数处理测试")
    void testNullParameterHandling() {
        log.info("{} 开始测试FileIntegrityException空值参数处理", TEST_INFO_PREFIX);
        
        // 测试null参数
        FileIntegrityException exception = new FileIntegrityException(
                null, null, null, null);
        
        // 验证null值处理
        assertNull(exception.getMessage());
        assertNull(exception.getTransferId());
        assertNull(exception.getExpectedMd5());
        assertNull(exception.getActualMd5());
        
        // toString方法应该能正常处理null值
        String toStringResult = exception.toString();
        assertNotNull(toStringResult);
        assertTrue(toStringResult.contains("FileIntegrityException"));
        
        log.info("{} 空值参数处理测试通过", TEST_INFO_PREFIX);
    }
    
    @Test
    @DisplayName("序列化版本UID测试")
    void testSerialVersionUID() {
        log.info("{} 开始测试FileIntegrityException序列化版本UID", TEST_INFO_PREFIX);
        
        // 通过反射获取serialVersionUID字段
        try {
            java.lang.reflect.Field field = FileIntegrityException.class.getDeclaredField("serialVersionUID");
            field.setAccessible(true);
            long serialVersionUID = field.getLong(null);
            
            assertEquals(1L, serialVersionUID, "serialVersionUID应该为1L");
            
            log.info("{} 序列化版本UID值: {}", TEST_INFO_PREFIX, serialVersionUID);
            log.info("{} 序列化版本UID测试通过", TEST_INFO_PREFIX);
            
        } catch (Exception e) {
            fail("获取serialVersionUID失败: " + e.getMessage());
        }
    }
}
