package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.config.UserConfig;
import com.sdesrd.filetransfer.server.dto.FileInfo;
import com.sdesrd.filetransfer.server.dto.FileUploadCompleteResponse;
import com.sdesrd.filetransfer.server.dto.FileUploadInitRequest;
import com.sdesrd.filetransfer.server.dto.FileUploadInitResponse;
import com.sdesrd.filetransfer.server.dto.TransferProgressResponse;
import com.sdesrd.filetransfer.server.entity.FileChunkRecord;
import com.sdesrd.filetransfer.server.entity.FileTransferRecord;
import com.sdesrd.filetransfer.server.exception.FileTransferException;
import com.sdesrd.filetransfer.server.exception.FileIntegrityException;
import com.sdesrd.filetransfer.server.mapper.FileChunkRecordMapper;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;
import com.sdesrd.filetransfer.server.util.FileUtils;
import com.sdesrd.filetransfer.server.util.PerformanceMonitor;
import com.sdesrd.filetransfer.server.util.RateLimitUtils;
import com.sdesrd.filetransfer.server.util.UlidUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输服务
 */
@Slf4j
@Service
public class FileTransferService {
    
    @Autowired
    private FileTransferRecordMapper transferRecordMapper;
    
    @Autowired
    private FileChunkRecordMapper chunkRecordMapper;
    
    @Autowired
    private FileTransferProperties properties;
    
    @Autowired
    private AuthService authService;
    
    @Autowired
    private FileMetadataService metadataService;
    
    @Autowired
    private DatabaseFallbackService databaseFallbackService;
    
    /**
     * 初始化文件上传
     * 客户端指定fileId版本：验证客户端提供的fileId并实现严格的冲突检测
     */
    @Transactional
    public FileUploadInitResponse initUpload(FileUploadInitRequest request, String username, String clientIp) {
        // 获取用户配置
        UserConfig userConfig = authService.getUserConfig(username);

        // 验证客户端提供的fileId
        if (!StringUtils.hasText(request.getFileId())) {
            throw new FileTransferException("客户端必须提供fileId");
        }

        String clientFileId = request.getFileId().trim().toUpperCase();
        if (!UlidUtils.isValidUlid(clientFileId)) {
            throw new FileTransferException("客户端提供的fileId格式无效，必须是26字符的ULID格式");
        }

        // 验证文件大小
        if (request.getFileSize() > userConfig.getMaxFileSize()) {
            throw new FileTransferException("文件大小超出限制: " + request.getFileSize() + " > " + userConfig.getMaxFileSize());
        }

        // 验证文件MD5
        if (!StringUtils.hasText(request.getFileMd5())) {
            throw new FileTransferException("文件MD5不能为空");
        }

        // 处理原始文件名和文件后缀名
        String originalFileName = request.getOriginalFileName();
        String fileExtension = request.getFileExtension();
        
        // 如果没有提供原始文件名，尝试从文件后缀名构建一个默认名称
        if (!StringUtils.hasText(originalFileName)) {
            String fileMd5 = request.getFileMd5();
            if (StringUtils.hasText(fileExtension)) {
                originalFileName = fileMd5 + "." + fileExtension;
            } else {
                originalFileName = fileMd5;
            }
        }
        
        // 如果没有提供文件后缀名，但提供了原始文件名，尝试从原始文件名中提取
        if (!StringUtils.hasText(fileExtension) && StringUtils.hasText(originalFileName)) {
            int lastDotIndex = originalFileName.lastIndexOf('.');
            if (lastDotIndex > 0 && lastDotIndex < originalFileName.length() - 1) {
                fileExtension = originalFileName.substring(lastDotIndex + 1);
            }
        }
        
        // 确保文件后缀名不为null
        if (fileExtension == null) {
            fileExtension = "";
        }

        // 实现严格的fileId冲突检测
        String fileId = clientFileId; // 使用客户端指定的fileId
        String fileMd5 = request.getFileMd5();

        // 检查fileId是否已存在
        FileTransferRecord existingFileIdRecord = findCompletedFileByFileId(fileId);
        if (existingFileIdRecord != null) {
            // fileId已存在，验证MD5是否匹配
            String existingMd5 = extractOriginalMd5FromExtInfo(existingFileIdRecord.getExtInfo());
            if (!fileMd5.equals(existingMd5)) {
                // fileId存在但MD5不匹配，拒绝上传
                log.error("fileId冲突检测失败 - fileId: {}, 客户端MD5: {}, 已存在MD5: {}",
                    fileId, fileMd5, existingMd5);
                throw new FileIntegrityException(
                    String.format("fileId已存在但文件内容不同：fileId=%s，客户端MD5=%s，已存在MD5=%s",
                        fileId, fileMd5, existingMd5),
                    null, existingMd5, fileMd5);
            }
            log.info("fileId冲突检测通过 - fileId: {}, MD5匹配: {}", fileId, fileMd5);
        }

        // 生成传输ID
        String transferId = UUID.randomUUID().toString();
        String currentTime = Instant.now().toString();
        
        // 计算分块信息
        long chunkSize = request.getChunkSize() != null ? request.getChunkSize() : userConfig.getDefaultChunkSize();
        int totalChunks = (int) Math.ceil((double) request.getFileSize() / chunkSize);

        // 构建最终存储的文件名：md5.{后缀名} 或 md5（无后缀时）
        String finalFileName = StringUtils.hasText(fileExtension) ?
            fileMd5 + "." + fileExtension : fileMd5;

        // 构建文件路径：使用用户配置的存储路径 YYYYMM/fileId/md5.{后缀名}
        String filePath = buildNewFilePathForUser(fileId, finalFileName, username);

        // 客户端指定fileId模式的秒传检查
        boolean fastUpload = false;
        FileTransferRecord existingRecord = null;

        // 如果fileId已存在且MD5匹配（前面已验证），直接秒传
        if (existingFileIdRecord != null) {
            if (verifyExistingFile(existingFileIdRecord, fileMd5)) {
                log.info("文件秒传（fileId+MD5完全匹配） - 用户: {}, fileId: {}, MD5: {}",
                    username, fileId, fileMd5);
                fastUpload = true;
                existingRecord = existingFileIdRecord;
            } else {
                log.warn("fileId存在但文件完整性验证失败，将重新上传 - fileId: {}", fileId);
                // 删除损坏的文件记录和物理文件
                try {
                    deleteCorruptedFile(existingFileIdRecord);
                } catch (Exception e) {
                    log.error("删除损坏文件失败: {}", existingFileIdRecord.getFilePath(), e);
                }
            }
        } else {
            // fileId不存在，检查是否存在相同MD5的文件用于秒传
            existingRecord = findAnyCompletedFileByMd5(fileMd5, username);
            if (existingRecord != null && verifyExistingFile(existingRecord, fileMd5)) {
                log.info("文件秒传（MD5匹配） - 用户: {}, 源文件: {}, 目标fileId: {}, MD5: {}",
                    username, existingRecord.getFilePath(), fileId, fileMd5);
                fastUpload = true;
            }
        }

        // 创建传输记录
        FileTransferRecord record = new FileTransferRecord();
        record.setId(transferId);
        record.setFileId(fileId); // 使用ULID作为fileId
        record.setFileName(finalFileName); // 使用最终的文件名
        record.setOriginalFileName(originalFileName); // 设置客户端原始文件名
        record.setFileSize(request.getFileSize());
        record.setFilePath(filePath);
        record.setTransferredSize(fastUpload ? request.getFileSize() : 0L);
        record.setClientIp(clientIp);
        record.setCreateTime(currentTime);
        record.setUpdateTime(currentTime);
        record.setTotalChunks(totalChunks);
        record.setCompletedChunks(fastUpload ? totalChunks : 0);
        // 在extInfo中存储原始MD5值和文件后缀名
        record.setExtInfo("{\"originalMd5\":\"" + fileMd5 + "\",\"fileExtension\":\"" + fileExtension + "\"}");
        
        if (fastUpload) {
            // 秒传：复制已存在的文件到新位置
            try {
                // 确保目标目录存在
                ensureDirectoryExists(filePath);

                // 复制文件
                FileUtils.copyFile(existingRecord.getFilePath(), filePath);

                // 移除文件权限保护机制 - 不再设置目录为只读权限
                // 文件复制完成后，目录保持默认权限，便于后续文件操作
                log.debug("秒传文件复制完成，目录保持默认权限: {}", new File(filePath).getParent());

                record.setStatus(2); // 传输完成
                record.setCompleteTime(currentTime);
                record.setTransferredSize(request.getFileSize());
                record.setCompletedChunks(totalChunks);

                log.info("秒传复制文件成功 - 源: {}, 目标: {}, MD5: {}",
                    existingRecord.getFilePath(), filePath, fileMd5);

            } catch (Exception e) {
                log.warn("秒传复制文件失败，降级为普通上传 - 错误: {}", e.getMessage());
                fastUpload = false;
                record.setStatus(1); // 传输中
                record.setTransferredSize(0L);
                record.setCompletedChunks(0);
                ensureDirectoryExists(filePath);
            }
        } else {
            record.setStatus(1); // 传输中
            ensureDirectoryExists(filePath);
        }
        
        transferRecordMapper.insert(record);

        FileUploadInitResponse response = new FileUploadInitResponse();
        response.setTransferId(transferId);
        response.setFileId(fileId); // 返回ULID格式的fileId
        response.setFileName(finalFileName); // 返回最终存储的文件名
        response.setChunkSize(chunkSize);
        response.setTotalChunks(totalChunks);
        response.setFastUpload(fastUpload);

        log.info("初始化上传完成 - 用户: {}, 传输ID: {}, fileId: {}, 文件名: {}, 秒传: {}",
            username, transferId, fileId, finalFileName, fastUpload);
        return response;
    }
    
    /**
     * 上传文件分块
     */
    @Transactional
    public void uploadChunk(String transferId, Integer chunkIndex, String chunkMd5, MultipartFile chunkFile, String username) {
        // 查询传输记录
        FileTransferRecord record = transferRecordMapper.selectById(transferId);
        if (record == null) {
            throw new FileTransferException("传输记录不存在: " + transferId);
        }
        
        if (record.getStatus() == 2) {
            log.warn("传输已完成，忽略分块上传 - 传输ID: {}, 分块: {}", transferId, chunkIndex);
            return;
        }
        
        if (record.getStatus() == 3) {
            throw new FileTransferException("传输已失败，无法继续上传 - 传输ID: " + transferId);
        }
        
        // 获取用户配置
        UserConfig userConfig = authService.getUserConfig(username);
        
        // 检查分块是否已存在
        QueryWrapper<FileChunkRecord> chunkQuery = new QueryWrapper<>();
        chunkQuery.eq("transfer_id", transferId).eq("chunk_index", chunkIndex);
        FileChunkRecord existingChunk = chunkRecordMapper.selectOne(chunkQuery);
        
        if (existingChunk != null && existingChunk.getStatus() == 1) {
            log.debug("分块已存在，跳过上传 - 传输ID: {}, 分块: {}", transferId, chunkIndex);
            return;
        }
        
        try {
            // 验证分块大小
            if (chunkFile.getSize() > userConfig.getMaxInMemorySize()) {
                throw new FileTransferException("分块大小超出限制: " + chunkFile.getSize() + " > " + userConfig.getMaxInMemorySize());
            }
            
            // 应用上传限速
            if (userConfig.getRateLimitEnabled()) {
                RateLimitUtils.applyRateLimit(username + "_upload", userConfig.getUploadRateLimit(), chunkFile.getSize());
            }
            
            // 验证分块MD5
            byte[] chunkData = chunkFile.getBytes();
            String actualMd5 = FileUtils.calculateMD5(chunkData);
            if (!actualMd5.equals(chunkMd5)) {
                throw new FileTransferException("分块MD5校验失败: expected=" + chunkMd5 + ", actual=" + actualMd5);
            }
            
            // 保存分块文件
            String chunkPath = buildChunkPath(record.getFilePath(), chunkIndex);
            ensureDirectoryExists(chunkPath);
            
            try (FileOutputStream fos = new FileOutputStream(chunkPath)) {
                fos.write(chunkData);
                fos.flush();
            }
            
            // 记录分块信息
            FileChunkRecord chunkRecord = new FileChunkRecord();
            chunkRecord.setId(UUID.randomUUID().toString());
            chunkRecord.setTransferId(transferId);
            chunkRecord.setFileId(record.getFileId());
            chunkRecord.setChunkIndex(chunkIndex);
            chunkRecord.setChunkSize((long) chunkData.length);
            chunkRecord.setChunkOffset((long) chunkIndex * userConfig.getDefaultChunkSize());
            chunkRecord.setChunkPath(chunkPath);
            chunkRecord.setChunkMd5(chunkMd5);
            chunkRecord.setStatus(1); // 完成
            chunkRecord.setRetryCount(0);
            chunkRecord.setCreateTime(Instant.now().toString());
            chunkRecord.setCompleteTime(Instant.now().toString());
            
            if (existingChunk != null) {
                chunkRecord.setId(existingChunk.getId());
                chunkRecordMapper.updateById(chunkRecord);
            } else {
                chunkRecordMapper.insert(chunkRecord);
            }
            
            // 更新传输记录
            updateTransferProgress(transferId);
            
            log.debug("分块上传完成 - 传输ID: {}, 分块: {}, 大小: {}", transferId, chunkIndex, chunkData.length);
            
        } catch (IOException e) {
            log.error("保存分块文件失败 - 传输ID: {}, 分块: {}", transferId, chunkIndex, e);
            
            // 记录失败的分块
            if (existingChunk == null) {
                FileChunkRecord failedChunk = new FileChunkRecord();
                failedChunk.setId(UUID.randomUUID().toString());
                failedChunk.setTransferId(transferId);
                failedChunk.setFileId(record.getFileId());
                failedChunk.setChunkIndex(chunkIndex);
                failedChunk.setStatus(2); // 失败
                failedChunk.setFailReason("保存分块文件失败: " + e.getMessage());
                failedChunk.setCreateTime(Instant.now().toString());
                chunkRecordMapper.insert(failedChunk);
            }
            
            throw new FileTransferException("保存分块文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 完成文件上传
     * 重构后的版本：返回完整的上传结果信息，包括fileId和相对路径
     */
    @Transactional
    public FileUploadCompleteResponse completeUpload(String transferId, String username) {
        // 查询传输记录
        FileTransferRecord record = transferRecordMapper.selectById(transferId);
        if (record == null) {
            throw new FileTransferException("传输记录不存在: " + transferId);
        }
        
        if (record.getStatus() == 2) {
            log.info("传输已完成 - 传输ID: {}", transferId);
            // 构建并返回完成响应
            return buildCompleteResponse(record, transferId, username, true);
        }
        
        // 检查所有分块是否上传完成
        QueryWrapper<FileChunkRecord> chunkQuery = new QueryWrapper<>();
        chunkQuery.eq("transfer_id", transferId).eq("status", 1);
        List<FileChunkRecord> completedChunks = chunkRecordMapper.selectList(chunkQuery);
        
        if (completedChunks.size() != record.getTotalChunks()) {
            throw new FileTransferException("文件分块未完全上传: " + completedChunks.size() + "/" + record.getTotalChunks());
        }
        
        try {
            // 合并分块文件
            mergeChunks(record, completedChunks);
            
            // 验证合并后的文件
            File mergedFile = new File(record.getFilePath());
            if (!mergedFile.exists()) {
                throw new IOException("合并后的文件不存在: " + record.getFilePath());
            }
            
            if (mergedFile.length() != record.getFileSize()) {
                throw new IOException("合并文件大小不匹配: expected=" + record.getFileSize() + ", actual=" + mergedFile.length());
            }
            
            // 验证文件MD5（使用extInfo中的原始MD5）
            String originalMd5 = extractOriginalMd5FromExtInfo(record.getExtInfo());
            if (StringUtils.hasText(originalMd5)) {
                String actualMd5 = FileUtils.calculateFileMD5(mergedFile);
                if (!actualMd5.equals(originalMd5)) {
                    log.error("文件MD5校验失败 - 传输ID: {}, 预期: {}, 实际: {}",
                            transferId, originalMd5, actualMd5);

                    // 标记传输为失败状态（3表示传输失败）
                    record.setStatus(3);
                    record.setFailReason("文件完整性校验失败：MD5不匹配");
                    transferRecordMapper.updateById(record);

                    // 清理损坏的文件
                    try {
                        deleteFileAndDirectory(mergedFile);
                        log.info("已清理MD5校验失败的损坏文件: {}", record.getFilePath());
                    } catch (Exception cleanupException) {
                        log.error("清理损坏文件失败: {}", record.getFilePath(), cleanupException);
                    }

                    // 抛出文件完整性异常
                    throw new FileIntegrityException(
                            String.format("文件完整性校验失败：预期MD5=%s，实际MD5=%s", originalMd5, actualMd5),
                            transferId, originalMd5, actualMd5);
                }

                log.info("文件MD5校验通过 - 传输ID: {}, MD5: {}", transferId, actualMd5);
            }

            // 移除文件权限保护机制 - 不再设置目录为只读权限
            // 文件合并完成后，目录保持默认权限，便于后续文件操作和维护
            log.debug("文件合并完成，目录保持默认权限: {}", mergedFile.getParent());

            // 更新传输记录状态
            record.setStatus(2); // 传输完成
            record.setTransferredSize(record.getFileSize());
            record.setCompletedChunks(record.getTotalChunks());
            record.setCompleteTime(Instant.now().toString());
            record.setUpdateTime(Instant.now().toString());

            transferRecordMapper.updateById(record);

            // 创建或更新info.json元数据文件
            try {
                // 使用用户配置的存储路径
                UserConfig userConfig = authService.getUserConfig(username);
                String storagePath = userConfig.getStoragePath();
                metadataService.createOrUpdateMetadata(record.getFileId(), record,
                                                       record.getOriginalFileName(), storagePath);
                log.debug("成功创建元数据文件 - fileId: {}, 原文件名: {}, 用户: {}",
                         record.getFileId(), record.getOriginalFileName(), username);
            } catch (Exception metadataException) {
                log.warn("创建元数据文件失败，但不影响上传完成 - fileId: {}, 用户: {}, 错误: {}",
                        record.getFileId(), username, metadataException.getMessage());
            }

            // 记录上传统计
            PerformanceMonitor.recordUpload(record.getFileSize(), true);

            log.info("文件上传完成 - 传输ID: {}, 文件: {}, 大小: {}", transferId, record.getFileName(), record.getFileSize());

            // 构建并返回完成响应
            return buildCompleteResponse(record, transferId, username, false);
            
        } catch (Exception e) {
            log.error("完成上传失败 - 传输ID: {}", transferId, e);
            
            // 更新为失败状态
            record.setStatus(3); // 传输失败
            record.setFailReason("完成上传失败: " + e.getMessage());
            record.setUpdateTime(Instant.now().toString());
            transferRecordMapper.updateById(record);

            // 记录上传失败统计
            PerformanceMonitor.recordUpload(record.getFileSize(), false);

            throw new FileTransferException("完成上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 下载文件
     */
    public void downloadFile(String fileId, String username, HttpServletResponse response) {
        // 使用带容错机制的查找方法
        FileTransferRecord record = findCompletedFileByMd5WithFallback(fileId, username);
        if (record == null) {
            throw new FileTransferException("文件不存在或无权限访问: " + fileId);
        }
        
        File file = new File(record.getFilePath());
        if (!file.exists()) {
            throw new FileTransferException("文件不存在: " + record.getFilePath());
        }
        
        try {
            // 获取用户配置
            UserConfig userConfig = authService.getUserConfig(username);
            
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setContentLengthLong(file.length());
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodeFileName(record.getFileName()) + "\"");
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
            
            // 应用下载限速并传输文件
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalRead = 0;
                long startTime = System.currentTimeMillis();
                
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                    totalRead += bytesRead;
                    
                    // 应用下载限速
                    if (userConfig.getRateLimitEnabled() && userConfig.getDownloadRateLimit() > 0) {
                        RateLimitUtils.applyRateLimit(username + "_download", userConfig.getDownloadRateLimit(), bytesRead);
                    }
                    
                    // 每传输1MB输出一次日志
                    if (totalRead % (1024 * 1024) == 0) {
                        log.debug("下载进度 - 用户: {}, 文件: {}, 已传输: {}/{}", 
                                username, record.getFileName(), totalRead, file.length());
                    }
                }
                
                os.flush();
                
                long duration = System.currentTimeMillis() - startTime;
                double speed = duration > 0 ? (double) totalRead / duration * 1000 : 0; // bytes/s
                
                // 记录下载统计
                PerformanceMonitor.recordDownload(totalRead, true);

                log.info("文件下载完成 - 用户: {}, 文件: {}, 大小: {}, 耗时: {}ms, 速度: {}/s",
                        username, record.getFileName(), totalRead, duration, FileUtils.formatFileSize((long) speed));
            }
            
        } catch (IOException e) {
            log.error("下载文件失败 - 文件ID: {}", fileId, e);

            // 记录下载失败统计
            PerformanceMonitor.recordDownload(file.length(), false);

            throw new FileTransferException("下载文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询传输进度
     */
    public TransferProgressResponse queryProgress(String transferId, String username) {
        FileTransferRecord record = transferRecordMapper.selectById(transferId);
        if (record == null) {
            throw new FileTransferException("传输记录不存在: " + transferId);
        }
        
        // 实时查询已完成的分块数
        QueryWrapper<FileChunkRecord> chunkQuery = new QueryWrapper<>();
        chunkQuery.eq("transfer_id", transferId).eq("status", 1);
        int completedChunks = chunkRecordMapper.selectCount(chunkQuery).intValue();
        
        // 计算实际进度
        double progress = record.getTotalChunks() > 0 ? 
            (double) completedChunks / record.getTotalChunks() * 100 : 0;
        
        long transferredSize = record.getTotalChunks() > 0 ? 
            (long) ((double) completedChunks / record.getTotalChunks() * record.getFileSize()) : 0;
        
        if (record.getStatus() == 2) {
            transferredSize = record.getFileSize();
            progress = 100.0;
            completedChunks = record.getTotalChunks();
        }
        
        TransferProgressResponse response = new TransferProgressResponse();
        response.setTransferId(transferId);
        response.setFileName(record.getFileName());
        response.setTotalSize(record.getFileSize());
        response.setTransferredSize(transferredSize);
        response.setProgress(progress);
        response.setTotalChunks(record.getTotalChunks());
        response.setCompletedChunks(completedChunks);
        response.setCompleted(record.getStatus() == 2);
        response.setStatus(record.getStatus());
        
        return response;
    }
    
    /**
     * 查找已完成的文件记录（按fileId和MD5匹配）
     * 增强的秒传机制：同时匹配fileId和MD5
     */
    private FileTransferRecord findCompletedFileByFileIdAndMd5(String fileId, String fileMd5, String username) {
        try {
            QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
            query.eq("file_id", fileId).eq("status", 2);

            List<FileTransferRecord> records = transferRecordMapper.selectList(query);

            // 进一步验证MD5匹配
            for (FileTransferRecord record : records) {
                if (record.getExtInfo() != null && record.getExtInfo().contains(fileMd5)) {
                    return record;
                }
            }

            return null;
        } catch (Exception e) {
            log.warn("按fileId和MD5查询失败 - fileId: {}, MD5: {}, 错误: {}", fileId, fileMd5, e.getMessage());
            return null;
        }
    }

    /**
     * 查找任意已完成的文件记录（按MD5）
     * 用于MD5匹配的秒传
     */
    private FileTransferRecord findAnyCompletedFileByMd5(String fileMd5, String username) {
        try {
            QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
            query.eq("status", 2);
            query.like("ext_info", fileMd5); // 在扩展信息中查找MD5

            List<FileTransferRecord> records = transferRecordMapper.selectList(query);

            return records.stream()
                .filter(record -> record.getFilePath() != null)
                .findFirst()
                .orElse(null);
        } catch (Exception e) {
            log.warn("按MD5查询失败 - MD5: {}, 错误: {}", fileMd5, e.getMessage());
            return null;
        }
    }

    /**
     * 根据fileId查找已完成的文件记录
     * 用于客户端指定fileId模式的冲突检测
     */
    private FileTransferRecord findCompletedFileByFileId(String fileId) {
        try {
            QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
            query.eq("file_id", fileId).eq("status", 2);

            List<FileTransferRecord> records = transferRecordMapper.selectList(query);
            return records.stream()
                .filter(record -> record.getFilePath() != null)
                .findFirst()
                .orElse(null);
        } catch (Exception e) {
            log.warn("按fileId查询失败 - fileId: {}, 错误: {}", fileId, e.getMessage());
            return null;
        }
    }

    /**
     * 删除损坏的文件记录和物理文件
     * 用于清理fileId冲突检测中发现的损坏文件
     */
    private void deleteCorruptedFile(FileTransferRecord record) {
        try {
            // 删除物理文件
            if (record.getFilePath() != null) {
                File file = new File(record.getFilePath());
                if (file.exists()) {
                    deleteFileAndDirectory(file);
                    log.info("已删除损坏的物理文件: {}", record.getFilePath());
                }
            }

            // 删除数据库记录
            transferRecordMapper.deleteById(record.getId());
            log.info("已删除损坏文件的数据库记录: fileId={}, recordId={}",
                record.getFileId(), record.getId());

        } catch (Exception e) {
            log.error("删除损坏文件失败 - fileId: {}, 路径: {}",
                record.getFileId(), record.getFilePath(), e);
            throw new FileTransferException("删除损坏文件失败: " + e.getMessage());
        }
    }

    /**
     * 从扩展信息中提取原始MD5值
     * 用于客户端指定fileId模式的MD5验证
     *
     * @param extInfo 扩展信息JSON字符串
     * @return 原始MD5值，如果提取失败返回null
     */
    private String extractOriginalMd5FromExtInfo(String extInfo) {
        if (extInfo == null || extInfo.trim().isEmpty()) {
            return null;
        }

        try {
            // 简单的JSON解析，提取originalMd5字段
            // 格式：{"originalMd5":"xxx","fileExtension":"yyy"}
            String pattern = "\"originalMd5\"\\s*:\\s*\"([^\"]+)\"";
            java.util.regex.Pattern regex = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher matcher = regex.matcher(extInfo);

            if (matcher.find()) {
                return matcher.group(1);
            }

            log.warn("无法从扩展信息中提取originalMd5: {}", extInfo);
            return null;

        } catch (Exception e) {
            log.error("解析扩展信息失败: {}", extInfo, e);
            return null;
        }
    }

    /**
     * 验证现有文件的完整性
     * 重新计算文件MD5并与预期值比较
     */
    private boolean verifyExistingFile(FileTransferRecord record, String expectedMd5) {
        if (record == null || record.getFilePath() == null) {
            return false;
        }

        File file = new File(record.getFilePath());
        if (!file.exists() || !file.isFile()) {
            log.warn("文件不存在，无法验证: {}", record.getFilePath());
            return false;
        }

        try {
            String actualMd5 = FileUtils.calculateFileMD5(file);
            boolean matches = expectedMd5.equals(actualMd5);

            if (!matches) {
                log.warn("文件MD5不匹配，可能已被外部修改 - 文件: {}, 预期: {}, 实际: {}",
                    record.getFilePath(), expectedMd5, actualMd5);

                // 文件已损坏，删除整个文件夹
                // 移除权限保护机制 - 直接删除文件，不需要临时权限管理
                try {
                    deleteFileAndDirectory(file);
                    log.info("已删除损坏的文件及其目录: {}", record.getFilePath());
                } catch (Exception e) {
                    log.error("删除损坏文件失败: {}", record.getFilePath(), e);
                }
            }

            return matches;

        } catch (Exception e) {
            log.error("验证文件MD5失败: {}", record.getFilePath(), e);
            return false;
        }
    }

    /**
     * 删除文件及其父目录（如果为空）
     */
    private void deleteFileAndDirectory(File file) throws IOException {
        if (file.exists()) {
            // 删除文件
            if (!file.delete()) {
                log.warn("删除文件失败: {}", file.getAbsolutePath());
            }
        }

        // 尝试删除父目录（如果为空）
        File parentDir = file.getParentFile();
        if (parentDir != null && parentDir.exists()) {
            String[] children = parentDir.list();
            if (children == null || children.length == 0) {
                if (parentDir.delete()) {
                    log.debug("删除空目录: {}", parentDir.getAbsolutePath());

                    // 递归删除上级空目录
                    File grandParent = parentDir.getParentFile();
                    if (grandParent != null && grandParent.exists()) {
                        String[] grandChildren = grandParent.list();
                        if (grandChildren == null || grandChildren.length == 0) {
                            if (grandParent.delete()) {
                                log.debug("删除空目录: {}", grandParent.getAbsolutePath());
                            }
                        }
                    }
                } else {
                    log.warn("删除空目录失败: {}", parentDir.getAbsolutePath());
                }
            }
        }
    }

    /**
     * 查找已完成的文件记录（按MD5和用户）
     */
    private FileTransferRecord findCompletedFileByMd5(String fileMd5, String username) {
        try {
            QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
            query.eq("file_id", fileMd5).eq("status", 2);

            List<FileTransferRecord> records = transferRecordMapper.selectList(query);

            // 使用新的文件路径规则，不再按用户过滤
            return records.stream()
                .filter(record -> record.getFilePath() != null)
                .findFirst()
                .orElse(null);
        } catch (Exception e) {
            log.warn("数据库查询失败，将尝试通过文件系统查找 - fileId: {}, 错误: {}", fileMd5, e.getMessage());
            return null;
        }
    }
    
    /**
     * 带容错机制的文件记录查找
     * 如果数据库查询失败或没有记录，尝试通过文件路径规则查找物理文件
     */
    private FileTransferRecord findCompletedFileByMd5WithFallback(String fileId, String username) {
        // 首先尝试从数据库查找
        FileTransferRecord record = findCompletedFileByMd5(fileId, username);
        
        if (record != null && checkFileExists(record.getFilePath())) {
            return record;
        }
        
        // 数据库查不到或文件不存在，尝试通过用户存储路径规则查找
        log.info("数据库查询无结果或文件不存在，尝试通过用户存储路径规则查找 - fileId: {}, 用户: {}", fileId, username);
        String filePath = tryFindFileByUserPathRule(fileId, username);
        
        if (filePath != null) {
            // 构建一个虚拟的记录对象用于返回
            File file = new File(filePath);
            FileTransferRecord virtualRecord = new FileTransferRecord();
            virtualRecord.setFileId(fileId);
            virtualRecord.setFileName(file.getName());
            virtualRecord.setFileSize(file.length());
            virtualRecord.setFilePath(filePath);
            virtualRecord.setStatus(2); // 标记为已完成
            virtualRecord.setCompleteTime(Instant.ofEpochMilli(file.lastModified()).toString());
            
            log.info("通过文件路径规则找到文件 - fileId: {}, 路径: {}", fileId, filePath);
            return virtualRecord;
        }
        
        return null;
    }
    
    /**
     * 根据用户配置的存储路径查找文件
     * 新规则：${user-storage-path}/YYYYMM/fileId（ULID）/md5.{后缀名}
     *
     * @param fileId 文件ID
     * @param username 用户名
     * @return 文件路径，如果未找到则返回null
     */
    private String tryFindFileByUserPathRule(String fileId, String username) {
        try {
            // 获取用户配置的存储路径
            UserConfig userConfig = authService.getUserConfig(username);
            String basePath = userConfig.getStoragePath();

            // 如果fileId是ULID格式，从中提取年月信息
            if (UlidUtils.isValidUlid(fileId)) {
                String yearMonth = UlidUtils.extractYearMonth(fileId);
                if (yearMonth != null) {
                    Path dirPath = Paths.get(basePath, yearMonth, fileId);

                    if (Files.exists(dirPath) && Files.isDirectory(dirPath)) {
                        // 查找目录下的文件
                        try (DirectoryStream<Path> stream = Files.newDirectoryStream(dirPath)) {
                            for (Path path : stream) {
                                if (Files.isRegularFile(path)) {
                                    log.info("通过用户存储规则找到文件 - fileId: {}, 用户: {}, 路径: {}",
                                            fileId, username, path);
                                    return path.toString();
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.warn("通过用户存储规则查找文件失败 - fileId: {}, 用户: {}, 错误: {}", fileId, username, e.getMessage());
        }

        return null;
    }



    /**
     * 重建SQLite数据库
     * 通过扫描多用户存储目录下的文件，重建数据库记录
     *
     * @return 重建统计信息
     */
    @Transactional
    public Map<String, Object> rebuildDatabase() {
        log.info("开始重建数据库 - 支持多用户存储路径扫描");

        // 备份当前数据库
        String backupPath = backupCurrentDatabase();

        Map<String, Object> result = new HashMap<>();
        result.put("backupPath", backupPath);

        try {
            log.info("开始多用户存储路径扫描重建");

            // 使用DatabaseFallbackService的多用户扫描重建功能
            Map<String, Object> scanResult = databaseFallbackService.scanAndRebuildFromMetadata();

            // 从扫描结果中提取统计信息
            int scannedFiles = (Integer) scanResult.getOrDefault("scannedFiles", 0);
            int rebuiltRecords = (Integer) scanResult.getOrDefault("rebuiltRecords", 0);
            int skippedFiles = (Integer) scanResult.getOrDefault("skippedFiles", 0);
            @SuppressWarnings("unchecked")
            List<String> errors = (List<String>) scanResult.getOrDefault("errors", new ArrayList<>());
            @SuppressWarnings("unchecked")
            Map<String, Object> userResults = (Map<String, Object>) scanResult.getOrDefault("userResults", new HashMap<>());

            log.info("多用户扫描重建完成 - 总扫描: {}, 总重建: {}, 总跳过: {}, 错误: {}",
                    scannedFiles, rebuiltRecords, skippedFiles, errors.size());

            // 构建详细的结果信息
            StringBuilder messageBuilder = new StringBuilder();
            messageBuilder.append(String.format("多用户数据库重建完成 - 总扫描: %d, 总重建: %d, 总跳过: %d",
                    scannedFiles, rebuiltRecords, skippedFiles));

            if (!userResults.isEmpty()) {
                messageBuilder.append("\n用户扫描详情:");
                for (Map.Entry<String, Object> entry : userResults.entrySet()) {
                    String userKey = entry.getKey();
                    @SuppressWarnings("unchecked")
                    Map<String, Object> userResult = (Map<String, Object>) entry.getValue();

                    int userScanned = (Integer) userResult.getOrDefault("scannedFiles", 0);
                    int userRebuilt = (Integer) userResult.getOrDefault("rebuiltRecords", 0);
                    int userSkipped = (Integer) userResult.getOrDefault("skippedFiles", 0);
                    String pathDesc = (String) userResult.getOrDefault("pathDescription", userKey);

                    messageBuilder.append(String.format("\n  - %s: 扫描=%d, 重建=%d, 跳过=%d",
                            pathDesc, userScanned, userRebuilt, userSkipped));
                }
            }

            if (!errors.isEmpty()) {
                messageBuilder.append(String.format("\n警告: %d 个错误", errors.size()));
            }

            result.put("success", true);
            result.put("scannedFiles", scannedFiles);
            result.put("rebuiltRecords", rebuiltRecords);
            result.put("skippedFiles", skippedFiles);
            result.put("errors", errors);
            result.put("userResults", userResults);
            result.put("message", messageBuilder.toString());

            log.info("数据库重建完成 - 总扫描: {}, 总重建: {}, 总跳过: {}", scannedFiles, rebuiltRecords, skippedFiles);

        } catch (Exception e) {
            log.error("重建数据库失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            throw new FileTransferException("重建数据库失败: " + e.getMessage());
        }

        return result;
    }
    
    /**
     * 备份当前数据库
     * 
     * @return 备份文件路径
     */
    private String backupCurrentDatabase() {
        try {
            String databasePath = properties.getDatabasePath();
            File databaseFile = new File(databasePath);
            
            if (!databaseFile.exists()) {
                log.info("数据库文件不存在，无需备份: {}", databasePath);
                return null;
            }
            
            String timestamp = Instant.now().toString().replaceAll("[:\\.]", "-");
            String backupPath = databasePath + ".backup." + timestamp;
            
            FileUtils.copyFile(databaseFile.getAbsolutePath(), backupPath);
            log.info("数据库备份完成 - 原文件: {}, 备份文件: {}", databasePath, backupPath);
            
            return backupPath;
            
        } catch (Exception e) {
            log.error("备份数据库失败", e);
            throw new FileTransferException("备份数据库失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新传输进度
     */
    private void updateTransferProgress(String transferId) {
        QueryWrapper<FileChunkRecord> chunkQuery = new QueryWrapper<>();
        chunkQuery.eq("transfer_id", transferId).eq("status", 1);
        int completedChunks = chunkRecordMapper.selectCount(chunkQuery).intValue();
        
        FileTransferRecord record = new FileTransferRecord();
        record.setId(transferId);
        record.setCompletedChunks(completedChunks);
        record.setUpdateTime(Instant.now().toString());
        
        transferRecordMapper.updateById(record);
    }
    
    /**
     * 合并分块文件
     */
    private void mergeChunks(FileTransferRecord record, List<FileChunkRecord> chunks) throws IOException {
        // 按分块序号排序
        chunks.sort((a, b) -> a.getChunkIndex().compareTo(b.getChunkIndex()));
        
        try (FileOutputStream fos = new FileOutputStream(record.getFilePath())) {
            for (FileChunkRecord chunk : chunks) {
                File chunkFile = new File(chunk.getChunkPath());
                if (!chunkFile.exists()) {
                    throw new IOException("分块文件不存在: " + chunk.getChunkPath());
                }
                
                try (FileInputStream fis = new FileInputStream(chunkFile)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = fis.read(buffer)) != -1) {
                        fos.write(buffer, 0, bytesRead);
                    }
                }
                
                // 删除分块文件 - 移除权限保护机制，直接删除
                if (!chunkFile.delete()) {
                    log.warn("删除分块文件失败: {}", chunk.getChunkPath());
                }
            }
            
            fos.flush();
        }
        
        log.info("文件合并完成 - 传输ID: {}, 分块数: {}", record.getId(), chunks.size());
    }
    
    /**
     * 根据用户配置构建新的文件路径
     * 新的存储规则：${user-storage-path}/YYYYMM/fileId（ULID）/md5.{后缀名}
     *
     * @param fileId 文件ID
     * @param fileName 文件名
     * @param username 用户名
     * @return 完整的文件存储路径
     */
    private String buildNewFilePathForUser(String fileId, String fileName, String username) {
        // 获取用户配置的存储路径
        UserConfig userConfig = authService.getUserConfig(username);
        String basePath = userConfig.getStoragePath();

        // 从ULID提取年月信息
        String yearMonth = UlidUtils.extractYearMonth(fileId);
        if (yearMonth == null) {
            log.warn("无法从fileId提取年月信息，使用当前年月: {}", fileId);
            yearMonth = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMM"));
        }

        // 构建路径: user-storage-path/YYYYMM/fileId/fileName
        String safeName = sanitizeFileName(fileName);
        String fullPath = Paths.get(basePath, yearMonth, fileId, safeName).toString();

        log.debug("为用户 {} 构建文件路径: {}", username, fullPath);
        return fullPath;
    }


    
    /**
     * 构建分块文件路径
     */
    private String buildChunkPath(String filePath, int chunkIndex) {
        return filePath + ".chunk." + String.format("%06d", chunkIndex);
    }
    
    /**
     * 清理文件名中的非法字符
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "unnamed";
        }
        // 移除或替换文件名中的非法字符
        return fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
    }
    
    /**
     * 编码文件名用于HTTP响应
     */
    private String encodeFileName(String fileName) {
        try {
            return java.net.URLEncoder.encode(fileName, "UTF-8")
                    .replaceAll("\\+", "%20"); // 空格用%20而不是+
        } catch (Exception e) {
            return fileName;
        }
    }
    
    /**
     * 确保目录存在
     */
    private void ensureDirectoryExists(String filePath) {
        Path parentDir = Paths.get(filePath).getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            try {
                Files.createDirectories(parentDir);
                log.debug("创建目录: {}", parentDir);
            } catch (IOException e) {
                throw new RuntimeException("创建目录失败: " + parentDir, e);
            }
        }
    }
    
    /**
     * 检查文件是否存在
     */
    private boolean checkFileExists(String filePath) {
        return filePath != null && Files.exists(Paths.get(filePath));
    }
    
    /**
     * 获取文件详细信息
     * 支持数据库故障时的回退机制
     * 
     * @param fileId 文件ID（ULID）
     * @param username 用户名
     * @return 文件信息
     */
    public FileInfo getFileInfo(String fileId, String username) {
        try {
            // 首先检查数据库是否健康
            if (databaseFallbackService.isDatabaseHealthy()) {
                // 数据库正常，使用数据库查询
                FileTransferRecord record = findCompletedFileByMd5WithFallback(fileId, username);
                if (record != null) {
                    return buildFileInfoFromRecord(record, username);
                }
                
                log.debug("数据库中未找到文件记录，尝试回退机制 - fileId: {}", fileId);
            } else {
                log.warn("数据库不健康，直接使用回退机制 - fileId: {}", fileId);
            }
            
            // 数据库故障或未找到记录，使用回退机制
            FileInfo fallbackFileInfo = databaseFallbackService.findFileByIdFallback(fileId, username);
            if (fallbackFileInfo != null) {
                log.info("使用回退机制成功找到文件 - fileId: {}, 文件名: {}", 
                        fileId, fallbackFileInfo.getFileName());
                return fallbackFileInfo;
            }
            
            throw new FileTransferException("文件不存在或无权限访问: " + fileId);
            
        } catch (FileTransferException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取文件信息失败 - fileId: {}, 用户: {}", fileId, username, e);
            throw new FileTransferException("获取文件信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 基于数据库记录构建文件信息
     * 
     * @param record 文件传输记录
     * @param username 用户名
     * @return 文件信息
     */
    private FileInfo buildFileInfoFromRecord(FileTransferRecord record, String username) {
        // 检查物理文件是否存在
        if (!checkFileExists(record.getFilePath())) {
            throw new FileTransferException("文件物理存储不存在: " + record.getFileId());
        }
        
        // 获取用户配置以计算相对路径
        UserConfig userConfig = authService.getUserConfig(username);
        String userStoragePath = userConfig.getStoragePath();
        
        // 构建文件信息
        FileInfo fileInfo = new FileInfo();
        fileInfo.setFileId(record.getFileId());
        
        // 优先使用原始文件名，如果没有则使用物理文件名
        String displayFileName = record.getOriginalFileName() != null ? 
                                record.getOriginalFileName() : record.getFileName();
        fileInfo.setFileName(displayFileName);
        
        fileInfo.setFileSize(record.getFileSize());
        fileInfo.setFileType(detectFileType(displayFileName));
        fileInfo.setUploadTime(record.getCompleteTime());
        
        // 计算相对路径（相对于用户存储根目录）
        String relativePath = calculateRelativePath(record.getFilePath(), userStoragePath);
        fileInfo.setRelativePath(relativePath);
        
        // 获取文件的额外信息
        File file = new File(record.getFilePath());
        if (file.exists()) {
            fileInfo.setLastModified(file.lastModified());
            fileInfo.setCanRead(file.canRead());
            fileInfo.setCanWrite(file.canWrite());
        }
        
        return fileInfo;
    }
    

    
    /**
     * 分块下载文件
     * 支持HTTP Range请求，实现断点续传
     * 
     * @param fileId 文件ID
     * @param username 用户名
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     */
    public void downloadFileChunk(String fileId, String username, HttpServletRequest request, HttpServletResponse response) {
        // 使用带容错机制的查找方法
        FileTransferRecord record = findCompletedFileByMd5WithFallback(fileId, username);
        if (record == null) {
            throw new FileTransferException("文件不存在或无权限访问: " + fileId);
        }
        
        File file = new File(record.getFilePath());
        if (!file.exists()) {
            throw new FileTransferException("文件不存在: " + record.getFilePath());
        }
        
        try {
            // 获取用户配置
            UserConfig userConfig = authService.getUserConfig(username);
            
            long fileLength = file.length();
            long start = 0;
            long end = fileLength - 1;
            
            // 解析Range请求头
            String rangeHeader = request.getHeader("Range");
            if (rangeHeader != null && rangeHeader.startsWith("bytes=")) {
                String[] ranges = rangeHeader.substring("bytes=".length()).split("-");
                try {
                    if (ranges.length >= 1 && !ranges[0].isEmpty()) {
                        start = Long.parseLong(ranges[0]);
                    }
                    if (ranges.length >= 2 && !ranges[1].isEmpty()) {
                        end = Long.parseLong(ranges[1]);
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的Range请求头: {}", rangeHeader);
                    // 忽略无效的Range，使用完整文件
                }
            }
            
            // 验证范围
            if (start > end || start >= fileLength) {
                response.setStatus(HttpServletResponse.SC_REQUESTED_RANGE_NOT_SATISFIABLE);
                response.setHeader("Content-Range", "bytes */" + fileLength);
                return;
            }
            
            // 调整end值
            if (end >= fileLength) {
                end = fileLength - 1;
            }
            
            long contentLength = end - start + 1;
            
            // 设置响应头
            if (rangeHeader != null) {
                response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
                response.setHeader("Content-Range", "bytes " + start + "-" + end + "/" + fileLength);
                response.setHeader("Accept-Ranges", "bytes");
            } else {
                response.setStatus(HttpServletResponse.SC_OK);
            }
            
            response.setContentType("application/octet-stream");
            response.setContentLengthLong(contentLength);
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodeFileName(record.getFileName()) + "\"");
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
            
            // 分块传输文件
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                
                // 跳转到起始位置
                if (start > 0) {
                    fis.skip(start);
                }
                
                byte[] buffer = new byte[8192];
                long totalRead = 0;
                long startTime = System.currentTimeMillis();
                
                while (totalRead < contentLength) {
                    int maxRead = (int) Math.min(buffer.length, contentLength - totalRead);
                    int bytesRead = fis.read(buffer, 0, maxRead);
                    
                    if (bytesRead == -1) {
                        break;
                    }
                    
                    os.write(buffer, 0, bytesRead);
                    totalRead += bytesRead;
                    
                    // 应用下载限速
                    if (userConfig.getRateLimitEnabled() && userConfig.getDownloadRateLimit() > 0) {
                        RateLimitUtils.applyRateLimit(username + "_download", userConfig.getDownloadRateLimit(), bytesRead);
                    }
                    
                    // 每传输1MB输出一次日志
                    if (totalRead % (1024 * 1024) == 0) {
                        log.debug("分块下载进度 - 用户: {}, 文件: {}, 已传输: {}/{}", 
                                username, record.getFileName(), totalRead, contentLength);
                    }
                }
                
                os.flush();
                
                long duration = System.currentTimeMillis() - startTime;
                double speed = duration > 0 ? (double) totalRead / duration * 1000 : 0; // bytes/s
                
                log.info("分块下载完成 - 用户: {}, 文件: {}, 范围: {}-{}, 大小: {}, 耗时: {}ms, 速度: {}/s", 
                        username, record.getFileName(), start, end, totalRead, duration, FileUtils.formatFileSize((long) speed));
            }
            
        } catch (IOException e) {
            log.error("分块下载文件失败 - 文件ID: {}", fileId, e);
            throw new FileTransferException("分块下载文件失败: " + e.getMessage());
        }
    }
    

    
    /**
     * 计算相对路径
     *
     * @param absolutePath 绝对路径
     * @param basePath 基础路径
     * @return 相对路径
     */
    private String calculateRelativePath(String absolutePath, String basePath) {
        try {
            // 使用File.separator确保跨平台兼容性
            Path absolute = Paths.get(absolutePath).normalize();
            Path base = Paths.get(basePath).normalize();
            Path relative = base.relativize(absolute);

            // 统一使用正斜杠作为路径分隔符，确保跨平台兼容性
            // 这样在Windows和Linux系统中都能正确处理路径
            return relative.toString().replace(File.separator, "/");
        } catch (Exception e) {
            log.warn("计算相对路径失败 - 绝对路径: {}, 基础路径: {}", absolutePath, basePath, e);
            return absolutePath; // 返回原路径
        }
    }
    
    /**
     * 检测文件类型
     * 
     * @param fileName 文件名
     * @return 文件类型
     */
    private String detectFileType(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "unknown";
        }
        
        String extension = "";
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            extension = fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        
        // 常见文件类型映射
        switch (extension) {
            case "txt":
            case "md":
            case "log":
                return "text";
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
            case "webp":
                return "image";
            case "mp4":
            case "avi":
            case "mkv":
            case "mov":
            case "wmv":
                return "video";
            case "mp3":
            case "wav":
            case "flac":
            case "aac":
                return "audio";
            case "pdf":
                return "pdf";
            case "doc":
            case "docx":
                return "document";
            case "xls":
            case "xlsx":
                return "spreadsheet";
            case "ppt":
            case "pptx":
                return "presentation";
            case "zip":
            case "rar":
            case "7z":
            case "tar":
            case "gz":
                return "archive";
            case "java":
            case "js":
            case "py":
            case "cpp":
            case "c":
            case "php":
                return "code";
            default:
                return "unknown";
        }
    }

    /**
     * 构建文件上传完成响应
     */
    private FileUploadCompleteResponse buildCompleteResponse(FileTransferRecord record, String transferId, String username, boolean fastUpload) {
        FileUploadCompleteResponse response = new FileUploadCompleteResponse();
        response.setTransferId(transferId);
        response.setFileId(record.getFileId());
        response.setFileName(record.getFileName());
        response.setFileSize(record.getFileSize());
        response.setCompleteTime(record.getCompleteTime());
        response.setFastUpload(fastUpload);

        // 从extInfo中提取原始MD5
        String originalMd5 = extractOriginalMd5FromExtInfo(record.getExtInfo());
        response.setFileMd5(originalMd5);

        // 计算相对路径（使用用户配置的存储路径）
        UserConfig userConfig = authService.getUserConfig(username);
        String basePath = userConfig.getStoragePath();
        String relativePath = calculateRelativePath(record.getFilePath(), basePath);
        response.setRelativePath(relativePath);

        // 计算传输耗时
        if (record.getCreateTime() != null && record.getCompleteTime() != null) {
            try {
                Instant createTime = Instant.parse(record.getCreateTime());
                Instant completeTime = Instant.parse(record.getCompleteTime());
                long duration = completeTime.toEpochMilli() - createTime.toEpochMilli();
                response.setTransferDuration(duration);
            } catch (Exception e) {
                log.warn("计算传输耗时失败: {}", e.getMessage());
            }
        }

        return response;
    }

}