package com.sdesrd.filetransfer.server.standalone;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;

/**
 * 文件传输服务端独立应用
 * 
 * 这是一个独立的文件传输服务端应用，可以作为单独的进程运行
 * 通过引入file-transfer-server-sdk提供完整的文件传输服务功能
 */
@SpringBootApplication
@ConfigurationPropertiesScan
public class FileTransferServerApplication {

    public static void main(String[] args) {
        // 设置默认配置文件
        System.setProperty("spring.config.name", "file-transfer-server");
        
        SpringApplication app = new SpringApplication(FileTransferServerApplication.class);
        
        // 设置默认profile
        app.setAdditionalProfiles("server");
        
        app.run(args);
        
        System.out.println("===========================================");
        System.out.println("文件传输服务端启动成功！");
        System.out.println("API文档地址: http://localhost:49011/doc.html");
        System.out.println("健康检查: http://localhost:49011/actuator/health");
        System.out.println("服务端点: http://localhost:49011/file-transfer");
        System.out.println("===========================================");
    }
} 