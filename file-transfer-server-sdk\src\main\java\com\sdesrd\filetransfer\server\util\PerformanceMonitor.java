package com.sdesrd.filetransfer.server.util;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;
import java.lang.management.ThreadMXBean;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import lombok.extern.slf4j.Slf4j;

/**
 * 性能监控器
 * 监控系统性能指标和传输统计
 */
@Slf4j
public class PerformanceMonitor {
    
    /** 监控间隔（秒） */
    private static final int MONITOR_INTERVAL_SECONDS = 30;
    
    /** 性能指标保留时间（毫秒） */
    private static final long METRICS_RETENTION_MS = 3600000L; // 1小时
    
    /** 传输统计 */
    private static final AtomicLong TOTAL_UPLOAD_COUNT = new AtomicLong(0);
    private static final AtomicLong TOTAL_DOWNLOAD_COUNT = new AtomicLong(0);
    private static final AtomicLong TOTAL_UPLOAD_BYTES = new AtomicLong(0);
    private static final AtomicLong TOTAL_DOWNLOAD_BYTES = new AtomicLong(0);
    private static final AtomicLong SUCCESSFUL_TRANSFERS = new AtomicLong(0);
    private static final AtomicLong FAILED_TRANSFERS = new AtomicLong(0);
    
    /** 性能指标历史记录 */
    private static final ConcurrentHashMap<Long, PerformanceMetrics> METRICS_HISTORY = new ConcurrentHashMap<>();
    
    /** 定时任务执行器 */
    private static ScheduledExecutorService scheduler;
    
    /** 是否已启动监控 */
    private static volatile boolean monitoringStarted = false;
    
    /**
     * 启动性能监控
     */
    public static synchronized void startMonitoring() {
        if (monitoringStarted) {
            return;
        }
        
        scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "PerformanceMonitor");
            t.setDaemon(true);
            return t;
        });
        
        // 定期收集性能指标
        scheduler.scheduleAtFixedRate(() -> {
            try {
                collectMetrics();
                cleanupOldMetrics();
            } catch (Exception e) {
                log.error("收集性能指标失败", e);
            }
        }, 0, MONITOR_INTERVAL_SECONDS, TimeUnit.SECONDS);
        
        monitoringStarted = true;
        log.info("性能监控已启动，监控间隔: {}秒", MONITOR_INTERVAL_SECONDS);
    }
    
    /**
     * 停止性能监控
     */
    public static synchronized void stopMonitoring() {
        if (!monitoringStarted) {
            return;
        }
        
        if (scheduler != null) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        monitoringStarted = false;
        log.info("性能监控已停止");
    }
    
    /**
     * 记录上传统计
     */
    public static void recordUpload(long bytes, boolean success) {
        TOTAL_UPLOAD_COUNT.incrementAndGet();
        TOTAL_UPLOAD_BYTES.addAndGet(bytes);
        
        if (success) {
            SUCCESSFUL_TRANSFERS.incrementAndGet();
        } else {
            FAILED_TRANSFERS.incrementAndGet();
        }
    }
    
    /**
     * 记录下载统计
     */
    public static void recordDownload(long bytes, boolean success) {
        TOTAL_DOWNLOAD_COUNT.incrementAndGet();
        TOTAL_DOWNLOAD_BYTES.addAndGet(bytes);
        
        if (success) {
            SUCCESSFUL_TRANSFERS.incrementAndGet();
        } else {
            FAILED_TRANSFERS.incrementAndGet();
        }
    }
    
    /**
     * 获取当前性能指标
     */
    public static PerformanceMetrics getCurrentMetrics() {
        return collectMetrics();
    }
    
    /**
     * 获取传输统计
     */
    public static TransferStatistics getTransferStatistics() {
        long totalTransfers = TOTAL_UPLOAD_COUNT.get() + TOTAL_DOWNLOAD_COUNT.get();
        double successRate = totalTransfers > 0 ? 
                (double) SUCCESSFUL_TRANSFERS.get() / totalTransfers * 100 : 0.0;
        
        return new TransferStatistics(
                TOTAL_UPLOAD_COUNT.get(),
                TOTAL_DOWNLOAD_COUNT.get(),
                TOTAL_UPLOAD_BYTES.get(),
                TOTAL_DOWNLOAD_BYTES.get(),
                SUCCESSFUL_TRANSFERS.get(),
                FAILED_TRANSFERS.get(),
                successRate
        );
    }
    
    /**
     * 收集性能指标
     */
    private static PerformanceMetrics collectMetrics() {
        long timestamp = System.currentTimeMillis();
        
        // JVM内存信息
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapMemory = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapMemory = memoryBean.getNonHeapMemoryUsage();
        
        // 系统信息
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        
        // 创建性能指标对象
        PerformanceMetrics metrics = new PerformanceMetrics(
                timestamp,
                heapMemory.getUsed(),
                heapMemory.getMax(),
                nonHeapMemory.getUsed(),
                nonHeapMemory.getMax(),
                osBean.getAvailableProcessors(),
                runtimeBean.getUptime(),
                threadBean.getThreadCount(),
                threadBean.getDaemonThreadCount(),
                RateLimitUtils.getRateLimiterCount()
        );
        
        // 保存到历史记录
        METRICS_HISTORY.put(timestamp, metrics);
        
        return metrics;
    }
    
    /**
     * 清理过期的性能指标
     */
    private static void cleanupOldMetrics() {
        long cutoffTime = System.currentTimeMillis() - METRICS_RETENTION_MS;
        METRICS_HISTORY.entrySet().removeIf(entry -> entry.getKey() < cutoffTime);
    }
    
    /**
     * 性能指标数据类
     */
    public static class PerformanceMetrics {
        private final long timestamp;
        private final long heapMemoryUsed;
        private final long heapMemoryMax;
        private final long nonHeapMemoryUsed;
        private final long nonHeapMemoryMax;
        private final int availableProcessors;
        private final long jvmUptime;
        private final int threadCount;
        private final int daemonThreadCount;
        private final int rateLimiterCount;
        
        public PerformanceMetrics(long timestamp, long heapMemoryUsed, long heapMemoryMax,
                                long nonHeapMemoryUsed, long nonHeapMemoryMax, int availableProcessors,
                                long jvmUptime, int threadCount, int daemonThreadCount, int rateLimiterCount) {
            this.timestamp = timestamp;
            this.heapMemoryUsed = heapMemoryUsed;
            this.heapMemoryMax = heapMemoryMax;
            this.nonHeapMemoryUsed = nonHeapMemoryUsed;
            this.nonHeapMemoryMax = nonHeapMemoryMax;
            this.availableProcessors = availableProcessors;
            this.jvmUptime = jvmUptime;
            this.threadCount = threadCount;
            this.daemonThreadCount = daemonThreadCount;
            this.rateLimiterCount = rateLimiterCount;
        }
        
        // Getters
        public long getTimestamp() { return timestamp; }
        public long getHeapMemoryUsed() { return heapMemoryUsed; }
        public long getHeapMemoryMax() { return heapMemoryMax; }
        public long getNonHeapMemoryUsed() { return nonHeapMemoryUsed; }
        public long getNonHeapMemoryMax() { return nonHeapMemoryMax; }
        public int getAvailableProcessors() { return availableProcessors; }
        public long getJvmUptime() { return jvmUptime; }
        public int getThreadCount() { return threadCount; }
        public int getDaemonThreadCount() { return daemonThreadCount; }
        public int getRateLimiterCount() { return rateLimiterCount; }
        
        public double getHeapMemoryUsagePercent() {
            return heapMemoryMax > 0 ? (double) heapMemoryUsed / heapMemoryMax * 100 : 0.0;
        }
        
        public double getNonHeapMemoryUsagePercent() {
            return nonHeapMemoryMax > 0 ? (double) nonHeapMemoryUsed / nonHeapMemoryMax * 100 : 0.0;
        }
        
        @Override
        public String toString() {
            return String.format("性能指标 - 堆内存: %s/%s (%.1f%%), 非堆内存: %s/%s (%.1f%%), " +
                            "CPU核心: %d, JVM运行时间: %s, 线程数: %d, 限流器数: %d",
                    formatBytes(heapMemoryUsed), formatBytes(heapMemoryMax), getHeapMemoryUsagePercent(),
                    formatBytes(nonHeapMemoryUsed), formatBytes(nonHeapMemoryMax), getNonHeapMemoryUsagePercent(),
                    availableProcessors, formatDuration(jvmUptime), threadCount, rateLimiterCount);
        }
        
        private String formatBytes(long bytes) {
            return com.sdesrd.filetransfer.server.util.FileUtils.formatFileSize(bytes);
        }
        
        private String formatDuration(long millis) {
            long seconds = millis / 1000;
            long minutes = seconds / 60;
            long hours = minutes / 60;
            long days = hours / 24;
            
            if (days > 0) {
                return String.format("%d天%d小时", days, hours % 24);
            } else if (hours > 0) {
                return String.format("%d小时%d分钟", hours, minutes % 60);
            } else if (minutes > 0) {
                return String.format("%d分钟%d秒", minutes, seconds % 60);
            } else {
                return String.format("%d秒", seconds);
            }
        }
    }
    
    /**
     * 传输统计数据类
     */
    public static class TransferStatistics {
        private final long totalUploads;
        private final long totalDownloads;
        private final long totalUploadBytes;
        private final long totalDownloadBytes;
        private final long successfulTransfers;
        private final long failedTransfers;
        private final double successRate;
        
        public TransferStatistics(long totalUploads, long totalDownloads, long totalUploadBytes,
                                long totalDownloadBytes, long successfulTransfers, long failedTransfers,
                                double successRate) {
            this.totalUploads = totalUploads;
            this.totalDownloads = totalDownloads;
            this.totalUploadBytes = totalUploadBytes;
            this.totalDownloadBytes = totalDownloadBytes;
            this.successfulTransfers = successfulTransfers;
            this.failedTransfers = failedTransfers;
            this.successRate = successRate;
        }
        
        // Getters
        public long getTotalUploads() { return totalUploads; }
        public long getTotalDownloads() { return totalDownloads; }
        public long getTotalUploadBytes() { return totalUploadBytes; }
        public long getTotalDownloadBytes() { return totalDownloadBytes; }
        public long getSuccessfulTransfers() { return successfulTransfers; }
        public long getFailedTransfers() { return failedTransfers; }
        public double getSuccessRate() { return successRate; }
        public long getTotalTransfers() { return totalUploads + totalDownloads; }
        public long getTotalBytes() { return totalUploadBytes + totalDownloadBytes; }
        
        @Override
        public String toString() {
            return String.format("传输统计 - 总上传: %d (%s), 总下载: %d (%s), " +
                            "成功: %d, 失败: %d, 成功率: %.2f%%",
                    totalUploads, com.sdesrd.filetransfer.server.util.FileUtils.formatFileSize(totalUploadBytes),
                    totalDownloads, com.sdesrd.filetransfer.server.util.FileUtils.formatFileSize(totalDownloadBytes),
                    successfulTransfers, failedTransfers, successRate);
        }
    }
}
