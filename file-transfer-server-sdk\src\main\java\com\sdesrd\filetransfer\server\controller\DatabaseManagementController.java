package com.sdesrd.filetransfer.server.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sdesrd.filetransfer.server.dto.ApiResult;
import com.sdesrd.filetransfer.server.interceptor.AuthInterceptor;
import com.sdesrd.filetransfer.server.service.DatabaseManagementService;
import com.sdesrd.filetransfer.server.service.DatabaseManagementService.BackupFileInfo;
import com.sdesrd.filetransfer.server.service.DatabaseManagementService.DatabaseHealthInfo;
import com.sdesrd.filetransfer.server.service.DatabaseManagementService.DatabaseRebuildResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库管理控制器
 * 提供数据库备份、恢复、重建和健康检查功能
 *
 * 注意：路径映射已移除"/filetransfer"前缀，现在通过服务端context-path统一配置
 * 最终API端点格式：/filetransfer/api/database/* （通过context-path + @RequestMapping组合）
 */
@Slf4j
@RestController
@RequestMapping("/api/database")
public class DatabaseManagementController {
    
    @Autowired
    private DatabaseManagementService databaseManagementService;
    
    /**
     * 检查数据库健康状态
     */
    @GetMapping("/health")
    public ApiResult<DatabaseHealthInfo> checkHealth(HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);
            
            log.info("检查数据库健康状态 - 用户: {}, IP: {}", currentUser, clientIp);
            
            DatabaseHealthInfo healthInfo = databaseManagementService.checkDatabaseHealth();
            return ApiResult.success("数据库健康检查完成", healthInfo);
            
        } catch (Exception e) {
            log.error("数据库健康检查失败", e);
            return ApiResult.error("数据库健康检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建数据库备份
     */
    @PostMapping("/backup")
    public ApiResult<String> createBackup(HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);
            
            log.info("创建数据库备份 - 用户: {}, IP: {}", currentUser, clientIp);
            
            String backupPath = databaseManagementService.createBackup();
            return ApiResult.success("数据库备份创建成功", backupPath);
            
        } catch (Exception e) {
            log.error("创建数据库备份失败", e);
            return ApiResult.error("创建数据库备份失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有备份文件列表
     */
    @GetMapping("/backups")
    public ApiResult<List<BackupFileInfo>> listBackups(HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);
            
            log.info("获取备份文件列表 - 用户: {}, IP: {}", currentUser, clientIp);
            
            List<BackupFileInfo> backups = databaseManagementService.listBackups();
            return ApiResult.success("获取备份文件列表成功", backups);
            
        } catch (Exception e) {
            log.error("获取备份文件列表失败", e);
            return ApiResult.error("获取备份文件列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 下载备份文件
     */
    @GetMapping("/backup/download/{fileName}")
    public void downloadBackup(
            @PathVariable("fileName") String fileName,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);
            
            log.info("下载备份文件 - 用户: {}, 文件: {}, IP: {}", currentUser, fileName, clientIp);
            
            File backupFile = databaseManagementService.getBackupFile(fileName);
            
            // 设置响应头
            httpResponse.setContentType("application/octet-stream");
            httpResponse.setContentLengthLong(backupFile.length());
            httpResponse.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            httpResponse.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            httpResponse.setHeader("Pragma", "no-cache");
            httpResponse.setHeader("Expires", "0");
            
            // 传输文件
            try (FileInputStream fis = new FileInputStream(backupFile);
                 OutputStream os = httpResponse.getOutputStream()) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalRead = 0;
                
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                    totalRead += bytesRead;
                }
                
                os.flush();
                
                log.info("备份文件下载完成 - 用户: {}, 文件: {}, 大小: {}", 
                    currentUser, fileName, totalRead);
            }
            
        } catch (Exception e) {
            log.error("下载备份文件失败 - 文件: {}", fileName, e);
            try {
                httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                httpResponse.setContentType("application/json;charset=UTF-8");
                httpResponse.getWriter().write("{\"code\":500,\"message\":\"下载备份文件失败: " + e.getMessage() + "\",\"data\":null}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }
    
    /**
     * 通过磁盘扫描重建数据库
     */
    @PostMapping("/rebuild")
    public ApiResult<DatabaseRebuildResult> rebuildDatabase(HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);
            
            log.info("开始重建数据库 - 用户: {}, IP: {}", currentUser, clientIp);

            DatabaseRebuildResult result = databaseManagementService.rebuildDatabaseFromDisk(currentUser, clientIp);
            
            if (result.isSuccess()) {
                return ApiResult.success("数据库重建成功", result);
            } else {
                return ApiResult.<DatabaseRebuildResult>error("数据库重建失败: " + result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("重建数据库失败", e);
            return ApiResult.error("重建数据库失败: " + e.getMessage());
        }
    }

    /**
     * 查询数据库重建状态
     */
    @GetMapping("/rebuild/status")
    public ApiResult<Map<String, Object>> getRebuildStatus(HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);

            log.debug("查询数据库重建状态 - 用户: {}, IP: {}", currentUser, clientIp);

            Map<String, Object> status = databaseManagementService.getRebuildStatus();
            return ApiResult.success("查询重建状态成功", status);

        } catch (Exception e) {
            log.error("查询数据库重建状态失败", e);
            return ApiResult.error("查询重建状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
