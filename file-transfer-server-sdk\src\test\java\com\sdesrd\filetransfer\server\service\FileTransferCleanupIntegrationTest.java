package com.sdesrd.filetransfer.server.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sdesrd.filetransfer.server.config.CleanupConstants;
import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.entity.FileChunkRecord;
import com.sdesrd.filetransfer.server.entity.FileTransferRecord;
import com.sdesrd.filetransfer.server.mapper.FileChunkRecordMapper;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;
import com.sdesrd.filetransfer.server.service.CleanupStatisticsService.CleanupOperationInfo;
import com.sdesrd.filetransfer.server.service.CleanupStatisticsService.CleanupStatistics;
import com.sdesrd.filetransfer.server.service.FileTransferMonitorService.TransferStatistics;

/**
 * 文件传输清理功能集成测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("文件传输清理功能集成测试")
class FileTransferCleanupIntegrationTest {

    @Mock
    private FileTransferRecordMapper transferRecordMapper;

    @Mock
    private FileChunkRecordMapper chunkRecordMapper;

    @Mock
    private FileTransferProperties properties;

    @Mock
    private CleanupStatisticsService cleanupStatisticsService;

    @InjectMocks
    private FileTransferMonitorService monitorService;

    @BeforeEach
    void setUp() {
        // 配置默认的属性值
        when(properties.isCleanupEnabled()).thenReturn(true);
        when(properties.getCleanupInterval()).thenReturn(CleanupConstants.DEFAULT_CLEANUP_INTERVAL_MS);
        when(properties.getRecordExpireTime()).thenReturn(CleanupConstants.DEFAULT_RECORD_EXPIRE_TIME_MS);
        when(properties.getChunkExpireTime()).thenReturn(CleanupConstants.DEFAULT_CHUNK_EXPIRE_TIME_MS);
        when(properties.getFailedRecordRetainTime()).thenReturn(CleanupConstants.DEFAULT_FAILED_RECORD_RETAIN_TIME_MS);
        when(properties.isDeletePhysicalFiles()).thenReturn(false);
        when(properties.getMaxBatchDeleteSize()).thenReturn(CleanupConstants.MAX_BATCH_DELETE_SIZE);
        when(properties.getCleanupIntervalSeconds()).thenReturn(CleanupConstants.DEFAULT_CLEANUP_INTERVAL_MS / 1000);
        when(properties.getRecordExpireTimeSeconds()).thenReturn(CleanupConstants.DEFAULT_RECORD_EXPIRE_TIME_MS / 1000);
        when(properties.getChunkExpireTimeSeconds()).thenReturn(CleanupConstants.DEFAULT_CHUNK_EXPIRE_TIME_MS / 1000);
        when(properties.getFailedRecordRetainTimeSeconds()).thenReturn(CleanupConstants.DEFAULT_FAILED_RECORD_RETAIN_TIME_MS / 1000);
    }

    @Test
    @DisplayName("测试配置验证 - 有效配置")
    void testConfigValidation_ValidConfig() {
        // 配置验证应该通过，不抛出异常
        assertDoesNotThrow(() -> {
            monitorService.init();
        });
    }

    @Test
    @DisplayName("测试配置验证 - 无效配置")
    void testConfigValidation_InvalidConfig() {
        // 配置无效的清理间隔
        doThrow(new IllegalArgumentException("清理间隔时间不能小于 300000 毫秒（5分钟）"))
                .when(properties).validateCleanupConfig();

        // 验证初始化时会抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            monitorService.init();
        });
    }

    @Test
    @DisplayName("测试清理功能禁用")
    void testCleanupDisabled() {
        when(properties.isCleanupEnabled()).thenReturn(false);

        // 初始化应该成功，但不启动清理任务
        assertDoesNotThrow(() -> {
            monitorService.init();
        });

        // 验证没有调用清理相关的方法
        verify(transferRecordMapper, never()).selectList(any(QueryWrapper.class));
        verify(chunkRecordMapper, never()).selectList(any(QueryWrapper.class));
    }

    @Test
    @DisplayName("测试手动清理 - 有过期记录")
    void testManualCleanup_WithExpiredRecords() {
        // 准备测试数据
        FileTransferRecord expiredRecord1 = createExpiredTransferRecord("record1", "file1.txt");
        FileTransferRecord expiredRecord2 = createExpiredTransferRecord("record2", "file2.txt");
        
        FileChunkRecord expiredChunk1 = createExpiredChunkRecord("chunk1", "record1");
        FileChunkRecord expiredChunk2 = createExpiredChunkRecord("chunk2", "record1");
        FileChunkRecord expiredChunk3 = createExpiredChunkRecord("chunk3", "record2");

        // 配置Mock返回值
        when(transferRecordMapper.selectList(any(QueryWrapper.class)))
                .thenReturn(Arrays.asList(expiredRecord1, expiredRecord2));
        when(chunkRecordMapper.selectList(any(QueryWrapper.class)))
                .thenReturn(Arrays.asList(expiredChunk1, expiredChunk2, expiredChunk3));

        // 配置清理统计服务
        CleanupOperationInfo mockOperation = new CleanupOperationInfo();
        when(cleanupStatisticsService.startCleanupOperation()).thenReturn(mockOperation);

        // 执行手动清理
        CleanupOperationInfo result = monitorService.manualCleanup();

        // 验证结果
        assertNotNull(result);
        assertEquals(mockOperation, result);

        // 验证删除操作被调用
        verify(transferRecordMapper, times(2)).deleteById(any());
        verify(chunkRecordMapper, times(3)).deleteById(any());

        // 验证统计服务被调用
        verify(cleanupStatisticsService).startCleanupOperation();
        verify(cleanupStatisticsService).completeCleanupOperation(eq(mockOperation), eq(2L), eq(3L), eq(0L));
    }

    @Test
    @DisplayName("测试手动清理 - 无过期记录")
    void testManualCleanup_NoExpiredRecords() {
        // 配置Mock返回空列表
        when(transferRecordMapper.selectList(any(QueryWrapper.class)))
                .thenReturn(Collections.emptyList());
        when(chunkRecordMapper.selectList(any(QueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // 配置清理统计服务
        CleanupOperationInfo mockOperation = new CleanupOperationInfo();
        when(cleanupStatisticsService.startCleanupOperation()).thenReturn(mockOperation);

        // 执行手动清理
        CleanupOperationInfo result = monitorService.manualCleanup();

        // 验证结果
        assertNotNull(result);

        // 验证没有删除操作
        verify(transferRecordMapper, never()).deleteById(any());
        verify(chunkRecordMapper, never()).deleteById(any());

        // 验证统计服务被调用，但清理数量为0
        verify(cleanupStatisticsService).completeCleanupOperation(eq(mockOperation), eq(0L), eq(0L), eq(0L));
    }

    @Test
    @DisplayName("测试清理操作异常处理")
    void testCleanupExceptionHandling() {
        // 配置Mock抛出异常
        when(transferRecordMapper.selectList(any(QueryWrapper.class)))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // 配置清理统计服务
        CleanupOperationInfo mockOperation = new CleanupOperationInfo();
        when(cleanupStatisticsService.startCleanupOperation()).thenReturn(mockOperation);

        // 执行手动清理
        CleanupOperationInfo result = monitorService.manualCleanup();

        // 验证结果
        assertNotNull(result);

        // 验证异常被正确处理
        verify(cleanupStatisticsService).failCleanupOperation(eq(mockOperation), eq("数据库连接失败"));
    }

    @Test
    @DisplayName("测试传输统计信息")
    void testTransferStatistics() {
        // 配置Mock返回值
        when(transferRecordMapper.selectCount(any(QueryWrapper.class)))
                .thenReturn(5L)  // pending
                .thenReturn(3L)  // transferring
                .thenReturn(127L) // completed
                .thenReturn(2L)  // failed
                .thenReturn(1L); // paused

        // 获取统计信息
        TransferStatistics stats = monitorService.getTransferStatistics();

        // 验证结果
        assertNotNull(stats);
        assertEquals(5L, stats.getPendingCount());
        assertEquals(3L, stats.getTransferringCount());
        assertEquals(127L, stats.getCompletedCount());
        assertEquals(2L, stats.getFailedCount());
        assertEquals(1L, stats.getPausedCount());
        assertEquals(138L, stats.getTotalCount()); // 5+3+127+2+1
        assertEquals(8L, stats.getActiveCount()); // 5+3
        
        // 验证成功率计算
        double expectedSuccessRate = (double) 127 / (127 + 2) * 100; // 98.45%
        assertEquals(expectedSuccessRate, stats.getSuccessRate(), 0.01);
    }

    @Test
    @DisplayName("测试批量删除限制")
    void testBatchDeleteLimit() {
        // 设置较小的批量删除大小
        when(properties.getMaxBatchDeleteSize()).thenReturn(2);

        // 准备3条过期记录
        FileTransferRecord record1 = createExpiredTransferRecord("record1", "file1.txt");
        FileTransferRecord record2 = createExpiredTransferRecord("record2", "file2.txt");
        FileTransferRecord record3 = createExpiredTransferRecord("record3", "file3.txt");

        when(transferRecordMapper.selectList(any(QueryWrapper.class)))
                .thenReturn(Arrays.asList(record1, record2, record3));
        when(chunkRecordMapper.selectList(any(QueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // 配置清理统计服务
        CleanupOperationInfo mockOperation = new CleanupOperationInfo();
        when(cleanupStatisticsService.startCleanupOperation()).thenReturn(mockOperation);

        // 执行手动清理
        monitorService.manualCleanup();

        // 验证所有记录都被删除（分批处理）
        verify(transferRecordMapper, times(3)).deleteById(any());
        verify(cleanupStatisticsService).completeCleanupOperation(eq(mockOperation), eq(3L), eq(0L), eq(0L));
    }

    /**
     * 创建过期的传输记录
     */
    private FileTransferRecord createExpiredTransferRecord(String id, String fileName) {
        FileTransferRecord record = new FileTransferRecord();
        record.setId(id);
        record.setFileName(fileName);
        record.setFileSize(1024L);
        record.setStatus(CleanupConstants.TRANSFER_STATUS_COMPLETED);
        record.setCreateTime(Instant.now().minus(2, ChronoUnit.DAYS).toString());
        record.setUpdateTime(Instant.now().minus(2, ChronoUnit.DAYS).toString());
        return record;
    }

    /**
     * 创建过期的分块记录
     */
    private FileChunkRecord createExpiredChunkRecord(String id, String transferId) {
        FileChunkRecord chunk = new FileChunkRecord();
        chunk.setId(id);
        chunk.setTransferId(transferId);
        chunk.setChunkIndex(0);
        chunk.setChunkSize(1024L);
        chunk.setStatus(CleanupConstants.CHUNK_STATUS_COMPLETED);
        chunk.setCreateTime(Instant.now().minus(8, ChronoUnit.DAYS).toString());
        return chunk;
    }
}
