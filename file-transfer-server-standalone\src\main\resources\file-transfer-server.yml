# 文件传输独立服务端配置文件
server:
  port: 49011
  servlet:
    # 统一设置context-path为/filetransfer，确保API端点格式一致
    # 最终API端点格式：/filetransfer/api/file/*, /filetransfer/api/admin/*
    context-path: /filetransfer
  tomcat:
    max-threads: 200
    min-spare-threads: 10

spring:
  application:
    name: file-transfer-server
  
  # 数据源配置 - SQLite（优化并发性能）
  datasource:
    driver-class-name: org.sqlite.JDBC
    # 添加SQLite优化参数：启用WAL模式、设置超时、优化并发
    url: **************************************************************************************************************************************
    username:
    password:

  # 连接池配置（优化并发处理）
  druid:
    initial-size: 2
    min-idle: 2
    max-active: 5  # 降低最大连接数，SQLite不适合高并发
    max-wait: 30000  # 减少等待时间
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 300000
    validation-query: SELECT 1
    test-while-idle: true
    test-on-borrow: false
    test-on-return: false
    # 添加连接池优化配置
    pool-prepared-statements: true
    max-pool-prepared-statement-per-connection-size: 20
    # 连接超时配置
    connection-error-retry-attempts: 3
    break-after-acquire-failure: true

# MyBatis Plus配置（优化数据库操作）
mybatis-plus:
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: true
    # 设置超时时间
    default-statement-timeout: 30
    # 优化批处理
    default-executor-type: reuse
  global-config:
    db-config:
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 文件传输服务端配置
file:
  transfer:
    server:
      enabled: true
      
      # 存储配置
      storage-path: ./data/file-transfer/files
      database-path: ./data/file-transfer/database.db
      
      # 功能配置
      fast-upload-enabled: true
      rate-limit-enabled: true
      
      # 速度限制 (字节/秒)
      upload-rate-limit: 10485760      # 10MB/s
      download-rate-limit: 10485760    # 10MB/s
      
      # 文件大小限制
      default-chunk-size: 2097152      # 2MB
      max-file-size: 104857600         # 100MB
      max-in-memory-size: 10485760     # 10MB
      
      # 清理配置
      cleanup-enabled: true            # 是否启用自动清理功能
      cleanup-interval: 3600000        # 清理间隔时间（毫秒），默认1小时
      record-expire-time: 86400000     # 传输记录过期时间（毫秒），默认24小时
      chunk-expire-time: 604800000     # 分块记录过期时间（毫秒），默认7天
      failed-record-retain-time: 259200000  # 失败记录保留时间（毫秒），默认3天
      delete-physical-files: false     # 是否在清理时删除物理文件
      max-batch-delete-size: 1000      # 批量删除的最大记录数
      
      # 服务配置
      swagger-enabled: true
      cors-enabled: true
      allowed-origins:
        - "*"

      # 用户配置 - 添加demo用户用于演示和测试
      users:
        # 演示用户 - 用于客户端演示和集成测试
        demo:
          secret-key: "demo-secret-key-2024"  # 与客户端演示配置保持一致
          role: "user"                        # 普通用户角色
          storage-path: "./data/demo/files"   # 演示用户专用文件存储路径
          upload-rate-limit: 10485760         # 10MB/s上传限速
          download-rate-limit: 10485760       # 10MB/s下载限速
          max-file-size: 104857600            # 100MB最大文件大小
          default-chunk-size: 1048576         # 1MB分块大小，与客户端配置匹配
          max-in-memory-size: 10485760        # 10MB内存缓存大小
          fast-upload-enabled: true           # 启用秒传功能
          rate-limit-enabled: true            # 启用速度限制

        # 管理员用户 - 用于管理功能测试
        admin:
          secret-key: "admin-secret-key-2024" # 管理员密钥
          role: "admin"                       # 管理员角色，可访问管理接口
          storage-path: "./data/admin/files"  # 管理员专用文件存储路径
          upload-rate-limit: 52428800         # 50MB/s上传限速
          download-rate-limit: 52428800       # 50MB/s下载限速
          max-file-size: 1073741824           # 1GB最大文件大小
          default-chunk-size: 4194304         # 4MB分块大小
          max-in-memory-size: 52428800        # 50MB内存缓存大小
          fast-upload-enabled: true           # 启用秒传功能
          rate-limit-enabled: false           # 管理员不限速

# 日志配置
logging:
  level:
    com.sdesrd.filetransfer: DEBUG
    org.springframework.web: INFO
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ./logs/file-transfer-server.log
    max-size: 10MB
    max-history: 30

# 监控端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always 