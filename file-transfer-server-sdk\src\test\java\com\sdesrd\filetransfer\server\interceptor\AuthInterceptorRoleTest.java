package com.sdesrd.filetransfer.server.interceptor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

import java.io.PrintWriter;
import java.io.StringWriter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.sdesrd.filetransfer.server.config.UserConfig;
import com.sdesrd.filetransfer.server.service.AuthService;

import lombok.extern.slf4j.Slf4j;

/**
 * 认证拦截器角色验证功能测试
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@DisplayName("认证拦截器角色验证测试")
class AuthInterceptorRoleTest {
    
    private static final String TEST_INFO_PREFIX = "[测试信息]";
    private static final String EXPECTED_EXCEPTION_PREFIX = "[预期异常测试]";
    
    @Mock
    private AuthService authService;
    
    @Mock
    private HttpServletRequest request;
    
    @Mock
    private HttpServletResponse response;
    
    @InjectMocks
    private AuthInterceptor authInterceptor;
    
    private StringWriter responseWriter;
    private PrintWriter printWriter;
    
    @BeforeEach
    void setUp() throws Exception {
        // 设置响应写入器（使用lenient模式避免UnnecessaryStubbing错误）
        responseWriter = new StringWriter();
        printWriter = new PrintWriter(responseWriter);
        lenient().when(response.getWriter()).thenReturn(printWriter);
    }
    
    @Test
    @DisplayName("管理员用户访问管理接口 - 应该允许访问")
    void testAdminUserAccessAdminInterface() throws Exception {
        log.info("{} 开始测试管理员用户访问管理接口", TEST_INFO_PREFIX);
        
        // 准备测试数据
        String adminUser = "admin";
        String authToken = "admin:1234567890:signature";
        String adminPath = "/filetransfer/api/admin/status";
        
        // 创建管理员用户配置
        UserConfig adminConfig = new UserConfig();
        adminConfig.setRole(UserConfig.Role.ADMIN);
        adminConfig.setSecretKey("admin-secret");
        
        // 模拟请求和认证（使用lenient模式避免UnnecessaryStubbing错误）
        lenient().when(request.getRequestURI()).thenReturn(adminPath);
        lenient().when(request.getHeader("X-File-Transfer-User")).thenReturn(adminUser);
        lenient().when(request.getHeader("X-File-Transfer-Auth")).thenReturn(authToken);
        lenient().when(request.getRemoteAddr()).thenReturn("127.0.0.1");

        lenient().when(authService.authenticate(adminUser, authToken)).thenReturn(true);
        lenient().when(authService.getUserConfig(adminUser)).thenReturn(adminConfig);
        
        // 执行测试
        boolean result = authInterceptor.preHandle(request, response, null);
        
        // 验证结果
        assertTrue(result, "管理员用户应该能够访问管理接口");
        verify(request).setAttribute("currentUser", adminUser);
        
        log.info("{} 管理员用户访问管理接口测试通过", TEST_INFO_PREFIX);
    }
    
    @Test
    @DisplayName("普通用户访问管理接口 - 应该被拒绝")
    void testRegularUserAccessAdminInterface() throws Exception {
        log.info("{} 开始测试普通用户访问管理接口 - 预期结果：访问被拒绝", EXPECTED_EXCEPTION_PREFIX);
        
        // 准备测试数据
        String regularUser = "user";
        String authToken = "user:1234567890:signature";
        String adminPath = "/filetransfer/api/admin/status";
        
        // 创建普通用户配置
        UserConfig userConfig = new UserConfig();
        userConfig.setRole(UserConfig.Role.USER);
        userConfig.setSecretKey("user-secret");
        
        // 模拟请求和认证
        when(request.getRequestURI()).thenReturn(adminPath);
        when(request.getHeader("X-File-Transfer-User")).thenReturn(regularUser);
        when(request.getHeader("X-File-Transfer-Auth")).thenReturn(authToken);
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");
        
        when(authService.authenticate(regularUser, authToken)).thenReturn(true);
        when(authService.getUserConfig(regularUser)).thenReturn(userConfig);
        
        // 执行测试
        boolean result = authInterceptor.preHandle(request, response, null);
        
        // 验证结果
        assertFalse(result, "普通用户不应该能够访问管理接口");
        verify(response).setStatus(HttpServletResponse.SC_FORBIDDEN);
        verify(response).setContentType("application/json;charset=UTF-8");
        
        // 验证响应内容
        printWriter.flush();
        String responseContent = responseWriter.toString();
        assertTrue(responseContent.contains("权限不足"), "响应应该包含权限不足的错误信息");
        assertTrue(responseContent.contains("403"), "响应应该包含403错误码");
        
        log.info("{} 普通用户访问管理接口被正确拒绝", TEST_INFO_PREFIX);
    }
    
    @Test
    @DisplayName("普通用户访问普通接口 - 应该允许访问")
    void testRegularUserAccessRegularInterface() throws Exception {
        log.info("{} 开始测试普通用户访问普通接口", TEST_INFO_PREFIX);
        
        // 准备测试数据
        String regularUser = "user";
        String authToken = "user:1234567890:signature";
        String regularPath = "/filetransfer/api/file/upload/init";
        
        // 创建普通用户配置
        UserConfig userConfig = new UserConfig();
        userConfig.setRole(UserConfig.Role.USER);
        userConfig.setSecretKey("user-secret");
        
        // 模拟请求和认证（使用lenient模式避免UnnecessaryStubbing错误）
        lenient().when(request.getRequestURI()).thenReturn(regularPath);
        lenient().when(request.getHeader("X-File-Transfer-User")).thenReturn(regularUser);
        lenient().when(request.getHeader("X-File-Transfer-Auth")).thenReturn(authToken);
        lenient().when(request.getRemoteAddr()).thenReturn("127.0.0.1");

        lenient().when(authService.authenticate(regularUser, authToken)).thenReturn(true);
        lenient().when(authService.getUserConfig(regularUser)).thenReturn(userConfig);
        
        // 执行测试
        boolean result = authInterceptor.preHandle(request, response, null);
        
        // 验证结果
        assertTrue(result, "普通用户应该能够访问普通接口");
        verify(request).setAttribute("currentUser", regularUser);
        
        log.info("{} 普通用户访问普通接口测试通过", TEST_INFO_PREFIX);
    }
    
    @Test
    @DisplayName("数据库管理接口角色验证测试")
    void testDatabaseManagementInterfaceRoleValidation() throws Exception {
        log.info("{} 开始测试数据库管理接口角色验证", TEST_INFO_PREFIX);
        
        // 准备测试数据
        String adminUser = "admin";
        String authToken = "admin:1234567890:signature";
        String databasePath = "/filetransfer/api/database/rebuild";
        
        // 创建管理员用户配置
        UserConfig adminConfig = new UserConfig();
        adminConfig.setRole(UserConfig.Role.ADMIN);
        adminConfig.setSecretKey("admin-secret");
        
        // 模拟请求和认证（使用lenient模式避免UnnecessaryStubbing错误）
        lenient().when(request.getRequestURI()).thenReturn(databasePath);
        lenient().when(request.getHeader("X-File-Transfer-User")).thenReturn(adminUser);
        lenient().when(request.getHeader("X-File-Transfer-Auth")).thenReturn(authToken);
        lenient().when(request.getRemoteAddr()).thenReturn("127.0.0.1");

        lenient().when(authService.authenticate(adminUser, authToken)).thenReturn(true);
        lenient().when(authService.getUserConfig(adminUser)).thenReturn(adminConfig);
        
        // 执行测试
        boolean result = authInterceptor.preHandle(request, response, null);
        
        // 验证结果
        assertTrue(result, "管理员用户应该能够访问数据库管理接口");
        verify(request).setAttribute("currentUser", adminUser);
        
        log.info("{} 数据库管理接口角色验证测试通过", TEST_INFO_PREFIX);
    }
    
    @Test
    @DisplayName("用户配置为null时的权限验证")
    void testNullUserConfigPermissionValidation() throws Exception {
        log.info("{} 开始测试用户配置为null时的权限验证 - 预期结果：访问被拒绝", EXPECTED_EXCEPTION_PREFIX);
        
        // 准备测试数据
        String unknownUser = "unknown";
        String authToken = "unknown:1234567890:signature";
        String adminPath = "/filetransfer/api/admin/status";
        
        // 模拟请求和认证
        when(request.getRequestURI()).thenReturn(adminPath);
        when(request.getHeader("X-File-Transfer-User")).thenReturn(unknownUser);
        when(request.getHeader("X-File-Transfer-Auth")).thenReturn(authToken);
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");
        
        when(authService.authenticate(unknownUser, authToken)).thenReturn(true);
        when(authService.getUserConfig(unknownUser)).thenReturn(null);
        
        // 执行测试
        boolean result = authInterceptor.preHandle(request, response, null);
        
        // 验证结果
        assertFalse(result, "用户配置为null时不应该能够访问管理接口");
        verify(response).setStatus(HttpServletResponse.SC_FORBIDDEN);
        
        log.info("{} 用户配置为null时的权限验证测试通过", TEST_INFO_PREFIX);
    }
}
