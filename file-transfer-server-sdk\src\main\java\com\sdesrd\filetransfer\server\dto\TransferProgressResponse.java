package com.sdesrd.filetransfer.server.dto;

import lombok.Data;

/**
 * 传输进度响应
 */
@Data
public class TransferProgressResponse {
    
    /**
     * 传输ID
     */
    private String transferId;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 总大小（字节）
     */
    private Long totalSize;
    
    /**
     * 已传输大小（字节）
     */
    private Long transferredSize;
    
    /**
     * 进度百分比（0-100）
     */
    private Double progress;
    
    /**
     * 总分块数
     */
    private Integer totalChunks;
    
    /**
     * 已完成分块数
     */
    private Integer completedChunks;
    
    /**
     * 是否完成
     */
    private Boolean completed;
    
    /**
     * 传输状态：0-待传输，1-传输中，2-传输完成，3-传输失败
     */
    private Integer status;
} 