# 文件传输SDK清理功能使用指南

## 概述

文件传输SDK的清理功能提供了自动和手动清理过期传输记录的能力，帮助维护数据库性能和存储空间。本指南详细介绍了清理功能的配置、使用方法和最佳实践。

## 功能特性

### 🔧 核心功能
- **自动清理**：定时清理过期的传输记录和分块记录
- **手动清理**：支持通过API手动触发清理操作
- **统计监控**：提供详细的清理操作统计信息
- **配置灵活**：支持多种清理策略和时间配置
- **线程安全**：所有清理操作都是线程安全的
- **批量处理**：支持批量删除，避免数据库性能问题

### 📊 清理类型
1. **传输记录清理**：清理已完成或失败的文件传输记录
2. **分块记录清理**：清理过期的文件分块记录
3. **物理文件清理**：可选的物理文件删除功能（默认禁用）

## 配置说明

### 基础配置

在 `application.yml` 或 `file-transfer-server.yml` 中配置清理功能：

```yaml
file:
  transfer:
    server:
      # 清理功能配置
      cleanup-enabled: true                    # 是否启用自动清理功能
      cleanup-interval: 3600000               # 清理间隔时间（毫秒），默认1小时
      record-expire-time: 86400000            # 传输记录过期时间（毫秒），默认24小时
      chunk-expire-time: 604800000            # 分块记录过期时间（毫秒），默认7天
      failed-record-retain-time: 259200000    # 失败记录保留时间（毫秒），默认3天
      delete-physical-files: false            # 是否在清理时删除物理文件
      max-batch-delete-size: 1000             # 批量删除的最大记录数
```

### 配置参数详解

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `cleanup-enabled` | boolean | true | 是否启用自动清理功能 |
| `cleanup-interval` | long | 3600000 | 清理任务执行间隔（毫秒） |
| `record-expire-time` | long | 86400000 | 传输记录过期时间（毫秒） |
| `chunk-expire-time` | long | 604800000 | 分块记录过期时间（毫秒） |
| `failed-record-retain-time` | long | 259200000 | 失败记录保留时间（毫秒） |
| `delete-physical-files` | boolean | false | 是否删除物理文件 |
| `max-batch-delete-size` | int | 1000 | 批量删除最大记录数 |

### 配置验证规则

系统会自动验证配置的有效性：

- **清理间隔时间**：不能小于5分钟，不能大于7天
- **记录过期时间**：不能小于1小时，不能大于30天
- **分块记录过期时间**：必须大于等于传输记录过期时间
- **失败记录保留时间**：必须大于等于传输记录过期时间
- **批量删除大小**：必须大于0，不能超过10000

## API接口

### 1. 获取清理统计信息

```http
GET /filetransfer/api/admin/cleanup/statistics
```

**响应示例：**
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "totalCleanedTransferRecords": 1250,
    "totalCleanedChunkRecords": 3750,
    "totalCleanedPhysicalFiles": 125,
    "totalCleanupOperations": 48,
    "totalCleanupFailures": 2,
    "successRate": 95.83,
    "serviceStartTime": "2025-06-26T10:30:00Z",
    "lastCleanupOperation": {
      "operationId": "1719396000000",
      "startTime": "2025-06-26T14:00:00Z",
      "endTime": "2025-06-26T14:00:05Z",
      "status": "COMPLETED",
      "cleanedTransferRecords": 25,
      "cleanedChunkRecords": 75,
      "cleanedPhysicalFiles": 5,
      "durationMs": 5000
    }
  }
}
```

### 2. 手动触发清理操作

```http
POST /filetransfer/api/admin/cleanup/manual
```

**响应示例：**
```json
{
  "success": true,
  "message": "清理操作已触发",
  "data": {
    "operationId": "1719396120000",
    "startTime": "2025-06-26T14:02:00Z",
    "status": "RUNNING"
  }
}
```

### 3. 获取清理配置信息

```http
GET /filetransfer/api/admin/cleanup/config
```

**响应示例：**
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "cleanupEnabled": true,
    "cleanupIntervalMs": 3600000,
    "recordExpireTimeMs": 86400000,
    "chunkExpireTimeMs": 604800000,
    "failedRecordRetainTimeMs": 259200000,
    "deletePhysicalFiles": false,
    "maxBatchDeleteSize": 1000,
    "cleanupIntervalSeconds": 3600,
    "recordExpireTimeSeconds": 86400,
    "chunkExpireTimeSeconds": 604800,
    "failedRecordRetainTimeSeconds": 259200
  }
}
```

### 4. 重置清理统计信息

```http
POST /filetransfer/api/admin/cleanup/reset-statistics
```

## 使用示例

### Java代码示例

```java
@RestController
@RequestMapping("/api/cleanup")
public class CleanupController {
    
    @Autowired
    private FileTransferMonitorService monitorService;
    
    @Autowired
    private CleanupStatisticsService cleanupStatisticsService;
    
    /**
     * 获取清理统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<CleanupStatistics> getCleanupStats() {
        CleanupStatistics stats = cleanupStatisticsService.getStatistics();
        return ResponseEntity.ok(stats);
    }
    
    /**
     * 手动触发清理
     */
    @PostMapping("/trigger")
    public ResponseEntity<CleanupOperationInfo> triggerCleanup() {
        CleanupOperationInfo operation = monitorService.manualCleanup();
        return ResponseEntity.ok(operation);
    }
}
```

### 配置类示例

```java
@Configuration
@EnableConfigurationProperties(FileTransferProperties.class)
public class CleanupConfig {
    
    @Bean
    @ConditionalOnProperty(
        prefix = "file.transfer.server", 
        name = "cleanup-enabled", 
        havingValue = "true", 
        matchIfMissing = true
    )
    public CleanupStatisticsService cleanupStatisticsService() {
        return new CleanupStatisticsService();
    }
    
    @EventListener
    public void onApplicationReady(ApplicationReadyEvent event) {
        // 应用启动后验证清理配置
        FileTransferProperties properties = event.getApplicationContext()
            .getBean(FileTransferProperties.class);
        
        try {
            properties.validateCleanupConfig();
            log.info("清理配置验证通过");
        } catch (IllegalArgumentException e) {
            log.error("清理配置验证失败: {}", e.getMessage());
            throw e;
        }
    }
}
```

## 最佳实践

### 1. 配置建议

**生产环境配置：**
```yaml
file:
  transfer:
    server:
      cleanup-enabled: true
      cleanup-interval: 3600000      # 1小时清理一次
      record-expire-time: 86400000   # 保留24小时的传输记录
      chunk-expire-time: 604800000   # 保留7天的分块记录
      failed-record-retain-time: 259200000  # 保留3天的失败记录
      delete-physical-files: false   # 生产环境建议手动管理物理文件
      max-batch-delete-size: 500     # 适中的批量大小
```

**开发环境配置：**
```yaml
file:
  transfer:
    server:
      cleanup-enabled: true
      cleanup-interval: 300000       # 5分钟清理一次（快速测试）
      record-expire-time: 3600000    # 保留1小时的传输记录
      chunk-expire-time: 7200000     # 保留2小时的分块记录
      failed-record-retain-time: 3600000  # 保留1小时的失败记录
      delete-physical-files: true    # 开发环境可以自动删除
      max-batch-delete-size: 100     # 较小的批量大小
```

### 2. 监控建议

1. **定期检查清理统计**：监控清理操作的成功率和清理数量
2. **关注失败率**：如果清理失败率过高，检查数据库连接和配置
3. **监控磁盘空间**：定期检查存储空间使用情况
4. **日志监控**：关注清理相关的日志信息

### 3. 性能优化

1. **合理设置批量大小**：根据数据库性能调整 `max-batch-delete-size`
2. **避免高峰期清理**：可以通过调整 `cleanup-interval` 避开业务高峰期
3. **分层清理策略**：对不同类型的记录设置不同的过期时间

### 4. 安全注意事项

1. **物理文件删除**：谨慎启用 `delete-physical-files`，建议先备份
2. **权限控制**：确保只有管理员可以访问清理API
3. **配置验证**：启动时验证清理配置的合理性
4. **监控告警**：设置清理失败的告警机制

## 故障排除

### 常见问题

**Q: 清理任务不执行？**
A: 检查 `cleanup-enabled` 是否为 true，以及配置验证是否通过。

**Q: 清理操作失败率高？**
A: 检查数据库连接状态，确认数据库权限，查看详细错误日志。

**Q: 清理速度慢？**
A: 调整 `max-batch-delete-size` 参数，或者检查数据库索引。

**Q: 物理文件没有被删除？**
A: 确认 `delete-physical-files` 设置为 true，检查文件权限。

### 日志分析

清理相关的日志级别：
- **INFO**：清理操作开始/完成，清理数量超过阈值
- **DEBUG**：详细的清理过程信息
- **WARN**：清理失败率过高，发现卡住的传输
- **ERROR**：清理操作异常，配置验证失败

## 版本兼容性

- **最低要求**：Java 8+, Spring Boot 2.0+
- **推荐版本**：Java 11+, Spring Boot 2.7+
- **数据库支持**：SQLite 3.0+, MySQL 5.7+, PostgreSQL 10+

## 更新日志

### v1.0.0 (2025-06-26)
- 初始版本发布
- 支持自动和手动清理
- 提供详细的统计信息
- 完整的配置验证机制
- 线程安全的清理操作
