package com.sdesrd.filetransfer.client.util;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.sdesrd.filetransfer.client.exception.FileTransferException;

import lombok.extern.slf4j.Slf4j;

/**
 * ULID验证器测试类
 * 验证客户端ULID格式验证功能的正确性
 */
@Slf4j
@DisplayName("ULID验证器测试")
class UlidValidatorTest {

    /** 测试信息前缀 */
    private static final String TEST_INFO_PREFIX = "[ULID_VALIDATOR_TEST]";

    /** 有效的ULID示例 */
    private static final String VALID_ULID = "01ARZ3NDEKTSV4RRFFQ69G5FAV";
    
    /** 另一个有效的ULID示例 */
    private static final String VALID_ULID_2 = "01FHAC7QJKX9VWPQZR8N2YBMTG";

    @Test
    @DisplayName("验证有效的ULID格式")
    void testValidUlid() {
        log.info("{} 测试有效的ULID格式验证", TEST_INFO_PREFIX);

        // 测试标准ULID格式
        assertTrue(UlidValidator.isValidUlid(VALID_ULID), "标准ULID应该验证通过");
        assertTrue(UlidValidator.isValidUlid(VALID_ULID_2), "另一个标准ULID应该验证通过");
        
        // 测试小写ULID（应该被接受）
        assertTrue(UlidValidator.isValidUlid(VALID_ULID.toLowerCase()), "小写ULID应该验证通过");
        
        // 测试带空格的ULID（应该被trim后验证通过）
        assertTrue(UlidValidator.isValidUlid("  " + VALID_ULID + "  "), "带空格的ULID应该验证通过");

        log.info("{} 有效ULID格式验证测试通过", TEST_INFO_PREFIX);
    }

    @Test
    @DisplayName("验证无效的ULID格式")
    void testInvalidUlid() {
        log.info("{} 测试无效的ULID格式验证", TEST_INFO_PREFIX);

        // 测试null和空字符串
        assertFalse(UlidValidator.isValidUlid(null), "null应该验证失败");
        assertFalse(UlidValidator.isValidUlid(""), "空字符串应该验证失败");
        assertFalse(UlidValidator.isValidUlid("   "), "空白字符串应该验证失败");

        // 测试长度错误
        assertFalse(UlidValidator.isValidUlid("01ARZ3NDEKTSV4RRFFQ69G5FA"), "25字符应该验证失败");
        assertFalse(UlidValidator.isValidUlid("01ARZ3NDEKTSV4RRFFQ69G5FAVX"), "27字符应该验证失败");

        // 测试包含无效字符
        assertFalse(UlidValidator.isValidUlid("01ARZ3NDEKTSV4RRFFQ69G5FAI"), "包含I字符应该验证失败");
        assertFalse(UlidValidator.isValidUlid("01ARZ3NDEKTSV4RRFFQ69G5FAL"), "包含L字符应该验证失败");
        assertFalse(UlidValidator.isValidUlid("01ARZ3NDEKTSV4RRFFQ69G5FAO"), "包含O字符应该验证失败");
        assertFalse(UlidValidator.isValidUlid("01ARZ3NDEKTSV4RRFFQ69G5FAU"), "包含U字符应该验证失败");
        
        // 测试包含特殊字符
        assertFalse(UlidValidator.isValidUlid("01ARZ3NDEKTSV4RRFFQ69G5FA-"), "包含连字符应该验证失败");
        assertFalse(UlidValidator.isValidUlid("01ARZ3NDEKTSV4RRFFQ69G5FA_"), "包含下划线应该验证失败");

        log.info("{} 无效ULID格式验证测试通过", TEST_INFO_PREFIX);
    }

    @Test
    @DisplayName("验证并规范化ULID")
    void testValidateAndNormalize() throws FileTransferException {
        log.info("{} 测试ULID验证和规范化", TEST_INFO_PREFIX);

        // 测试正常情况
        String normalized = UlidValidator.validateAndNormalize(VALID_ULID);
        assertEquals(VALID_ULID, normalized, "大写ULID应该保持不变");

        // 测试小写转大写
        String normalizedFromLower = UlidValidator.validateAndNormalize(VALID_ULID.toLowerCase());
        assertEquals(VALID_ULID, normalizedFromLower, "小写ULID应该被转换为大写");

        // 测试去除空格
        String normalizedFromSpaced = UlidValidator.validateAndNormalize("  " + VALID_ULID + "  ");
        assertEquals(VALID_ULID, normalizedFromSpaced, "带空格的ULID应该被去除空格");

        log.info("{} ULID验证和规范化测试通过", TEST_INFO_PREFIX);
    }

    @Test
    @DisplayName("验证并规范化ULID - 异常情况")
    void testValidateAndNormalizeExceptions() {
        log.info("{} 测试ULID验证和规范化的异常情况", TEST_INFO_PREFIX);

        // 测试null输入
        FileTransferException nullException = assertThrows(FileTransferException.class, 
            () -> UlidValidator.validateAndNormalize(null));
        assertTrue(nullException.getMessage().contains("不能为空"), "null输入应该抛出相应异常");

        // 测试空字符串
        FileTransferException emptyException = assertThrows(FileTransferException.class, 
            () -> UlidValidator.validateAndNormalize(""));
        assertTrue(emptyException.getMessage().contains("不能为空"), "空字符串应该抛出相应异常");

        // 测试长度错误
        FileTransferException lengthException = assertThrows(FileTransferException.class, 
            () -> UlidValidator.validateAndNormalize("01ARZ3NDEKTSV4RRFFQ69G5FA"));
        assertTrue(lengthException.getMessage().contains("长度无效"), "长度错误应该抛出相应异常");

        // 测试格式错误
        FileTransferException formatException = assertThrows(FileTransferException.class, 
            () -> UlidValidator.validateAndNormalize("01ARZ3NDEKTSV4RRFFQ69G5FAI"));
        assertTrue(formatException.getMessage().contains("格式无效"), "格式错误应该抛出相应异常");

        log.info("{} ULID验证和规范化异常情况测试通过", TEST_INFO_PREFIX);
    }

    @Test
    @DisplayName("详细验证ULID格式")
    void testValidateUlidWithDetails() {
        log.info("{} 测试详细ULID格式验证", TEST_INFO_PREFIX);

        // 测试正常情况
        assertDoesNotThrow(() -> UlidValidator.validateUlidWithDetails(VALID_ULID, "fileId"), 
            "有效ULID应该验证通过");

        // 测试null输入
        FileTransferException nullException = assertThrows(FileTransferException.class, 
            () -> UlidValidator.validateUlidWithDetails(null, "fileId"));
        assertTrue(nullException.getMessage().contains("fileId不能为空"), "null输入应该包含字段名");

        // 测试长度错误
        FileTransferException lengthException = assertThrows(FileTransferException.class, 
            () -> UlidValidator.validateUlidWithDetails("01ARZ3NDEKTSV4RRFFQ69G5FA", "fileId"));
        assertTrue(lengthException.getMessage().contains("fileId长度无效"), "长度错误应该包含字段名");

        // 测试包含无效字符
        FileTransferException charException = assertThrows(FileTransferException.class, 
            () -> UlidValidator.validateUlidWithDetails("01ARZ3NDEKTSV4RRFFQ69G5FAI", "fileId"));
        assertTrue(charException.getMessage().contains("fileId格式无效"), "字符错误应该包含字段名");
        assertTrue(charException.getMessage().contains("'I'"), "错误信息应该指出具体的无效字符");

        log.info("{} 详细ULID格式验证测试通过", TEST_INFO_PREFIX);
    }

    @Test
    @DisplayName("获取ULID格式说明")
    void testGetUlidFormatDescription() {
        log.info("{} 测试ULID格式说明", TEST_INFO_PREFIX);

        String description = UlidValidator.getUlidFormatDescription();
        
        assertNotNull(description, "格式说明不应该为null");
        assertFalse(description.trim().isEmpty(), "格式说明不应该为空");
        assertTrue(description.contains("26"), "格式说明应该包含长度信息");
        assertTrue(description.contains("ULID"), "格式说明应该包含ULID关键词");
        assertTrue(description.contains("Base32"), "格式说明应该包含编码信息");

        log.info("{} ULID格式说明: {}", TEST_INFO_PREFIX, description);
        log.info("{} ULID格式说明测试通过", TEST_INFO_PREFIX);
    }

    @Test
    @DisplayName("边界值测试")
    void testBoundaryValues() {
        log.info("{} 测试ULID验证的边界值", TEST_INFO_PREFIX);

        // 测试最小有效ULID（全0）
        String minUlid = "00000000000000000000000000";
        assertTrue(UlidValidator.isValidUlid(minUlid), "最小ULID应该验证通过");

        // 测试最大有效ULID（全Z，但不包含I、L、O、U）
        String maxUlid = "ZZZZZZZZZZZZZZZZZZZZZZZZZZ";
        assertTrue(UlidValidator.isValidUlid(maxUlid), "最大ULID应该验证通过");

        // 测试包含所有有效字符的ULID
        String allValidChars = "0123456789ABCDEFGHJKMNPQRS";
        assertTrue(UlidValidator.isValidUlid(allValidChars), "包含所有有效字符的ULID应该验证通过");

        log.info("{} ULID边界值测试通过", TEST_INFO_PREFIX);
    }
}
