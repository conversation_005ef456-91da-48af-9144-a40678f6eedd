package com.sdesrd.filetransfer.server.service;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.sdesrd.filetransfer.server.service.CleanupStatisticsService.CleanupOperationInfo;
import com.sdesrd.filetransfer.server.service.CleanupStatisticsService.CleanupOperationStatus;
import com.sdesrd.filetransfer.server.service.CleanupStatisticsService.CleanupStatistics;

/**
 * 清理统计服务测试
 */
@DisplayName("清理统计服务测试")
class CleanupStatisticsServiceTest {

    private CleanupStatisticsService cleanupStatisticsService;

    @BeforeEach
    void setUp() {
        cleanupStatisticsService = new CleanupStatisticsService();
    }

    @Test
    @DisplayName("测试清理操作统计 - 成功场景")
    void testCleanupOperationStatistics_Success() {
        // 获取初始统计信息
        CleanupStatistics initialStats = cleanupStatisticsService.getStatistics();
        assertEquals(0, initialStats.getTotalCleanupOperations());
        assertEquals(0, initialStats.getTotalCleanedTransferRecords());
        assertEquals(0, initialStats.getTotalCleanedChunkRecords());
        assertEquals(0, initialStats.getTotalCleanedPhysicalFiles());
        assertEquals(100.0, initialStats.getSuccessRate(), 0.01);

        // 开始清理操作
        CleanupOperationInfo operation = cleanupStatisticsService.startCleanupOperation();
        assertNotNull(operation);
        assertNotNull(operation.getOperationId());
        assertNotNull(operation.getStartTime());
        assertEquals(CleanupOperationStatus.RUNNING, operation.getStatus());

        // 完成清理操作
        long cleanedTransferRecords = 10;
        long cleanedChunkRecords = 25;
        long cleanedPhysicalFiles = 5;
        
        cleanupStatisticsService.completeCleanupOperation(operation, 
                cleanedTransferRecords, cleanedChunkRecords, cleanedPhysicalFiles);

        // 验证操作信息
        assertEquals(CleanupOperationStatus.COMPLETED, operation.getStatus());
        assertNotNull(operation.getEndTime());
        assertEquals(cleanedTransferRecords, operation.getCleanedTransferRecords());
        assertEquals(cleanedChunkRecords, operation.getCleanedChunkRecords());
        assertEquals(cleanedPhysicalFiles, operation.getCleanedPhysicalFiles());
        assertTrue(operation.getDurationMs() >= 0);

        // 验证累计统计信息
        CleanupStatistics finalStats = cleanupStatisticsService.getStatistics();
        assertEquals(1, finalStats.getTotalCleanupOperations());
        assertEquals(cleanedTransferRecords, finalStats.getTotalCleanedTransferRecords());
        assertEquals(cleanedChunkRecords, finalStats.getTotalCleanedChunkRecords());
        assertEquals(cleanedPhysicalFiles, finalStats.getTotalCleanedPhysicalFiles());
        assertEquals(cleanedTransferRecords + cleanedChunkRecords, finalStats.getTotalCleanedRecords());
        assertEquals(100.0, finalStats.getSuccessRate(), 0.01);
        assertEquals(operation, finalStats.getLastCleanupOperation());
    }

    @Test
    @DisplayName("测试清理操作统计 - 失败场景")
    void testCleanupOperationStatistics_Failure() {
        // 开始清理操作
        CleanupOperationInfo operation = cleanupStatisticsService.startCleanupOperation();
        
        // 模拟操作失败
        String errorMessage = "数据库连接失败";
        cleanupStatisticsService.failCleanupOperation(operation, errorMessage);

        // 验证操作信息
        assertEquals(CleanupOperationStatus.FAILED, operation.getStatus());
        assertNotNull(operation.getEndTime());
        assertEquals(errorMessage, operation.getErrorMessage());
        assertEquals(0, operation.getCleanedTransferRecords());
        assertEquals(0, operation.getCleanedChunkRecords());
        assertEquals(0, operation.getCleanedPhysicalFiles());
        assertTrue(operation.getDurationMs() >= 0);

        // 验证累计统计信息
        CleanupStatistics stats = cleanupStatisticsService.getStatistics();
        assertEquals(1, stats.getTotalCleanupOperations());
        assertEquals(1, stats.getTotalCleanupFailures());
        assertEquals(0, stats.getTotalCleanedTransferRecords());
        assertEquals(0, stats.getTotalCleanedChunkRecords());
        assertEquals(0, stats.getTotalCleanedPhysicalFiles());
        assertEquals(0.0, stats.getSuccessRate(), 0.01);
    }

    @Test
    @DisplayName("测试多次清理操作统计")
    void testMultipleCleanupOperations() {
        // 执行多次成功的清理操作
        for (int i = 0; i < 3; i++) {
            CleanupOperationInfo operation = cleanupStatisticsService.startCleanupOperation();
            cleanupStatisticsService.completeCleanupOperation(operation, 5, 10, 2);
        }

        // 执行一次失败的清理操作
        CleanupOperationInfo failedOperation = cleanupStatisticsService.startCleanupOperation();
        cleanupStatisticsService.failCleanupOperation(failedOperation, "测试失败");

        // 验证累计统计信息
        CleanupStatistics stats = cleanupStatisticsService.getStatistics();
        assertEquals(4, stats.getTotalCleanupOperations());
        assertEquals(1, stats.getTotalCleanupFailures());
        assertEquals(15, stats.getTotalCleanedTransferRecords()); // 3 * 5
        assertEquals(30, stats.getTotalCleanedChunkRecords()); // 3 * 10
        assertEquals(6, stats.getTotalCleanedPhysicalFiles()); // 3 * 2
        assertEquals(45, stats.getTotalCleanedRecords()); // 15 + 30
        assertEquals(75.0, stats.getSuccessRate(), 0.01); // 3/4 * 100
    }

    @Test
    @DisplayName("测试重置统计信息")
    void testResetStatistics() {
        // 执行一些清理操作
        CleanupOperationInfo operation1 = cleanupStatisticsService.startCleanupOperation();
        cleanupStatisticsService.completeCleanupOperation(operation1, 10, 20, 5);

        CleanupOperationInfo operation2 = cleanupStatisticsService.startCleanupOperation();
        cleanupStatisticsService.failCleanupOperation(operation2, "测试失败");

        // 验证统计信息不为空
        CleanupStatistics beforeReset = cleanupStatisticsService.getStatistics();
        assertEquals(2, beforeReset.getTotalCleanupOperations());
        assertEquals(1, beforeReset.getTotalCleanupFailures());
        assertEquals(10, beforeReset.getTotalCleanedTransferRecords());

        // 重置统计信息
        cleanupStatisticsService.resetStatistics();

        // 验证统计信息已重置
        CleanupStatistics afterReset = cleanupStatisticsService.getStatistics();
        assertEquals(0, afterReset.getTotalCleanupOperations());
        assertEquals(0, afterReset.getTotalCleanupFailures());
        assertEquals(0, afterReset.getTotalCleanedTransferRecords());
        assertEquals(0, afterReset.getTotalCleanedChunkRecords());
        assertEquals(0, afterReset.getTotalCleanedPhysicalFiles());
        assertEquals(100.0, afterReset.getSuccessRate(), 0.01);
        
        // 最后一次操作信息应该保持不变（重置不影响当前操作信息）
        assertNotNull(afterReset.getLastCleanupOperation());
    }

    @Test
    @DisplayName("测试清理操作信息格式化")
    void testCleanupOperationInfoFormatting() {
        CleanupOperationInfo operation = cleanupStatisticsService.startCleanupOperation();
        
        // 验证格式化方法
        assertNotNull(operation.getFormattedStartTime());
        assertFalse(operation.getFormattedStartTime().isEmpty());
        assertEquals("未结束", operation.getFormattedEndTime());
        
        // 完成操作后再次验证
        cleanupStatisticsService.completeCleanupOperation(operation, 1, 2, 3);
        
        assertNotNull(operation.getFormattedEndTime());
        assertFalse(operation.getFormattedEndTime().isEmpty());
        assertNotEquals("未结束", operation.getFormattedEndTime());
    }

    @Test
    @DisplayName("测试清理统计信息格式化")
    void testCleanupStatisticsFormatting() {
        CleanupStatistics stats = cleanupStatisticsService.getStatistics();
        
        // 验证服务启动时间格式化
        assertNotNull(stats.getFormattedServiceStartTime());
        assertFalse(stats.getFormattedServiceStartTime().isEmpty());
        
        // 验证总记录数计算
        CleanupOperationInfo operation = cleanupStatisticsService.startCleanupOperation();
        cleanupStatisticsService.completeCleanupOperation(operation, 10, 20, 5);
        
        CleanupStatistics updatedStats = cleanupStatisticsService.getStatistics();
        assertEquals(30, updatedStats.getTotalCleanedRecords()); // 10 + 20
    }

    @Test
    @DisplayName("测试并发安全性")
    void testConcurrentSafety() throws InterruptedException {
        final int threadCount = 10;
        final int operationsPerThread = 5;
        Thread[] threads = new Thread[threadCount];

        // 创建多个线程并发执行清理操作
        for (int i = 0; i < threadCount; i++) {
            threads[i] = new Thread(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    CleanupOperationInfo operation = cleanupStatisticsService.startCleanupOperation();
                    cleanupStatisticsService.completeCleanupOperation(operation, 1, 1, 1);
                }
            });
            threads[i].start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }

        // 验证统计信息的正确性
        CleanupStatistics stats = cleanupStatisticsService.getStatistics();
        assertEquals(threadCount * operationsPerThread, stats.getTotalCleanupOperations());
        assertEquals(threadCount * operationsPerThread, stats.getTotalCleanedTransferRecords());
        assertEquals(threadCount * operationsPerThread, stats.getTotalCleanedChunkRecords());
        assertEquals(threadCount * operationsPerThread, stats.getTotalCleanedPhysicalFiles());
        assertEquals(100.0, stats.getSuccessRate(), 0.01);
    }
}
