# Maven Profile属性传递问题解决方案

## 问题描述

在Maven项目中配置了开发环境的profile，定义了`demo.server.host`属性为`**************`，但Java代码通过`System.getProperty()`获取时得到的是默认值`localhost`而不是profile中配置的值。

## 问题根本原因

**Maven profile中的属性不会自动传递到Java运行时的系统属性中**。Maven profile属性只在Maven构建过程中生效，需要通过特定的方式才能传递到Java程序运行时环境。

## 解决方案

### 方案1：使用exec-maven-plugin运行（推荐）

项目已经正确配置了exec-maven-plugin，使用以下命令运行：

```bash
# 使用dev profile运行
mvn exec:java -pl file-transfer-client-demo -Pdev

# 或者显式指定主类
mvn exec:java -pl file-transfer-client-demo -Pdev -Dexec.mainClass="com.sdesrd.filetransfer.demo.FileTransferClientDemo"
```

### 方案2：命令行传递系统属性

```bash
# 直接传递系统属性
mvn exec:java -pl file-transfer-client-demo -Ddemo.server.host=************** -Ddemo.server.port=49011

# 编译后运行JAR时传递
java -Ddemo.server.host=************** -Ddemo.server.port=49011 -jar target/file-transfer-client-demo-1.0.0.jar
```

### 方案3：使用环境变量

设置环境变量（代码已支持环境变量读取）：

```bash
# Linux/Mac
export DEMO_SERVER_HOST=**************
export DEMO_SERVER_PORT=49011
mvn exec:java -pl file-transfer-client-demo

# Windows
set DEMO_SERVER_HOST=**************
set DEMO_SERVER_PORT=49011
mvn exec:java -pl file-transfer-client-demo
```

## 配置优先级

代码已经改进为支持多种配置来源，优先级如下：

1. **系统属性** (`-Ddemo.server.host=value`)
2. **环境变量** (`DEMO_SERVER_HOST=value`)
3. **默认值** (代码中定义的默认值)

## 验证配置

运行程序时会显示配置来源调试信息：

```
配置来源调试信息:
  demo.server.host = ************** (来源: 系统属性)
  demo.server.port = 49011 (来源: 系统属性)
  demo.user.name = demo (来源: 默认值)
  demo.chunk.size = 1048576 (来源: 默认值)
```

## 测试脚本

使用提供的测试脚本验证配置：

```bash
# Windows
test-maven-profile.bat

# Linux/Mac
./test-maven-profile.sh
```

## 最佳实践

1. **开发环境**：使用 `mvn exec:java -Pdev` 运行
2. **测试环境**：使用 `mvn exec:java -Ptest` 运行
3. **生产环境**：使用环境变量或系统属性传递配置
4. **CI/CD**：在构建脚本中设置环境变量或系统属性

## 技术说明

### exec-maven-plugin配置

```xml
<plugin>
    <groupId>org.codehaus.mojo</groupId>
    <artifactId>exec-maven-plugin</artifactId>
    <configuration>
        <systemProperties>
            <systemProperty>
                <key>demo.server.host</key>
                <value>${demo.server.host}</value>
            </systemProperty>
        </systemProperties>
    </configuration>
</plugin>
```

这个配置确保Maven属性`${demo.server.host}`被传递为Java系统属性`demo.server.host`。

### Profile配置

```xml
<profile>
    <id>dev</id>
    <activation>
        <activeByDefault>true</activeByDefault>
    </activation>
    <properties>
        <demo.server.host>**************</demo.server.host>
        <demo.server.port>49011</demo.server.port>
    </properties>
</profile>
```

### Java代码改进

```java
/**
 * 获取系统属性值
 * 优先级：系统属性 > 环境变量 > 默认值
 */
private static String getSystemProperty(String key, String defaultValue) {
    // 1. 首先检查系统属性
    String value = System.getProperty(key);
    if (StringUtils.isNotBlank(value)) {
        return value;
    }
    
    // 2. 检查环境变量
    String envKey = key.replace('.', '_').toUpperCase();
    value = System.getenv(envKey);
    if (StringUtils.isNotBlank(value)) {
        return value;
    }
    
    // 3. 使用默认值
    return defaultValue;
}
```

## 常见问题

### Q: 为什么直接运行JAR文件时获取不到profile中的配置？
A: 因为JAR文件运行时不会加载Maven profile配置，需要通过系统属性或环境变量传递。

### Q: 如何确认当前使用的是哪个profile？
A: 使用命令 `mvn help:active-profiles -pl file-transfer-client-demo`

### Q: 如何查看最终生效的属性值？
A: 使用命令 `mvn help:effective-pom -pl file-transfer-client-demo`

## 总结

通过以上解决方案，您可以确保Maven profile中配置的属性正确传递到Java运行时环境。推荐使用exec-maven-plugin运行程序，这样可以充分利用Maven的profile功能。
