package com.sdesrd.filetransfer.server.config;

import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import lombok.Data;

/**
 * 文件传输配置属性
 */
@Data
@ConfigurationProperties(prefix = "file.transfer.server")
public class FileTransferProperties {
    
    /**
     * 是否启用文件传输服务，默认启用
     */
    private boolean enabled = true;
    
    /**
     * 数据库文件路径，默认 ./data/file-transfer/database.db
     */
    private String databasePath = "./data/file-transfer/database.db";
    
    /**
     * 传输会话过期时间（毫秒），默认1小时
     * 用于清理长时间未完成的文件传输记录，与AuthService中的API_SIGNATURE_TTL_SECONDS（API签名验证时效）概念不同
     * 此配置影响数据库清理任务，而API签名时效影响单次请求的认证有效期
     */
    private long tokenExpire = 3600000L;
    
    /**
     * 清理过期传输记录的间隔（毫秒），默认1小时
     */
    private long cleanupInterval = CleanupConstants.DEFAULT_CLEANUP_INTERVAL_MS;

    /**
     * 传输记录过期时间（毫秒），默认24小时
     */
    private long recordExpireTime = CleanupConstants.DEFAULT_RECORD_EXPIRE_TIME_MS;

    /**
     * 分块记录过期时间（毫秒），默认7天
     * 分块记录保留时间比传输记录更长，用于调试和故障排查
     */
    private long chunkExpireTime = CleanupConstants.DEFAULT_CHUNK_EXPIRE_TIME_MS;

    /**
     * 失败记录保留时间（毫秒），默认3天
     * 失败的传输记录保留更长时间用于问题分析
     */
    private long failedRecordRetainTime = CleanupConstants.DEFAULT_FAILED_RECORD_RETAIN_TIME_MS;

    /**
     * 是否启用自动清理功能，默认启用
     */
    private boolean cleanupEnabled = true;

    /**
     * 批量删除的最大记录数，默认1000
     * 避免一次性删除过多记录导致数据库性能问题
     */
    private int maxBatchDeleteSize = CleanupConstants.MAX_BATCH_DELETE_SIZE;

    /**
     * 服务器端口，默认49011
     */
    private int serverPort = 49011;
    
    /**
     * 服务器上下文路径，默认 /filetransfer
     * 注意：此配置已废弃，现在通过Spring Boot的server.servlet.context-path统一配置
     */
    @Deprecated
    private String contextPath = "/filetransfer";
    
    /**
     * 是否启用Swagger文档，默认启用
     */
    private boolean swaggerEnabled = true;
    
    /**
     * 是否启用跨域支持，默认启用
     */
    private boolean corsEnabled = true;
    
    /**
     * 允许的跨域来源，默认允许所有
     */
    private String[] allowedOrigins = {"*"};
    
    /**
     * 默认配置（用于兜底）
     */
    private UserConfig defaultConfig = createDefaultConfig();
    
    /**
     * 多用户配置
     */
    private Map<String, UserConfig> users = new HashMap<>();
    
    /**
     * 创建默认配置
     */
    private UserConfig createDefaultConfig() {
        UserConfig config = new UserConfig();
        config.setStoragePath("./data/file-transfer/files");
        config.setUploadRateLimit(10485760L);  // 10MB/s
        config.setDownloadRateLimit(10485760L);
        config.setDefaultChunkSize(2097152L);  // 2MB
        config.setMaxFileSize(104857600L);     // 100MB
        config.setMaxInMemorySize(10485760L);  // 10MB
        config.setFastUploadEnabled(true);
        config.setRateLimitEnabled(true);
        return config;
    }
    
    /**
     * 获取用户配置，如果用户不存在则返回默认配置
     */
    public UserConfig getUserConfig(String username) {
        if (username == null || username.trim().isEmpty()) {
            return defaultConfig;
        }
        
        UserConfig userConfig = users.get(username);
        if (userConfig == null) {
            return defaultConfig;
        }
        
        // 用默认值填充空值
        return mergeWithDefault(userConfig);
    }
    
    /**
     * 将用户配置与默认配置合并
     */
    private UserConfig mergeWithDefault(UserConfig userConfig) {
        UserConfig merged = new UserConfig();
        merged.setSecretKey(userConfig.getSecretKey());
        merged.setRole(userConfig.getRole() != null ? userConfig.getRole() : defaultConfig.getRole());
        merged.setStoragePath(userConfig.getStoragePath() != null ? userConfig.getStoragePath() : defaultConfig.getStoragePath());
        merged.setUploadRateLimit(userConfig.getUploadRateLimit() != null ? userConfig.getUploadRateLimit() : defaultConfig.getUploadRateLimit());
        merged.setDownloadRateLimit(userConfig.getDownloadRateLimit() != null ? userConfig.getDownloadRateLimit() : defaultConfig.getDownloadRateLimit());
        merged.setDefaultChunkSize(userConfig.getDefaultChunkSize() != null ? userConfig.getDefaultChunkSize() : defaultConfig.getDefaultChunkSize());
        merged.setMaxFileSize(userConfig.getMaxFileSize() != null ? userConfig.getMaxFileSize() : defaultConfig.getMaxFileSize());
        merged.setMaxInMemorySize(userConfig.getMaxInMemorySize() != null ? userConfig.getMaxInMemorySize() : defaultConfig.getMaxInMemorySize());
        merged.setFastUploadEnabled(userConfig.getFastUploadEnabled() != null ? userConfig.getFastUploadEnabled() : defaultConfig.getFastUploadEnabled());
        merged.setRateLimitEnabled(userConfig.getRateLimitEnabled() != null ? userConfig.getRateLimitEnabled() : defaultConfig.getRateLimitEnabled());
        return merged;
    }
    
    /**
     * 验证用户密钥
     */
    public boolean validateUser(String username, String secretKey) {
        if (username == null || secretKey == null) {
            return false;
        }

        UserConfig userConfig = users.get(username);
        if (userConfig == null) {
            return false;
        }

        return secretKey.equals(userConfig.getSecretKey());
    }

    /**
     * 验证清理配置的有效性
     *
     * @throws IllegalArgumentException 如果配置无效
     */
    public void validateCleanupConfig() {
        // 验证清理间隔时间
        if (cleanupInterval < CleanupConstants.MIN_CLEANUP_INTERVAL_MS) {
            throw new IllegalArgumentException(
                String.format("清理间隔时间不能小于 %d 毫秒（5分钟）",
                    CleanupConstants.MIN_CLEANUP_INTERVAL_MS));
        }

        if (cleanupInterval > CleanupConstants.MAX_CLEANUP_INTERVAL_MS) {
            throw new IllegalArgumentException(
                String.format("清理间隔时间不能大于 %d 毫秒（7天）",
                    CleanupConstants.MAX_CLEANUP_INTERVAL_MS));
        }

        // 验证记录过期时间
        if (recordExpireTime < CleanupConstants.MIN_RECORD_EXPIRE_TIME_MS) {
            throw new IllegalArgumentException(
                String.format("记录过期时间不能小于 %d 毫秒（1小时）",
                    CleanupConstants.MIN_RECORD_EXPIRE_TIME_MS));
        }

        if (recordExpireTime > CleanupConstants.MAX_RECORD_EXPIRE_TIME_MS) {
            throw new IllegalArgumentException(
                String.format("记录过期时间不能大于 %d 毫秒（30天）",
                    CleanupConstants.MAX_RECORD_EXPIRE_TIME_MS));
        }

        // 验证分块记录过期时间应该大于等于传输记录过期时间
        if (chunkExpireTime < recordExpireTime) {
            throw new IllegalArgumentException(
                "分块记录过期时间不能小于传输记录过期时间");
        }

        // 验证失败记录保留时间应该大于等于传输记录过期时间
        if (failedRecordRetainTime < recordExpireTime) {
            throw new IllegalArgumentException(
                "失败记录保留时间不能小于传输记录过期时间");
        }

        // 验证批量删除大小
        if (maxBatchDeleteSize <= 0) {
            throw new IllegalArgumentException("批量删除大小必须大于0");
        }

        if (maxBatchDeleteSize > 10000) {
            throw new IllegalArgumentException("批量删除大小不能超过10000，避免数据库性能问题");
        }
    }

    /**
     * 获取清理间隔时间（秒）
     * 用于定时任务调度
     */
    public long getCleanupIntervalSeconds() {
        return cleanupInterval / CleanupConstants.MILLISECONDS_TO_SECONDS;
    }

    /**
     * 获取记录过期时间（秒）
     */
    public long getRecordExpireTimeSeconds() {
        return recordExpireTime / CleanupConstants.MILLISECONDS_TO_SECONDS;
    }

    /**
     * 获取分块记录过期时间（秒）
     */
    public long getChunkExpireTimeSeconds() {
        return chunkExpireTime / CleanupConstants.MILLISECONDS_TO_SECONDS;
    }

    /**
     * 获取失败记录保留时间（秒）
     */
    public long getFailedRecordRetainTimeSeconds() {
        return failedRecordRetainTime / CleanupConstants.MILLISECONDS_TO_SECONDS;
    }
}