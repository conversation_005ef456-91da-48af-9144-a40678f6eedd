package com.sdesrd.filetransfer.server.performance;

import static org.junit.jupiter.api.Assertions.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import com.sdesrd.filetransfer.server.util.FileTransferTestUtils;
import com.sdesrd.filetransfer.server.util.FileUtils;
import com.sdesrd.filetransfer.server.util.RateLimitUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输性能测试
 * 测试系统在各种负载下的性能表现
 */
@Slf4j
@DisplayName("文件传输性能测试")
class FileTransferPerformanceTest {
    
    @TempDir
    Path tempDir;
    
    private ExecutorService executorService;
    private List<File> testFiles;
    
    @BeforeEach
    void setUp() {
        executorService = Executors.newFixedThreadPool(10);
        testFiles = new ArrayList<>();
    }
    
    @AfterEach
    void tearDown() {
        // 关闭线程池
        if (executorService != null) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 清理测试文件 - 移除权限保护机制，直接删除文件
        for (File file : testFiles) {
            if (file.exists()) {
                try {
                    // 移除权限保护机制 - 直接删除文件，不再设置可写权限
                    if (!file.delete()) {
                        log.warn("删除测试文件失败: {}", file.getAbsolutePath());
                    }
                } catch (Exception e) {
                    log.error("清理测试文件时发生异常: {}", file.getAbsolutePath(), e);
                }
            }
        }
        testFiles.clear();
    }
    
    @Test
    @DisplayName("大文件MD5计算性能测试")
    void testLargeFileMD5Performance() throws IOException {
        // 创建大文件（50MB）
        String largeFilePath = tempDir.resolve("large-file-md5-test.dat").toString();
        File largeFile = FileTransferTestUtils.createTestFile(largeFilePath, 50 * 1024 * 1024);
        testFiles.add(largeFile);
        
        // 测试MD5计算性能
        long startTime = System.currentTimeMillis();
        String md5 = FileUtils.calculateFileMD5(largeFile);
        long endTime = System.currentTimeMillis();
        
        // 验证结果
        assertNotNull(md5);
        assertEquals(32, md5.length());
        
        // 性能验证（50MB文件的MD5计算应该在合理时间内完成）
        long duration = endTime - startTime;
        System.out.printf("50MB文件MD5计算耗时: %d毫秒%n", duration);
        
        // 通常50MB文件的MD5计算应该在10秒内完成
        assertTrue(duration < 10000, "MD5计算耗时过长: " + duration + "ms");
        
        // 计算吞吐量
        double throughputMBps = (50.0 * 1000) / duration;
        System.out.printf("MD5计算吞吐量: %.2f MB/s%n", throughputMBps);
        
        // 吞吐量应该大于5MB/s
        assertTrue(throughputMBps > 5.0, "MD5计算吞吐量过低: " + throughputMBps + " MB/s");
    }
    
    @Test
    @DisplayName("并发文件创建性能测试")
    void testConcurrentFileCreationPerformance() throws Exception {
        int fileCount = 20;
        int fileSizeMB = 5;
        
        // 并发创建多个文件
        List<CompletableFuture<File>> futures = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < fileCount; i++) {
            final int fileIndex = i;
            CompletableFuture<File> future = CompletableFuture.supplyAsync(() -> {
                try {
                    String fileName = "concurrent-file-" + fileIndex + ".dat";
                    String filePath = tempDir.resolve(fileName).toString();
                    return FileTransferTestUtils.createTestFile(filePath, fileSizeMB * 1024 * 1024);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }, executorService);
            futures.add(future);
        }
        
        // 等待所有文件创建完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));
        allFutures.get(60, TimeUnit.SECONDS);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 收集创建的文件
        for (CompletableFuture<File> future : futures) {
            File file = future.get();
            testFiles.add(file);
            assertTrue(file.exists());
            assertEquals(fileSizeMB * 1024 * 1024, file.length());
        }
        
        // 性能分析
        double totalSizeMB = fileCount * fileSizeMB;
        double throughputMBps = (totalSizeMB * 1000.0) / duration;
        
        System.out.printf("并发创建%d个%dMB文件耗时: %d毫秒%n", fileCount, fileSizeMB, duration);
        System.out.printf("总数据量: %.0fMB, 吞吐量: %.2f MB/s%n", totalSizeMB, throughputMBps);
        
        // 性能验证：并发创建应该比串行创建更快
        assertTrue(duration < 30000, "并发文件创建耗时过长: " + duration + "ms");
        assertTrue(throughputMBps > 1.0, "文件创建吞吐量过低: " + throughputMBps + " MB/s");
    }
    
    @Test
    @DisplayName("限流器性能测试")
    void testRateLimiterPerformance() throws Exception {
        String rateLimiterKey = "performance-test";
        long rateLimitBytesPerSecond = 10 * 1024 * 1024; // 10MB/s
        int requestCount = 100;
        int requestSizeBytes = 1024 * 1024; // 1MB per request
        
        // 测试限流器性能
        long startTime = System.currentTimeMillis();
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (int i = 0; i < requestCount; i++) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                RateLimitUtils.applyRateLimit(rateLimiterKey, rateLimitBytesPerSecond, requestSizeBytes);
            }, executorService);
            futures.add(future);
        }
        
        // 等待所有请求完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));
        allFutures.get(60, TimeUnit.SECONDS);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 清理限流器
        RateLimitUtils.clearRateLimiter(rateLimiterKey);
        
        // 性能分析
        double totalDataMB = (requestCount * requestSizeBytes) / (1024.0 * 1024.0);
        double actualThroughputMBps = (totalDataMB * 1000.0) / duration;
        double expectedThroughputMBps = rateLimitBytesPerSecond / (1024.0 * 1024.0);
        
        System.out.printf("限流器测试 - 请求数: %d, 每请求: %dKB%n", requestCount, requestSizeBytes / 1024);
        System.out.printf("总数据量: %.2fMB, 耗时: %d毫秒%n", totalDataMB, duration);
        System.out.printf("实际吞吐量: %.2f MB/s, 期望吞吐量: %.2f MB/s%n", 
                actualThroughputMBps, expectedThroughputMBps);
        
        // 验证限流效果：实际吞吐量应该接近但不超过限制值
        assertTrue(actualThroughputMBps <= expectedThroughputMBps * 1.1, 
                "实际吞吐量超出限制: " + actualThroughputMBps + " > " + expectedThroughputMBps);
        
        // 验证限流器没有过度限制（实际吞吐量不应该太低）
        assertTrue(actualThroughputMBps >= expectedThroughputMBps * 0.8, 
                "实际吞吐量过低: " + actualThroughputMBps + " < " + expectedThroughputMBps * 0.8);
    }
    
    @Test
    @DisplayName("文件复制性能测试")
    void testFileCopyPerformance() throws IOException {
        // 创建源文件（20MB）
        String sourceFilePath = tempDir.resolve("source-copy-test.dat").toString();
        File sourceFile = FileTransferTestUtils.createTestFile(sourceFilePath, 20 * 1024 * 1024);
        testFiles.add(sourceFile);
        
        // 测试文件复制性能
        String targetFilePath = tempDir.resolve("target-copy-test.dat").toString();
        
        long startTime = System.currentTimeMillis();
        FileUtils.copyFile(sourceFilePath, targetFilePath);
        long endTime = System.currentTimeMillis();
        
        File targetFile = new File(targetFilePath);
        testFiles.add(targetFile);
        
        // 验证复制结果
        assertTrue(targetFile.exists());
        assertEquals(sourceFile.length(), targetFile.length());
        
        // 验证文件内容一致性
        String sourceMd5 = FileUtils.calculateFileMD5(sourceFile);
        String targetMd5 = FileUtils.calculateFileMD5(targetFile);
        assertEquals(sourceMd5, targetMd5);
        
        // 性能分析
        long duration = endTime - startTime;
        double fileSizeMB = sourceFile.length() / (1024.0 * 1024.0);
        double throughputMBps = (fileSizeMB * 1000.0) / duration;
        
        System.out.printf("文件复制性能 - 大小: %.2fMB, 耗时: %d毫秒, 吞吐量: %.2f MB/s%n", 
                fileSizeMB, duration, throughputMBps);
        
        // 性能验证：20MB文件复制应该在合理时间内完成
        assertTrue(duration < 5000, "文件复制耗时过长: " + duration + "ms");
        assertTrue(throughputMBps > 10.0, "文件复制吞吐量过低: " + throughputMBps + " MB/s");
    }
    
    @Test
    @DisplayName("内存使用测试")
    void testMemoryUsage() throws IOException {
        // 记录初始内存使用
        Runtime runtime = Runtime.getRuntime();
        runtime.gc(); // 强制垃圾回收
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 创建多个大文件并计算MD5
        List<String> md5Values = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            String filePath = tempDir.resolve("memory-test-" + i + ".dat").toString();
            File file = FileTransferTestUtils.createTestFile(filePath, 10 * 1024 * 1024); // 10MB
            testFiles.add(file);
            
            String md5 = FileUtils.calculateFileMD5(file);
            md5Values.add(md5);
        }
        
        // 记录峰值内存使用
        runtime.gc(); // 强制垃圾回收
        long peakMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = peakMemory - initialMemory;
        
        System.out.printf("内存使用测试 - 初始: %dMB, 峰值: %dMB, 增长: %dMB%n", 
                initialMemory / 1024 / 1024, 
                peakMemory / 1024 / 1024, 
                memoryIncrease / 1024 / 1024);
        
        // 验证MD5计算结果
        assertEquals(5, md5Values.size());
        for (String md5 : md5Values) {
            assertNotNull(md5);
            assertEquals(32, md5.length());
        }
        
        // 内存使用验证：处理50MB数据时，内存增长不应该超过100MB
        assertTrue(memoryIncrease < 100 * 1024 * 1024, 
                "内存使用过多: " + memoryIncrease / 1024 / 1024 + "MB");
    }
}
