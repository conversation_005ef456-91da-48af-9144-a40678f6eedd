package com.sdesrd.filetransfer.demo;

import com.github.f4b6a3.ulid.UlidCreator;

/**
 * 测试用ULID生成器
 * 使用专业的ULID库生成标准ULID
 * 
 */
public class TestUlidGenerator {

    /**
     * 生成测试用ULID
     * 
     * @return 标准ULID字符串
     */
    public static String generateTestUlid() {
        return UlidCreator.getUlid().toString();
    }

    /**
     * 生成指定时间戳的ULID
     * 
     * @param timestamp 时间戳（毫秒）
     * @return 标准ULID字符串
     */
    public static String generateTestUlid(long timestamp) {
        return UlidCreator.getUlid(timestamp).toString();
    }

    /**
     * 验证ULID格式是否有效
     * 
     * @param ulid ULID字符串
     * @return 是否有效
     */
    public static boolean isValidUlid(String ulid) {
        if (ulid == null || ulid.length() != 26) {
            return false;
        }
        
        // 验证字符集（Crockford's Base32）
        String validChars = "0123456789ABCDEFGHJKMNPQRSTVWXYZ";
        for (char c : ulid.toCharArray()) {
            if (validChars.indexOf(c) == -1) {
                return false;
            }
        }
        
        return true;
    }
}
