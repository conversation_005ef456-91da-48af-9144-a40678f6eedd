package com.sdesrd.filetransfer.client.dto;

import lombok.Data;

/**
 * 下载结果
 */
@Data
public class DownloadResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 文件ID
     */
    private String fileId;
    
    /**
     * 本地路径
     */
    private String localPath;
    
    /**
     * 文件大小
     */
    private Long fileSize;
    
    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建成功的下载结果
     *
     * @param localPath 本地路径
     * @param fileSize 文件大小
     * @return 下载结果
     */
    public static DownloadResult success(String localPath, long fileSize) {
        DownloadResult result = new DownloadResult();
        result.setSuccess(true);
        result.setLocalPath(localPath);
        result.setFileSize(fileSize);
        return result;
    }

    /**
     * 创建失败的下载结果
     *
     * @param errorMessage 错误信息
     * @return 下载结果
     */
    public static DownloadResult failure(String errorMessage) {
        DownloadResult result = new DownloadResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
}