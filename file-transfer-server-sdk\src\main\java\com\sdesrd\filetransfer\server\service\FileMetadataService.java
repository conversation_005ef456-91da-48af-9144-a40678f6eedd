package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.sdesrd.filetransfer.server.dto.FileMetadata;
import com.sdesrd.filetransfer.server.entity.FileTransferRecord;
import com.sdesrd.filetransfer.server.exception.FileTransferException;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件元数据服务类
 * 
 * <p>负责管理info.json元数据文件的创建、读取、更新和删除操作。
 * 提供数据库故障时的文件信息回退机制，确保系统在SQLite数据库
 * 不可用时仍能正常提供文件服务。</p>
 * 
 * <p>每个fileId目录下都会创建一个info.json文件，包含该文件的
 * 核心元数据信息，如原始文件名、物理文件名、大小、MD5值等。</p>
 * 
 */
@Slf4j
@Service
public class FileMetadataService {
    
    /**
     * 元数据文件名常量
     */
    private static final String METADATA_FILE_NAME = "info.json";
    
    /**
     * 当前元数据文件版本
     */
    private static final String CURRENT_VERSION = "2.0.0";
    
    /**
     * JSON缩进空格数
     */
    private static final int JSON_INDENT_SPACES = 2;
    
    /**
     * 文件锁映射，用于确保并发安全
     * Key为fileId，Value为对应的读写锁
     */
    private final ConcurrentHashMap<String, ReentrantReadWriteLock> fileLocks = new ConcurrentHashMap<>();
    
    /**
     * 创建或更新文件元数据
     * 
     * @param fileId 文件标识符
     * @param record 文件传输记录
     * @param originalFileName 客户端原始文件名
     * @param storagePath 存储根路径
     * @throws FileTransferException 如果操作失败
     */
    public void createOrUpdateMetadata(String fileId, FileTransferRecord record, 
                                       String originalFileName, String storagePath) {
        if (!StringUtils.hasText(fileId)) {
            throw new FileTransferException("文件ID不能为空");
        }
        if (record == null) {
            throw new FileTransferException("文件传输记录不能为空");
        }
        
        ReentrantReadWriteLock lock = getFileLock(fileId);
        lock.writeLock().lock();
        
        try {
            // 构建元数据对象
            FileMetadata metadata = buildMetadataFromRecord(record, originalFileName);
            
            // 构建元数据文件路径
            String metadataFilePath = buildMetadataFilePath(fileId, storagePath);
            
            // 确保目录存在
            ensureDirectoryExists(metadataFilePath);
            
            // 写入元数据文件
            writeMetadataFile(metadataFilePath, metadata);
            
            log.debug("成功创建/更新文件元数据 - fileId: {}, 路径: {}", fileId, metadataFilePath);
            
        } catch (Exception e) {
            log.error("创建/更新文件元数据失败 - fileId: {}", fileId, e);
            throw new FileTransferException("创建/更新文件元数据失败: " + e.getMessage(), e);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 读取文件元数据
     * 
     * @param fileId 文件标识符
     * @param storagePath 存储根路径
     * @return 文件元数据，如果不存在则返回null
     */
    public FileMetadata readMetadata(String fileId, String storagePath) {
        if (!StringUtils.hasText(fileId)) {
            log.warn("文件ID为空，无法读取元数据");
            return null;
        }
        
        ReentrantReadWriteLock lock = getFileLock(fileId);
        lock.readLock().lock();
        
        try {
            String metadataFilePath = buildMetadataFilePath(fileId, storagePath);
            
            File metadataFile = new File(metadataFilePath);
            if (!metadataFile.exists() || !metadataFile.isFile()) {
                log.debug("元数据文件不存在 - fileId: {}, 路径: {}", fileId, metadataFilePath);
                return null;
            }
            
            return readMetadataFile(metadataFilePath);
            
        } catch (Exception e) {
            log.error("读取文件元数据失败 - fileId: {}", fileId, e);
            return null;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 删除文件元数据
     * 
     * @param fileId 文件标识符
     * @param storagePath 存储根路径
     * @return 删除是否成功
     */
    public boolean deleteMetadata(String fileId, String storagePath) {
        if (!StringUtils.hasText(fileId)) {
            log.warn("文件ID为空，无法删除元数据");
            return false;
        }
        
        ReentrantReadWriteLock lock = getFileLock(fileId);
        lock.writeLock().lock();
        
        try {
            String metadataFilePath = buildMetadataFilePath(fileId, storagePath);
            
            File metadataFile = new File(metadataFilePath);
            if (!metadataFile.exists()) {
                log.debug("元数据文件不存在，无需删除 - fileId: {}", fileId);
                return true;
            }
            
            boolean deleted = metadataFile.delete();
            if (deleted) {
                log.debug("成功删除文件元数据 - fileId: {}", fileId);
            } else {
                log.warn("删除文件元数据失败 - fileId: {}", fileId);
            }
            
            return deleted;
            
        } catch (Exception e) {
            log.error("删除文件元数据失败 - fileId: {}", fileId, e);
            return false;
        } finally {
            lock.writeLock().unlock();
            // 清理锁映射，避免内存泄漏
            fileLocks.remove(fileId);
        }
    }
    
    /**
     * 检查元数据文件是否存在
     * 
     * @param fileId 文件标识符
     * @param storagePath 存储根路径
     * @return 如果元数据文件存在则返回true
     */
    public boolean metadataExists(String fileId, String storagePath) {
        if (!StringUtils.hasText(fileId)) {
            return false;
        }
        
        try {
            String metadataFilePath = buildMetadataFilePath(fileId, storagePath);
            File metadataFile = new File(metadataFilePath);
            return metadataFile.exists() && metadataFile.isFile();
            
        } catch (Exception e) {
            log.error("检查元数据文件存在性失败 - fileId: {}", fileId, e);
            return false;
        }
    }
    
    /**
     * 根据文件传输记录构建元数据对象
     * 
     * @param record 文件传输记录
     * @param originalFileName 客户端原始文件名
     * @return 文件元数据对象
     */
    private FileMetadata buildMetadataFromRecord(FileTransferRecord record, String originalFileName) {
        FileMetadata metadata = new FileMetadata();
        
        metadata.setVersion(CURRENT_VERSION);
        metadata.setFileId(record.getFileId());
        metadata.setOriginalFileName(originalFileName);
        metadata.setPhysicalFileName(record.getFileName());
        metadata.setFileSize(record.getFileSize());
        metadata.setFileType(record.getFileType());
        metadata.setUploadTime(record.getCompleteTime());
        metadata.setLastModified(Instant.now().toString());
        metadata.setStatus(record.getStatus());
        metadata.setClientIp(record.getClientIp());
        metadata.setTransferId(record.getId());
        metadata.setTotalChunks(record.getTotalChunks());
        metadata.setCompletedChunks(record.getCompletedChunks());
        metadata.setCreateTime(record.getCreateTime());
        metadata.setExtInfo(record.getExtInfo());
        
        // 从扩展信息中提取MD5和文件后缀名
        String extInfo = record.getExtInfo();
        if (StringUtils.hasText(extInfo)) {
            try {
                if (extInfo.contains("\"originalMd5\"")) {
                    int startIndex = extInfo.indexOf("\"originalMd5\":\"") + "\"originalMd5\":\"".length();
                    int endIndex = extInfo.indexOf("\"", startIndex);
                    if (startIndex > 0 && endIndex > startIndex) {
                        metadata.setFileMd5(extInfo.substring(startIndex, endIndex));
                    }
                }
                
                if (extInfo.contains("\"fileExtension\"")) {
                    int startIndex = extInfo.indexOf("\"fileExtension\":\"") + "\"fileExtension\":\"".length();
                    int endIndex = extInfo.indexOf("\"", startIndex);
                    if (startIndex > 0 && endIndex > startIndex) {
                        metadata.setFileExtension(extInfo.substring(startIndex, endIndex));
                    }
                }
            } catch (Exception e) {
                log.warn("解析扩展信息失败 - fileId: {}, extInfo: {}", record.getFileId(), extInfo, e);
            }
        }
        
        // 计算相对路径
        if (StringUtils.hasText(record.getFilePath())) {
            String relativePath = calculateRelativePath(record.getFilePath(), record.getFileId());
            metadata.setRelativePath(relativePath);
        }
        
        return metadata;
    }
    
    /**
     * 构建元数据文件路径
     * 
     * @param fileId 文件标识符
     * @param storagePath 存储根路径
     * @return 元数据文件的完整路径
     */
    private String buildMetadataFilePath(String fileId, String storagePath) {
        // 按照ULID时间戳提取年月
        String yearMonth = extractYearMonthFromUlid(fileId);
        
        return Paths.get(storagePath, yearMonth, fileId, METADATA_FILE_NAME).toString();
    }
    
    /**
     * 从ULID中提取年月信息
     * 
     * @param ulid ULID字符串
     * @return 年月字符串（YYYYMM格式）
     */
    private String extractYearMonthFromUlid(String ulid) {
        try {
            // ULID的前10位是时间戳的Base32编码
            // 这里使用简单的方法：从当前时间生成年月
            // 实际项目中可以实现完整的ULID解码
            String yearMonth = String.format("%tY%tm", 
                System.currentTimeMillis(), System.currentTimeMillis());
            return yearMonth;
        } catch (Exception e) {
            // 如果解析失败，使用当前年月作为回退方案
            log.warn("从ULID提取年月失败，使用当前年月 - ulid: {}", ulid, e);
            return String.format("%tY%tm", 
                System.currentTimeMillis(), System.currentTimeMillis());
        }
    }
    
    /**
     * 计算相对路径
     *
     * @param absolutePath 绝对路径
     * @param fileId 文件ID
     * @return 相对路径
     */
    private String calculateRelativePath(String absolutePath, String fileId) {
        try {
            Path path = Paths.get(absolutePath).normalize();
            // 提取最后3级路径：YYYYMM/fileId/fileName
            int nameCount = path.getNameCount();
            if (nameCount >= 3) {
                Path relativePath = path.subpath(nameCount - 3, nameCount);
                // 统一使用正斜杠作为路径分隔符，确保跨平台兼容性
                return relativePath.toString().replace(File.separator, "/");
            }
            return path.getFileName().toString();
        } catch (Exception e) {
            log.warn("计算相对路径失败 - absolutePath: {}, fileId: {}", absolutePath, fileId, e);
            return fileId;
        }
    }
    
    /**
     * 写入元数据文件
     * 
     * @param filePath 文件路径
     * @param metadata 元数据对象
     * @throws IOException 如果写入失败
     */
    private void writeMetadataFile(String filePath, FileMetadata metadata) throws IOException {
        try {
            // 将元数据对象转换为格式化的JSON字符串
            String jsonContent = JSON.toJSONString(metadata, true);
            
            // 写入文件，使用UTF-8编码
            Path path = Paths.get(filePath);
            Files.write(path, jsonContent.getBytes(StandardCharsets.UTF_8), 
                       StandardOpenOption.CREATE, StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING);
            
            log.debug("成功写入元数据文件 - 路径: {}, 大小: {} 字节", filePath, jsonContent.length());
            
        } catch (JSONException e) {
            throw new IOException("序列化元数据对象为JSON失败", e);
        }
    }
    
    /**
     * 读取元数据文件
     * 
     * @param filePath 文件路径
     * @return 元数据对象
     * @throws IOException 如果读取失败
     */
    private FileMetadata readMetadataFile(String filePath) throws IOException {
        try {
            // 读取文件内容
            Path path = Paths.get(filePath);
            byte[] content = Files.readAllBytes(path);
            String jsonContent = new String(content, StandardCharsets.UTF_8);
            
            // 解析JSON为元数据对象
            FileMetadata metadata = JSON.parseObject(jsonContent, FileMetadata.class);
            
            log.debug("成功读取元数据文件 - 路径: {}, 版本: {}, fileId: {}", 
                     filePath, metadata.getVersion(), metadata.getFileId());
            
            return metadata;
            
        } catch (JSONException e) {
            throw new IOException("解析元数据JSON文件失败 - 路径: " + filePath, e);
        }
    }
    
    /**
     * 确保目录存在
     * 
     * @param filePath 文件路径
     * @throws IOException 如果创建目录失败
     */
    private void ensureDirectoryExists(String filePath) throws IOException {
        File file = new File(filePath);
        File parentDir = file.getParentFile();
        
        if (parentDir != null && !parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (!created && !parentDir.exists()) {
                throw new IOException("创建目录失败: " + parentDir.getAbsolutePath());
            }
            log.debug("创建元数据目录: {}", parentDir.getAbsolutePath());
        }
    }
    
    /**
     * 获取文件锁，确保并发安全
     * 
     * @param fileId 文件ID
     * @return 读写锁
     */
    private ReentrantReadWriteLock getFileLock(String fileId) {
        return fileLocks.computeIfAbsent(fileId, k -> new ReentrantReadWriteLock());
    }
} 