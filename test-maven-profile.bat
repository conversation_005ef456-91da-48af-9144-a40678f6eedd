@echo off
REM 测试Maven Profile属性传递脚本
REM 用于验证Maven profile中的属性是否正确传递到Java运行时

echo ========================================
echo Maven Profile属性传递测试
echo ========================================
echo.

echo [测试1] 检查当前激活的Maven profile
echo ----------------------------------------
call mvn help:active-profiles -pl file-transfer-client-demo
echo.

echo [测试2] 显示有效的Maven属性
echo ----------------------------------------
call mvn help:effective-pom -pl file-transfer-client-demo -Doutput=effective-pom.xml
echo 有效POM已生成到: effective-pom.xml
echo.

echo [测试3] 使用dev profile运行演示程序（显示配置信息）
echo ----------------------------------------
echo 注意观察"配置来源调试信息"部分，确认demo.server.host的值和来源
echo.
call mvn exec:java -pl file-transfer-client-demo -Pdev -Dexec.args="--test"
echo.

echo [测试4] 手动传递系统属性运行
echo ----------------------------------------
echo 通过-D参数直接传递系统属性
call mvn exec:java -pl file-transfer-client-demo -Ddemo.server.host=************** -Ddemo.server.port=49011 -Dexec.args="--test"
echo.

echo [测试5] 检查环境变量方式
echo ----------------------------------------
set DEMO_SERVER_HOST=**************
set DEMO_SERVER_PORT=49011
echo 设置环境变量: DEMO_SERVER_HOST=%DEMO_SERVER_HOST%
echo 设置环境变量: DEMO_SERVER_PORT=%DEMO_SERVER_PORT%
call mvn exec:java -pl file-transfer-client-demo -Dexec.args="--test"
echo.

echo ========================================
echo 测试完成
echo ========================================
echo.
echo 解决方案总结:
echo 1. 使用 mvn exec:java -Pdev 运行程序
echo 2. 或者使用 -D 参数直接传递系统属性
echo 3. 或者设置对应的环境变量
echo.

pause
