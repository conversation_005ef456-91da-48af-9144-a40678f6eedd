package com.sdesrd.filetransfer.server.exception;

/**
 * 文件完整性异常
 * 当文件MD5校验失败或文件损坏时抛出此异常
 */
public class FileIntegrityException extends FileTransferException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 传输ID
     */
    private final String transferId;
    
    /**
     * 预期的MD5值
     */
    private final String expectedMd5;
    
    /**
     * 实际的MD5值
     */
    private final String actualMd5;
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param transferId 传输ID
     * @param expectedMd5 预期的MD5值
     * @param actualMd5 实际的MD5值
     */
    public FileIntegrityException(String message, String transferId, String expectedMd5, String actualMd5) {
        super(message);
        this.transferId = transferId;
        this.expectedMd5 = expectedMd5;
        this.actualMd5 = actualMd5;
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param transferId 传输ID
     * @param expectedMd5 预期的MD5值
     * @param actualMd5 实际的MD5值
     * @param cause 原因异常
     */
    public FileIntegrityException(String message, String transferId, String expectedMd5, String actualMd5, Throwable cause) {
        super(message, cause);
        this.transferId = transferId;
        this.expectedMd5 = expectedMd5;
        this.actualMd5 = actualMd5;
    }
    
    /**
     * 获取传输ID
     * 
     * @return 传输ID
     */
    public String getTransferId() {
        return transferId;
    }
    
    /**
     * 获取预期的MD5值
     * 
     * @return 预期的MD5值
     */
    public String getExpectedMd5() {
        return expectedMd5;
    }
    
    /**
     * 获取实际的MD5值
     * 
     * @return 实际的MD5值
     */
    public String getActualMd5() {
        return actualMd5;
    }
    
    @Override
    public String toString() {
        return String.format("FileIntegrityException{transferId='%s', expectedMd5='%s', actualMd5='%s', message='%s'}", 
                transferId, expectedMd5, actualMd5, getMessage());
    }
}
