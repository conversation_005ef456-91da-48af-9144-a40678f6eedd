package com.sdesrd.filetransfer.server.dto;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

/**
 * 文件元数据信息DTO
 * 用于表示info.json文件的内容结构，包含文件的核心信息
 * 
 * <p>此类定义了存储在每个fileId目录下info.json文件中的标准格式，
 * 用于在数据库故障时提供文件信息的回退机制。</p>
 * 
 */
@Data
public class FileMetadata {
    
    /**
     * 元数据文件格式版本号
     * 用于向前兼容和版本控制
     */
    @JSONField(name = "version")
    private String version = "2.0.0";
    
    /**
     * 文件标识符（ULID格式）
     */
    @JSONField(name = "fileId")
    private String fileId;
    
    /**
     * 客户端原始文件名
     * 用户上传时的原始文件名，包含完整的文件名和后缀
     */
    @JSONField(name = "originalFileName")
    private String originalFileName;
    
    /**
     * 服务端存储的物理文件名
     * 实际存储在磁盘上的文件名，格式为 md5.后缀名
     */
    @JSONField(name = "physicalFileName")
    private String physicalFileName;
    
    /**
     * 文件大小（字节）
     */
    @JSONField(name = "fileSize")
    private Long fileSize;
    
    /**
     * 文件MD5哈希值
     */
    @JSONField(name = "fileMd5")
    private String fileMd5;
    
    /**
     * 文件后缀名（不包含点号）
     */
    @JSONField(name = "fileExtension")
    private String fileExtension;
    
    /**
     * 文件MIME类型
     */
    @JSONField(name = "fileType")
    private String fileType;
    
    /**
     * 文件上传完成时间（ISO格式）
     */
    @JSONField(name = "uploadTime")
    private String uploadTime;
    
    /**
     * 最后修改时间（ISO格式）
     */
    @JSONField(name = "lastModified")
    private String lastModified;
    
    /**
     * 文件状态
     * 0-待传输，1-传输中，2-传输完成，3-传输失败，4-已暂停
     */
    @JSONField(name = "status")
    private Integer status;
    
    /**
     * 客户端IP地址
     */
    @JSONField(name = "clientIp")
    private String clientIp;
    
    /**
     * 文件相对路径
     * 相对于存储根目录的路径，格式：YYYYMM/fileId/physicalFileName
     */
    @JSONField(name = "relativePath")
    private String relativePath;
    
    /**
     * 传输ID（用于跟踪传输过程）
     */
    @JSONField(name = "transferId")
    private String transferId;
    
    /**
     * 分块总数
     */
    @JSONField(name = "totalChunks")
    private Integer totalChunks;
    
    /**
     * 已完成分块数
     */
    @JSONField(name = "completedChunks")
    private Integer completedChunks;
    
    /**
     * 创建时间（ISO格式）
     */
    @JSONField(name = "createTime")
    private String createTime;
    
    /**
     * 扩展信息（JSON格式字符串）
     * 用于存储额外的自定义信息
     */
    @JSONField(name = "extInfo")
    private String extInfo;
    
    /**
     * 校验文件状态是否完整
     * 
     * @return 如果文件状态为传输完成则返回true
     */
    public boolean isComplete() {
        return status != null && status == 2;
    }
    
    /**
     * 获取格式化的文件大小
     * 
     * @return 格式化后的文件大小字符串（如：1.5MB）
     */
    public String getFormattedSize() {
        if (fileSize == null) {
            return "0B";
        }
        return formatFileSize(fileSize);
    }
    
    /**
     * 格式化文件大小的工具方法
     * 
     * @param size 文件大小（字节）
     * @return 格式化后的大小字符串
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + "B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1fKB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1fMB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1fGB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }
} 