package com.sdesrd.filetransfer.server.config;

/**
 * 清理配置常量类
 * 
 * 定义所有与清理功能相关的常量，避免在代码中使用魔法数字
 * 
 * <AUTHOR> Transfer SDK
 * @since 1.0.0
 */
public final class CleanupConstants {
    
    // ==================== 时间相关常量 ====================
    
    /**
     * 毫秒转秒的转换因子
     */
    public static final long MILLISECONDS_TO_SECONDS = 1000L;
    
    /**
     * 秒转分钟的转换因子
     */
    public static final long SECONDS_TO_MINUTES = 60L;
    
    /**
     * 分钟转小时的转换因子
     */
    public static final long MINUTES_TO_HOURS = 60L;
    
    /**
     * 小时转天的转换因子
     */
    public static final long HOURS_TO_DAYS = 24L;
    
    /**
     * 一小时的毫秒数
     */
    public static final long ONE_HOUR_MS = MINUTES_TO_HOURS * SECONDS_TO_MINUTES * MILLISECONDS_TO_SECONDS;
    
    /**
     * 一天的毫秒数
     */
    public static final long ONE_DAY_MS = HOURS_TO_DAYS * ONE_HOUR_MS;
    
    // ==================== 清理配置默认值 ====================
    
    /**
     * 默认清理间隔时间（毫秒）- 1小时
     */
    public static final long DEFAULT_CLEANUP_INTERVAL_MS = ONE_HOUR_MS;
    
    /**
     * 默认传输记录过期时间（毫秒）- 24小时
     */
    public static final long DEFAULT_RECORD_EXPIRE_TIME_MS = ONE_DAY_MS;
    
    /**
     * 默认分块记录过期时间（毫秒）- 7天
     * 分块记录保留时间比传输记录更长，用于调试和故障排查
     */
    public static final long DEFAULT_CHUNK_EXPIRE_TIME_MS = 7L * ONE_DAY_MS;
    
    /**
     * 默认失败记录保留时间（毫秒）- 3天
     * 失败的传输记录保留更长时间用于问题分析
     */
    public static final long DEFAULT_FAILED_RECORD_RETAIN_TIME_MS = 3L * ONE_DAY_MS;
    
    // ==================== 清理任务配置 ====================
    
    /**
     * 清理任务初始延迟时间（秒）- 1分钟
     * 服务启动后等待1分钟再开始第一次清理
     */
    public static final long CLEANUP_INITIAL_DELAY_SECONDS = 60L;
    
    /**
     * 监控任务初始延迟时间（秒）- 30秒
     */
    public static final long MONITOR_INITIAL_DELAY_SECONDS = 30L;
    
    /**
     * 监控任务执行间隔（秒）- 5分钟
     */
    public static final long MONITOR_INTERVAL_SECONDS = 5L * SECONDS_TO_MINUTES;
    
    /**
     * 线程池关闭等待时间（秒）- 10秒
     */
    public static final long THREAD_POOL_SHUTDOWN_TIMEOUT_SECONDS = 10L;
    
    /**
     * 清理任务线程池大小
     */
    public static final int CLEANUP_THREAD_POOL_SIZE = 2;
    
    // ==================== 传输状态常量 ====================
    
    /**
     * 传输状态：待传输
     */
    public static final int TRANSFER_STATUS_PENDING = 0;
    
    /**
     * 传输状态：传输中
     */
    public static final int TRANSFER_STATUS_TRANSFERRING = 1;
    
    /**
     * 传输状态：传输完成
     */
    public static final int TRANSFER_STATUS_COMPLETED = 2;
    
    /**
     * 传输状态：传输失败
     */
    public static final int TRANSFER_STATUS_FAILED = 3;
    
    /**
     * 传输状态：已暂停
     */
    public static final int TRANSFER_STATUS_PAUSED = 4;
    
    // ==================== 分块状态常量 ====================
    
    /**
     * 分块状态：待传输
     */
    public static final int CHUNK_STATUS_PENDING = 0;
    
    /**
     * 分块状态：传输完成
     */
    public static final int CHUNK_STATUS_COMPLETED = 1;
    
    /**
     * 分块状态：传输失败
     */
    public static final int CHUNK_STATUS_FAILED = 2;
    
    // ==================== 清理策略常量 ====================
    
    /**
     * 卡住传输的判断时间（小时）- 2小时
     * 超过此时间未更新的传输中记录将被视为卡住
     */
    public static final long STUCK_TRANSFER_THRESHOLD_HOURS = 2L;
    
    /**
     * 卡住传输的判断时间（毫秒）
     */
    public static final long STUCK_TRANSFER_THRESHOLD_MS = STUCK_TRANSFER_THRESHOLD_HOURS * ONE_HOUR_MS;
    
    /**
     * 传输失败率警告阈值（百分比）
     */
    public static final double FAILURE_RATE_WARNING_THRESHOLD = 10.0;
    
    /**
     * 批量删除的最大记录数
     * 避免一次性删除过多记录导致数据库性能问题
     */
    public static final int MAX_BATCH_DELETE_SIZE = 1000;
    
    /**
     * 清理操作的最大重试次数
     */
    public static final int MAX_CLEANUP_RETRY_COUNT = 3;
    
    // ==================== 配置验证常量 ====================
    
    /**
     * 最小清理间隔时间（毫秒）- 5分钟
     * 防止清理任务执行过于频繁
     */
    public static final long MIN_CLEANUP_INTERVAL_MS = 5L * SECONDS_TO_MINUTES * MILLISECONDS_TO_SECONDS;
    
    /**
     * 最大清理间隔时间（毫秒）- 7天
     * 防止清理间隔过长导致数据积累
     */
    public static final long MAX_CLEANUP_INTERVAL_MS = 7L * ONE_DAY_MS;
    
    /**
     * 最小记录过期时间（毫秒）- 1小时
     * 防止记录过早被清理
     */
    public static final long MIN_RECORD_EXPIRE_TIME_MS = ONE_HOUR_MS;
    
    /**
     * 最大记录过期时间（毫秒）- 30天
     * 防止记录保留时间过长
     */
    public static final long MAX_RECORD_EXPIRE_TIME_MS = 30L * ONE_DAY_MS;
    
    // ==================== 日志和统计常量 ====================
    
    /**
     * 清理统计信息保留天数
     */
    public static final int CLEANUP_STATS_RETENTION_DAYS = 30;
    
    /**
     * 清理操作日志级别阈值
     * 清理记录数超过此值时使用INFO级别，否则使用DEBUG级别
     */
    public static final int CLEANUP_LOG_THRESHOLD = 10;
    
    /**
     * 私有构造函数，防止实例化
     */
    private CleanupConstants() {
        throw new UnsupportedOperationException("常量类不允许实例化");
    }
}
