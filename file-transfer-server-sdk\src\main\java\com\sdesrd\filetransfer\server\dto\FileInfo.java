package com.sdesrd.filetransfer.server.dto;

import lombok.Data;

/**
 * 文件信息DTO
 * 用于返回文件的详细信息
 */
@Data
public class FileInfo {
    
    /**
     * 文件ID（MD5值）
     */
    private String fileId;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 文件路径（相对于用户存储根目录）
     */
    private String relativePath;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 上传完成时间
     */
    private String uploadTime;
    
    /**
     * 最后修改时间（毫秒时间戳）
     */
    private Long lastModified;
    
    /**
     * 是否可读
     */
    private Boolean canRead;
    
    /**
     * 是否可写
     */
    private Boolean canWrite;
    
    /**
     * 格式化的文件大小（如：1.5MB）
     */
    private String formattedSize;
    
    /**
     * 文件扩展名
     */
    private String extension;
    
    /**
     * 获取格式化的文件大小
     */
    public String getFormattedSize() {
        if (fileSize == null) {
            return "0B";
        }
        return formatFileSize(fileSize);
    }
    
    /**
     * 获取文件扩展名
     */
    public String getExtension() {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + "B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1fKB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1fMB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1fGB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }
} 