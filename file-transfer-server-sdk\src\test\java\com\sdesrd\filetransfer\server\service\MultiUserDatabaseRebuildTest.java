package com.sdesrd.filetransfer.server.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.config.UserConfig;

/**
 * 多用户数据库重建功能测试
 * 验证数据库重建功能是否正确支持多用户存储路径扫描
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("多用户数据库重建功能测试")
class MultiUserDatabaseRebuildTest {

    @Mock
    private FileTransferProperties properties;
    
    @Mock
    private DatabaseFallbackService databaseFallbackService;
    
    @InjectMocks
    private FileTransferService fileTransferService;
    
    private UserConfig defaultConfig;
    private Map<String, UserConfig> users;
    
    @BeforeEach
    void setUp() {
        // 设置默认配置
        defaultConfig = new UserConfig();
        defaultConfig.setStoragePath("./data/file-transfer/files");
        
        // 设置用户配置
        users = new HashMap<>();
        
        // demo用户
        UserConfig demoConfig = new UserConfig();
        demoConfig.setSecretKey("demo-secret-key-2024");
        demoConfig.setStoragePath("./data/demo/files");
        users.put("demo", demoConfig);
        
        // admin用户
        UserConfig adminConfig = new UserConfig();
        adminConfig.setSecretKey("admin-secret-key-2024");
        adminConfig.setStoragePath("./data/admin/files");
        users.put("admin", adminConfig);
        
        // test用户（使用默认路径）
        UserConfig testConfig = new UserConfig();
        testConfig.setSecretKey("test-secret-key-2024");
        testConfig.setStoragePath("./data/file-transfer/files"); // 与默认路径相同
        users.put("test", testConfig);
        
        // 模拟配置属性 - 使用lenient模式避免UnnecessaryStubbing错误
        lenient().when(properties.getDefaultConfig()).thenReturn(defaultConfig);
        lenient().when(properties.getUsers()).thenReturn(users);
        lenient().when(properties.getDatabasePath()).thenReturn("./data/test-database.db");
    }
    
    @Test
    @DisplayName("验证多用户存储路径配置")
    void testMultiUserStoragePathConfiguration() {
        // 验证默认配置
        assertEquals("./data/file-transfer/files", defaultConfig.getStoragePath());
        
        // 验证用户配置
        assertEquals(3, users.size());
        
        // 验证demo用户配置
        UserConfig demoConfig = users.get("demo");
        assertNotNull(demoConfig);
        assertEquals("./data/demo/files", demoConfig.getStoragePath());
        assertNotEquals(defaultConfig.getStoragePath(), demoConfig.getStoragePath());
        
        // 验证admin用户配置
        UserConfig adminConfig = users.get("admin");
        assertNotNull(adminConfig);
        assertEquals("./data/admin/files", adminConfig.getStoragePath());
        assertNotEquals(defaultConfig.getStoragePath(), adminConfig.getStoragePath());
        
        // 验证test用户配置（使用默认路径）
        UserConfig testConfig = users.get("test");
        assertNotNull(testConfig);
        assertEquals("./data/file-transfer/files", testConfig.getStoragePath());
        assertEquals(defaultConfig.getStoragePath(), testConfig.getStoragePath());
    }
    
    @Test
    @DisplayName("验证数据库重建调用多用户扫描功能")
    void testDatabaseRebuildCallsMultiUserScan() {
        // 模拟DatabaseFallbackService的扫描结果
        Map<String, Object> mockScanResult = new HashMap<>();
        mockScanResult.put("success", true);
        mockScanResult.put("scannedFiles", 150);
        mockScanResult.put("rebuiltRecords", 120);
        mockScanResult.put("skippedFiles", 30);
        mockScanResult.put("errors", new java.util.ArrayList<>());
        
        // 模拟用户扫描结果
        Map<String, Object> userResults = new HashMap<>();
        
        Map<String, Object> defaultResult = new HashMap<>();
        defaultResult.put("scannedFiles", 50);
        defaultResult.put("rebuiltRecords", 40);
        defaultResult.put("skippedFiles", 10);
        defaultResult.put("pathDescription", "默认配置");
        userResults.put("default", defaultResult);
        
        Map<String, Object> demoResult = new HashMap<>();
        demoResult.put("scannedFiles", 60);
        demoResult.put("rebuiltRecords", 50);
        demoResult.put("skippedFiles", 10);
        demoResult.put("pathDescription", "用户:demo");
        userResults.put("demo", demoResult);
        
        Map<String, Object> adminResult = new HashMap<>();
        adminResult.put("scannedFiles", 40);
        adminResult.put("rebuiltRecords", 30);
        adminResult.put("skippedFiles", 10);
        adminResult.put("pathDescription", "用户:admin");
        userResults.put("admin", adminResult);
        
        mockScanResult.put("userResults", userResults);
        
        when(databaseFallbackService.scanAndRebuildFromMetadata()).thenReturn(mockScanResult);
        
        // 执行数据库重建
        Map<String, Object> result = fileTransferService.rebuildDatabase();
        
        // 验证调用了多用户扫描功能
        verify(databaseFallbackService, times(1)).scanAndRebuildFromMetadata();
        
        // 验证结果
        assertNotNull(result);
        assertTrue((Boolean) result.get("success"));
        assertEquals(150, result.get("scannedFiles"));
        assertEquals(120, result.get("rebuiltRecords"));
        assertEquals(30, result.get("skippedFiles"));
        
        // 验证用户结果
        @SuppressWarnings("unchecked")
        Map<String, Object> resultUserResults = (Map<String, Object>) result.get("userResults");
        assertNotNull(resultUserResults);
        assertEquals(3, resultUserResults.size());
        assertTrue(resultUserResults.containsKey("default"));
        assertTrue(resultUserResults.containsKey("demo"));
        assertTrue(resultUserResults.containsKey("admin"));
        
        // 验证消息包含用户详情
        String message = (String) result.get("message");
        assertNotNull(message);
        assertTrue(message.contains("多用户数据库重建完成"));
        assertTrue(message.contains("用户扫描详情"));
        assertTrue(message.contains("默认配置"));
        assertTrue(message.contains("用户:demo"));
        assertTrue(message.contains("用户:admin"));
    }
    
    @Test
    @DisplayName("验证数据库重建错误处理")
    void testDatabaseRebuildErrorHandling() {
        // 模拟扫描结果包含错误
        Map<String, Object> mockScanResult = new HashMap<>();
        mockScanResult.put("success", true);
        mockScanResult.put("scannedFiles", 100);
        mockScanResult.put("rebuiltRecords", 80);
        mockScanResult.put("skippedFiles", 20);
        
        java.util.List<String> errors = new java.util.ArrayList<>();
        errors.add("扫描用户 demo 存储路径失败: 目录不存在");
        errors.add("处理文件失败 - 用户:admin - fileId: abc123, 错误: 元数据损坏");
        mockScanResult.put("errors", errors);
        mockScanResult.put("userResults", new HashMap<>());
        
        when(databaseFallbackService.scanAndRebuildFromMetadata()).thenReturn(mockScanResult);
        
        // 执行数据库重建
        Map<String, Object> result = fileTransferService.rebuildDatabase();
        
        // 验证结果
        assertNotNull(result);
        assertTrue((Boolean) result.get("success"));
        
        // 验证错误信息
        @SuppressWarnings("unchecked")
        java.util.List<String> resultErrors = (java.util.List<String>) result.get("errors");
        assertNotNull(resultErrors);
        assertEquals(2, resultErrors.size());
        
        // 验证消息包含警告信息
        String message = (String) result.get("message");
        assertNotNull(message);
        assertTrue(message.contains("警告: 2 个错误"));
    }
    
    @Test
    @DisplayName("验证DatabaseFallbackService多用户扫描逻辑")
    void testDatabaseFallbackServiceMultiUserScanLogic() {
        // 这个测试验证DatabaseFallbackService应该如何处理多用户配置
        
        // 验证配置中有多个不同的存储路径
        Map<String, String> uniquePaths = new HashMap<>();
        uniquePaths.put("default", defaultConfig.getStoragePath());
        
        for (Map.Entry<String, UserConfig> entry : users.entrySet()) {
            String username = entry.getKey();
            String storagePath = entry.getValue().getStoragePath();
            
            // 检查是否与默认路径不同
            if (!storagePath.equals(defaultConfig.getStoragePath())) {
                uniquePaths.put(username, storagePath);
            }
        }
        
        // 应该有3个不同的路径：默认、demo、admin
        // test用户使用默认路径，所以不会单独扫描
        assertEquals(3, uniquePaths.size());
        assertTrue(uniquePaths.containsKey("default"));
        assertTrue(uniquePaths.containsKey("demo"));
        assertTrue(uniquePaths.containsKey("admin"));
        
        // 验证路径确实不同
        assertEquals("./data/file-transfer/files", uniquePaths.get("default"));
        assertEquals("./data/demo/files", uniquePaths.get("demo"));
        assertEquals("./data/admin/files", uniquePaths.get("admin"));
    }
}
