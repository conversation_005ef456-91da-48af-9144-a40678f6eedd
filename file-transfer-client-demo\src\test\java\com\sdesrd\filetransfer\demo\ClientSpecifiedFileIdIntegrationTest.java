package com.sdesrd.filetransfer.demo;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import org.junit.After;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sdesrd.filetransfer.client.FileTransferClient;
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.dto.UploadResult;
import com.sdesrd.filetransfer.client.exception.FileTransferException;
import com.sdesrd.filetransfer.client.listener.TransferListener;

/**
 * 客户端指定fileId功能集成测试
 * 专门测试v2.1.0版本的客户端指定fileId架构
 */
public class ClientSpecifiedFileIdIntegrationTest {

    /** 测试日志记录器 */
    private static final Logger log = LoggerFactory.getLogger(ClientSpecifiedFileIdIntegrationTest.class);

    /** 测试信息前缀 */
    private static final String TEST_PREFIX = "[CLIENT_SPECIFIED_FILEID_TEST]";

    /** 测试服务器地址 */
    private static final String TEST_SERVER_ADDR = "localhost";

    /** 测试服务器端口 */
    private static final int TEST_SERVER_PORT = 49011;

    /** 测试用户名 */
    private static final String TEST_USERNAME = "demo";

    /** 测试密钥 */
    private static final String TEST_SECRET_KEY = "demo-secret-key-2024";

    /** 测试上传目录 */
    private static final String TEST_UPLOAD_DIR = "test-uploads-client-specified";

    /** 测试文件大小 */
    private static final int TEST_FILE_SIZE = 1024;

    /** 文件传输客户端 */
    private FileTransferClient client;

    /** 测试监听器 */
    private TransferListener testListener;

    @Before
    public void setUp() throws Exception {
        log.info("{} 初始化客户端指定fileId集成测试环境", TEST_PREFIX);

        // 创建测试目录
        createTestDirectories();

        // 创建客户端配置
        ClientConfig config = ClientConfigBuilder.create()
                .serverAddr(TEST_SERVER_ADDR)
                .serverPort(TEST_SERVER_PORT)
                .contextPath("filetransfer")
                .user(TEST_USERNAME)
                .secretKey(TEST_SECRET_KEY)
                .chunkSize(512 * 1024) // 512KB
                .maxConcurrentTransfers(2)
                .connectTimeout(30)
                .readTimeout(60)
                .build();

        // 创建客户端
        client = new FileTransferClient(config);

        // 创建测试监听器
        testListener = new TransferListener() {
            @Override
            public void onStart(com.sdesrd.filetransfer.client.dto.TransferProgress progress) {
                log.info("{} 传输开始 - 文件: {}", TEST_PREFIX, progress.getFileName());
            }

            @Override
            public void onProgress(com.sdesrd.filetransfer.client.dto.TransferProgress progress) {
                log.debug("{} 传输进度 - 文件: {}, 进度: {:.1f}%", 
                    TEST_PREFIX, progress.getFileName(), progress.getProgress());
            }

            @Override
            public void onCompleted(com.sdesrd.filetransfer.client.dto.TransferProgress progress) {
                log.info("{} 传输完成 - 文件: {}", TEST_PREFIX, progress.getFileName());
            }

            @Override
            public void onError(com.sdesrd.filetransfer.client.dto.TransferProgress progress, Throwable error) {
                log.error("{} 传输失败 - 文件: {}", TEST_PREFIX, progress.getFileName(), error);
            }
        };

        log.info("{} 测试环境初始化完成", TEST_PREFIX);
    }

    @After
    public void tearDown() {
        log.info("{} 清理测试环境", TEST_PREFIX);

        if (client != null) {
            try {
                client.close();
            } catch (Exception e) {
                log.warn("{} 关闭客户端时发生错误: {}", TEST_PREFIX, e.getMessage());
            }
        }

        // 清理测试文件
        cleanupTestFiles();

        log.info("{} 测试环境清理完成", TEST_PREFIX);
    }

    /**
     * 测试客户端指定有效fileId的正常上传
     */
    @Test
    public void testClientSpecifiedValidFileId() throws Exception {
        log.info("{} 开始测试客户端指定有效fileId的正常上传", TEST_PREFIX);

        // 创建测试文件
        File testFile = createTestFile("valid-fileid-test.txt", TEST_FILE_SIZE);

        // 生成有效的ULID格式fileId
        String clientFileId = TestUlidGenerator.generateTestUlid();
        log.info("{} 生成客户端fileId: {}", TEST_PREFIX, clientFileId);

        // 验证ULID格式
        assertTrue("生成的fileId应该是有效的ULID格式", TestUlidGenerator.isValidUlid(clientFileId));

        // 执行上传
        UploadResult result = client.uploadFileSyncWithId(
            testFile.getAbsolutePath(), clientFileId, testListener);

        // 验证结果
        assertNotNull("上传结果不应为空", result);
        assertTrue("上传应该成功", result.isSuccess());
        assertEquals("返回的fileId应该与客户端指定的一致", clientFileId, result.getFileId());
        assertEquals("文件大小应该匹配", (long) testFile.length(), (long) result.getFileSize());

        log.info("{} 客户端指定有效fileId上传测试完成 - fileId: {}", TEST_PREFIX, clientFileId);
    }

    /**
     * 测试客户端指定无效fileId格式
     */
    @Test
    public void testClientSpecifiedInvalidFileId() {
        log.info("{} 开始测试客户端指定无效fileId格式", TEST_PREFIX);

        // 创建测试文件
        File testFile = null;
        try {
            testFile = createTestFile("invalid-fileid-test.txt", TEST_FILE_SIZE);
        } catch (IOException e) {
            fail("创建测试文件失败: " + e.getMessage());
        }

        // 测试各种无效的fileId格式
        String[] invalidFileIds = {
            "invalid-format",           // 非ULID格式
            "01ARZ3NDEKTSV4RRFFQ69G5FA", // 25字符（长度不足）
            "01ARZ3NDEKTSV4RRFFQ69G5FAVX", // 27字符（长度过长）
            "01ARZ3NDEKTSV4RRFFQ69G5FAI", // 包含无效字符I
            "01ARZ3NDEKTSV4RRFFQ69G5FAL", // 包含无效字符L
            "01ARZ3NDEKTSV4RRFFQ69G5FAO", // 包含无效字符O
            "01ARZ3NDEKTSV4RRFFQ69G5FAU"  // 包含无效字符U
        };

        for (String invalidFileId : invalidFileIds) {
            log.info("{} 测试无效fileId: {}", TEST_PREFIX, invalidFileId);
            
            try {
                client.uploadFileSyncWithId(testFile.getAbsolutePath(), invalidFileId, testListener);
                fail("无效fileId应该被拒绝: " + invalidFileId);
            } catch (FileTransferException e) {
                log.info("{} [EXPECTED_EXCEPTION_TEST] 无效fileId被正确拒绝: {} - {}", 
                    TEST_PREFIX, invalidFileId, e.getMessage());
                assertTrue("异常信息应该包含格式错误说明", 
                    e.getMessage().contains("格式") || e.getMessage().contains("无效"));
            }
        }

        log.info("{} 客户端指定无效fileId格式测试完成", TEST_PREFIX);
    }

    /**
     * 测试fileId冲突检测 - 相同fileId和MD5（应该秒传）
     */
    @Test
    public void testFileIdConflictWithSameMd5() throws Exception {
        log.info("{} 开始测试fileId冲突检测 - 相同MD5", TEST_PREFIX);

        // 创建测试文件
        File testFile = createTestFile("conflict-same-md5.txt", TEST_FILE_SIZE);
        String clientFileId = TestUlidGenerator.generateTestUlid();

        // 第一次上传
        log.info("{} 第一次上传 - fileId: {}", TEST_PREFIX, clientFileId);
        UploadResult firstResult = client.uploadFileSyncWithId(
            testFile.getAbsolutePath(), clientFileId, testListener);

        assertTrue("第一次上传应该成功", firstResult.isSuccess());
        assertEquals("返回的fileId应该一致", clientFileId, firstResult.getFileId());

        // 第二次上传相同文件和相同fileId（应该秒传）
        log.info("{} 第二次上传相同文件和fileId - 期望秒传", TEST_PREFIX);
        UploadResult secondResult = client.uploadFileSyncWithId(
            testFile.getAbsolutePath(), clientFileId, testListener);

        assertTrue("第二次上传应该成功", secondResult.isSuccess());
        assertEquals("返回的fileId应该一致", clientFileId, secondResult.getFileId());
        assertTrue("应该触发秒传", secondResult.isFastUpload());

        log.info("{} fileId冲突检测（相同MD5）测试完成", TEST_PREFIX);
    }

    /**
     * 测试fileId冲突检测 - 相同fileId但不同MD5（应该被拒绝）
     */
    @Test
    public void testFileIdConflictWithDifferentMd5() throws Exception {
        log.info("{} 开始测试fileId冲突检测 - 不同MD5", TEST_PREFIX);

        // 创建两个不同内容的测试文件
        File testFile1 = createTestFile("conflict-diff-md5-1.txt", TEST_FILE_SIZE);
        File testFile2 = createTestFile("conflict-diff-md5-2.txt", TEST_FILE_SIZE + 100); // 不同大小确保MD5不同

        String clientFileId = TestUlidGenerator.generateTestUlid();

        // 第一次上传
        log.info("{} 第一次上传 - fileId: {}", TEST_PREFIX, clientFileId);
        UploadResult firstResult = client.uploadFileSyncWithId(
            testFile1.getAbsolutePath(), clientFileId, testListener);

        assertTrue("第一次上传应该成功", firstResult.isSuccess());

        // 第二次上传不同文件但相同fileId（应该被拒绝）
        log.info("{} 第二次上传不同文件但相同fileId - 期望被拒绝", TEST_PREFIX);
        try {
            client.uploadFileSyncWithId(testFile2.getAbsolutePath(), clientFileId, testListener);
            fail("相同fileId但不同MD5的上传应该被拒绝");
        } catch (FileTransferException e) {
            log.info("{} [EXPECTED_EXCEPTION_TEST] fileId冲突被正确检测: {}", TEST_PREFIX, e.getMessage());
            assertTrue("异常信息应该包含冲突说明", 
                e.getMessage().contains("冲突") || e.getMessage().contains("已存在") || e.getMessage().contains("不同"));
        }

        log.info("{} fileId冲突检测（不同MD5）测试完成", TEST_PREFIX);
    }

    /**
     * 创建测试目录
     */
    private void createTestDirectories() throws IOException {
        Files.createDirectories(Paths.get(TEST_UPLOAD_DIR));
        log.info("{} 创建测试目录: {}", TEST_PREFIX, TEST_UPLOAD_DIR);
    }

    /**
     * 创建测试文件
     */
    private File createTestFile(String fileName, int size) throws IOException {
        File file = new File(TEST_UPLOAD_DIR, fileName);
        byte[] data = new byte[size];
        
        // 填充不同的测试数据确保MD5不同
        for (int i = 0; i < size; i++) {
            data[i] = (byte) ((i + fileName.hashCode()) % 256);
        }
        
        Files.write(file.toPath(), data);
        log.info("{} 创建测试文件: {} ({}字节)", TEST_PREFIX, file.getAbsolutePath(), size);
        return file;
    }

    /**
     * 清理测试文件
     */
    private void cleanupTestFiles() {
        try {
            deleteDirectory(new File(TEST_UPLOAD_DIR));
            log.info("{} 清理测试文件完成", TEST_PREFIX);
        } catch (Exception e) {
            log.warn("{} 清理测试文件时发生错误: {}", TEST_PREFIX, e.getMessage());
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
}
