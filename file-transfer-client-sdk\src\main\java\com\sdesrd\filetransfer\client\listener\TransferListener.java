package com.sdesrd.filetransfer.client.listener;

import com.sdesrd.filetransfer.client.dto.TransferProgress;

/**
 * 传输监听器接口
 */
public interface TransferListener {
    
    /**
     * 传输开始
     * 
     * @param progress 传输进度信息
     */
    default void onStart(TransferProgress progress) {
    }
    
    /**
     * 传输进度更新
     * 
     * @param progress 传输进度信息
     */
    void onProgress(TransferProgress progress);
    
    /**
     * 传输完成
     * 
     * @param progress 传输进度信息
     */
    default void onCompleted(TransferProgress progress) {
    }
    
    /**
     * 传输失败
     * 
     * @param progress 传输进度信息
     * @param error    错误信息
     */
    default void onError(TransferProgress progress, Throwable error) {
    }
    
    /**
     * 传输暂停
     * 
     * @param progress 传输进度信息
     */
    default void onPaused(TransferProgress progress) {
    }
} 