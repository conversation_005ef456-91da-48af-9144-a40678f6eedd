package com.sdesrd.filetransfer.client.config;

import lombok.Data;

/**
 * 客户端认证配置
 */
@Data
public class ClientAuthConfig {

    /**
     * 服务器地址，默认localhost
     */
    private String serverAddr = "localhost";

    /**
     * 服务器端口，默认49011
     */
    private int serverPort = 49011;

    /**
     * 服务器上下文路径，默认为"filetransfer"
     * 与服务端的server.servlet.context-path配置保持一致
     * 例如：如果服务部署在 /filetransfer 下，则设置为 "filetransfer"
     * 注意：不要包含开头和结尾的斜杠
     */
    private String contextPath = "filetransfer";

    /**
     * 是否使用HTTPS协议，默认false
     */
    private boolean useHttps = false;

    /**
     * 用户名（必填）
     */
    private String user;

    /**
     * 用户密钥（必填）
     */
    private String secretKey;

    /**
     * 获取完整的服务器URL
     *
     * @return 格式化后的服务器基础URL
     */
    public String getServerUrl() {
        // 验证必要参数
        if (serverAddr == null || serverAddr.trim().isEmpty()) {
            throw new IllegalStateException("服务器地址不能为空");
        }
        if (serverPort <= 0 || serverPort > 65535) {
            throw new IllegalStateException("服务器端口必须在1-65535范围内");
        }

        // 确保contextPath格式正确
        String path = normalizeContextPath(contextPath);

        // 选择协议
        String protocol = useHttps ? "https" : "http";

        // 构建基础URL
        String baseUrl = String.format("%s://%s:%d", protocol, serverAddr.trim(), serverPort);

        // 如果有contextPath，则添加
        if (!path.isEmpty()) {
            baseUrl += "/" + path;
        }

        return baseUrl;
    }

    /**
     * 标准化上下文路径
     *
     * @param contextPath 原始上下文路径
     * @return 标准化后的上下文路径
     */
    private String normalizeContextPath(String contextPath) {
        if (contextPath == null) {
            return "";
        }

        String path = contextPath.trim();

        // 移除开头的斜杠
        while (path.startsWith("/")) {
            path = path.substring(1);
        }

        // 移除结尾的斜杠
        while (path.endsWith("/")) {
            path = path.substring(0, path.length() - 1);
        }

        return path;
    }

    /**
     * 验证配置的完整性
     *
     * @throws IllegalStateException 如果配置不完整或无效
     */
    public void validate() {
        if (serverAddr == null || serverAddr.trim().isEmpty()) {
            throw new IllegalStateException("服务器地址不能为空");
        }
        if (serverPort <= 0 || serverPort > 65535) {
            throw new IllegalStateException("服务器端口必须在1-65535范围内");
        }
        if (user == null || user.trim().isEmpty()) {
            throw new IllegalStateException("用户名不能为空");
        }
        if (secretKey == null || secretKey.trim().isEmpty()) {
            throw new IllegalStateException("用户密钥不能为空");
        }
    }
}