package com.sdesrd.filetransfer.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 文件传输记录实体类
 * 适配SQLite数据库
 */
@Data
@TableName("file_transfer_record")
public class FileTransferRecord {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 文件标识符（MD5或SHA256）
     */
    @TableField("file_id")
    private String fileId;
    
    /**
     * 服务端存储的文件名（物理文件名）
     */
    @TableField("file_name")
    private String fileName;
    
    /**
     * 客户端原始文件名
     * 用户上传时的原始文件名，包含完整的文件名和后缀
     */
    @TableField("original_file_name")
    private String originalFileName;
    
    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;
    
    /**
     * 文件存储路径
     */
    @TableField("file_path")
    private String filePath;
    
    /**
     * 已传输大小（字节）
     */
    @TableField("transferred_size")
    private Long transferredSize;
    
    /**
     * 分块总数
     */
    @TableField("total_chunks")
    private Integer totalChunks;
    
    /**
     * 已完成分块数
     */
    @TableField("completed_chunks")
    private Integer completedChunks;
    
    /**
     * 传输状态：0-待传输，1-传输中，2-传输完成，3-传输失败，4-已暂停
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 客户端IP地址
     */
    @TableField("client_ip")
    private String clientIp;
    
    /**
     * 创建时间（ISO格式字符串）
     */
    @TableField("create_time")
    private String createTime;
    
    /**
     * 更新时间（ISO格式字符串）
     */
    @TableField("update_time")
    private String updateTime;
    
    /**
     * 完成时间（ISO格式字符串）
     */
    @TableField("complete_time")
    private String completeTime;
    
    /**
     * 失败原因
     */
    @TableField("fail_reason")
    private String failReason;
    
    /**
     * 文件类型
     */
    @TableField("file_type")
    private String fileType;
    
    /**
     * 扩展信息（JSON格式）
     */
    @TableField("ext_info")
    private String extInfo;
} 