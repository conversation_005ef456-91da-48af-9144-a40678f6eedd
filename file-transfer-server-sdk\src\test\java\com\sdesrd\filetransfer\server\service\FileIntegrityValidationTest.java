package com.sdesrd.filetransfer.server.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.config.UserConfig;
import com.sdesrd.filetransfer.server.entity.FileTransferRecord;
import com.sdesrd.filetransfer.server.exception.FileIntegrityException;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;
import com.sdesrd.filetransfer.server.util.FileUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件完整性校验测试
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@DisplayName("文件完整性校验测试")
class FileIntegrityValidationTest {
    
    private static final String TEST_INFO_PREFIX = "[测试信息]";
    private static final String EXPECTED_EXCEPTION_PREFIX = "[预期异常测试]";
    
    @Mock
    private FileTransferRecordMapper transferRecordMapper;
    
    @Mock
    private FileTransferProperties properties;
    
    @Mock
    private DatabaseFallbackService databaseFallbackService;
    
    @InjectMocks
    private FileTransferService fileTransferService;
    
    @TempDir
    Path tempDir;
    
    private UserConfig testUserConfig;
    private FileTransferRecord testRecord;
    
    @BeforeEach
    void setUp() {
        // 创建测试用户配置
        testUserConfig = new UserConfig();
        testUserConfig.setSecretKey("test-secret");
        testUserConfig.setStoragePath(tempDir.toString());
        testUserConfig.setUploadRateLimit(10485760L);
        testUserConfig.setDownloadRateLimit(10485760L);
        testUserConfig.setDefaultChunkSize(1048576L);
        testUserConfig.setMaxFileSize(104857600L);
        testUserConfig.setMaxInMemorySize(10485760L);
        testUserConfig.setFastUploadEnabled(true);
        testUserConfig.setRateLimitEnabled(false);
        
        // 创建测试传输记录
        testRecord = new FileTransferRecord();
        testRecord.setId("test-transfer-123"); // 使用id字段作为传输ID
        testRecord.setOriginalFileName("test.txt");
        testRecord.setFileSize(1024L);
        testRecord.setStatus(1); // 1表示传输中
        testRecord.setFilePath(tempDir.resolve("test.txt").toString());
        
        // 设置包含MD5的扩展信息（JSON格式字符串）
        testRecord.setExtInfo("{\"originalMd5\":\"correct-md5-hash\"}");
    }
    
    @Test
    @DisplayName("MD5校验成功测试")
    void testMd5ValidationSuccess() throws Exception {
        log.info("{} 开始测试MD5校验成功场景", TEST_INFO_PREFIX);
        
        // 创建测试文件
        Path testFile = tempDir.resolve("test.txt");
        Files.write(testFile, "test content".getBytes());
        
        String correctMd5 = "correct-md5-hash";
        
        try (MockedStatic<FileUtils> mockedFileUtils = mockStatic(FileUtils.class)) {
            // 模拟MD5计算返回正确值
            mockedFileUtils.when(() -> FileUtils.calculateFileMD5(any(File.class)))
                    .thenReturn(correctMd5);
            
            // 模拟属性配置（使用lenient模式避免UnnecessaryStubbing错误）
            lenient().when(properties.getUserConfig("testuser")).thenReturn(testUserConfig);
            
            // 执行完成上传（这里简化测试，直接调用内部方法）
            // 由于completeUpload是复杂方法，我们测试其中的MD5验证逻辑
            boolean isValid = validateFileMd5(testRecord, testFile.toFile(), correctMd5);
            
            assertTrue(isValid, "MD5校验应该成功");
            log.info("{} MD5校验成功测试通过", TEST_INFO_PREFIX);
        }
    }
    
    @Test
    @DisplayName("MD5校验失败测试 - 应该抛出FileIntegrityException")
    void testMd5ValidationFailure() throws Exception {
        log.info("{} 开始测试MD5校验失败场景 - 预期结果：抛出FileIntegrityException", EXPECTED_EXCEPTION_PREFIX);
        
        // 创建测试文件
        Path testFile = tempDir.resolve("test.txt");
        Files.write(testFile, "test content".getBytes());
        
        String expectedMd5 = "correct-md5-hash";
        String actualMd5 = "wrong-md5-hash";
        
        try (MockedStatic<FileUtils> mockedFileUtils = mockStatic(FileUtils.class)) {
            // 模拟MD5计算返回错误值
            mockedFileUtils.when(() -> FileUtils.calculateFileMD5(any(File.class)))
                    .thenReturn(actualMd5);
            
            // 模拟属性配置（使用lenient模式避免UnnecessaryStubbing错误）
            lenient().when(properties.getUserConfig("testuser")).thenReturn(testUserConfig);
            
            // 执行测试并验证异常
            FileIntegrityException exception = assertThrows(
                    FileIntegrityException.class,
                    () -> validateFileMd5WithException(testRecord, testFile.toFile(), expectedMd5),
                    "MD5校验失败应该抛出FileIntegrityException"
            );
            
            // 验证异常详细信息
            assertEquals(testRecord.getId(), exception.getTransferId());
            assertEquals(expectedMd5, exception.getExpectedMd5());
            assertEquals(actualMd5, exception.getActualMd5());
            assertTrue(exception.getMessage().contains("文件完整性校验失败"));
            
            log.info("{} MD5校验失败正确抛出异常: {}", TEST_INFO_PREFIX, exception.getMessage());
        }
    }
    
    @Test
    @DisplayName("MD5校验失败后的清理测试")
    void testCleanupAfterMd5ValidationFailure() throws Exception {
        log.info("{} 开始测试MD5校验失败后的文件清理", TEST_INFO_PREFIX);
        
        // 创建测试文件和目录结构
        Path fileDir = tempDir.resolve("202412").resolve("test-file-id");
        Files.createDirectories(fileDir);
        Path testFile = fileDir.resolve("test.txt");
        Files.write(testFile, "test content".getBytes());
        
        assertTrue(Files.exists(testFile), "测试文件应该存在");
        assertTrue(Files.exists(fileDir), "测试目录应该存在");
        
        String expectedMd5 = "correct-md5-hash";
        String actualMd5 = "wrong-md5-hash";
        
        testRecord.setFilePath(testFile.toString());
        
        try (MockedStatic<FileUtils> mockedFileUtils = mockStatic(FileUtils.class)) {
            // 模拟MD5计算返回错误值
            mockedFileUtils.when(() -> FileUtils.calculateFileMD5(any(File.class)))
                    .thenReturn(actualMd5);
            
            // 模拟属性配置（使用lenient模式避免UnnecessaryStubbing错误）
            lenient().when(properties.getUserConfig("testuser")).thenReturn(testUserConfig);
            
            // 执行测试
            assertThrows(
                    FileIntegrityException.class,
                    () -> validateFileMd5WithExceptionAndCleanup(testRecord, testFile.toFile(), expectedMd5)
            );
            
            // 验证文件和目录已被清理
            assertFalse(Files.exists(testFile), "损坏的文件应该被删除");
            // 注意：目录清理取决于具体实现，这里主要验证文件被删除
            
            log.info("{} MD5校验失败后的文件清理测试通过", TEST_INFO_PREFIX);
        }
    }
    
    @Test
    @DisplayName("传输状态更新测试 - MD5校验失败")
    void testTransferStatusUpdateOnMd5Failure() throws Exception {
        log.info("{} 开始测试MD5校验失败时的传输状态更新", TEST_INFO_PREFIX);
        
        // 创建测试文件
        Path testFile = tempDir.resolve("test.txt");
        Files.write(testFile, "test content".getBytes());
        
        String expectedMd5 = "correct-md5-hash";
        String actualMd5 = "wrong-md5-hash";
        
        try (MockedStatic<FileUtils> mockedFileUtils = mockStatic(FileUtils.class)) {
            // 模拟MD5计算返回错误值
            mockedFileUtils.when(() -> FileUtils.calculateFileMD5(any(File.class)))
                    .thenReturn(actualMd5);
            
            // 模拟属性配置（使用lenient模式避免UnnecessaryStubbing错误）
            lenient().when(properties.getUserConfig("testuser")).thenReturn(testUserConfig);
            
            // 执行测试
            assertThrows(
                    FileIntegrityException.class,
                    () -> validateFileMd5WithStatusUpdate(testRecord, testFile.toFile(), expectedMd5)
            );
            
            // 验证传输状态被更新为失败（3表示传输失败）
            assertEquals(Integer.valueOf(3), testRecord.getStatus());
            assertTrue(testRecord.getFailReason().contains("文件完整性校验失败"));
            
            // 验证数据库更新被调用
            verify(transferRecordMapper).updateById(testRecord);
            
            log.info("{} 传输状态更新测试通过", TEST_INFO_PREFIX);
        }
    }
    
    // 辅助方法：模拟MD5验证逻辑（成功场景）
    private boolean validateFileMd5(FileTransferRecord record, File file, String expectedMd5) throws Exception {
        String actualMd5 = FileUtils.calculateFileMD5(file);
        return expectedMd5.equals(actualMd5);
    }
    
    // 辅助方法：模拟MD5验证逻辑（失败场景，抛出异常）
    private void validateFileMd5WithException(FileTransferRecord record, File file, String expectedMd5) throws Exception {
        String actualMd5 = FileUtils.calculateFileMD5(file);
        if (!expectedMd5.equals(actualMd5)) {
            throw new FileIntegrityException(
                    String.format("文件完整性校验失败：预期MD5=%s，实际MD5=%s", expectedMd5, actualMd5),
                    record.getId(), expectedMd5, actualMd5);
        }
    }
    
    // 辅助方法：模拟MD5验证逻辑（失败场景，包含清理）
    private void validateFileMd5WithExceptionAndCleanup(FileTransferRecord record, File file, String expectedMd5) throws Exception {
        String actualMd5 = FileUtils.calculateFileMD5(file);
        if (!expectedMd5.equals(actualMd5)) {
            // 清理损坏的文件
            if (file.exists()) {
                file.delete();
            }
            
            throw new FileIntegrityException(
                    String.format("文件完整性校验失败：预期MD5=%s，实际MD5=%s", expectedMd5, actualMd5),
                    record.getId(), expectedMd5, actualMd5);
        }
    }
    
    // 辅助方法：模拟MD5验证逻辑（失败场景，包含状态更新）
    private void validateFileMd5WithStatusUpdate(FileTransferRecord record, File file, String expectedMd5) throws Exception {
        String actualMd5 = FileUtils.calculateFileMD5(file);
        if (!expectedMd5.equals(actualMd5)) {
            // 更新传输状态（3表示传输失败）
            record.setStatus(3);
            record.setFailReason("文件完整性校验失败：MD5不匹配");
            transferRecordMapper.updateById(record);
            
            throw new FileIntegrityException(
                    String.format("文件完整性校验失败：预期MD5=%s，实际MD5=%s", expectedMd5, actualMd5),
                    record.getId(), expectedMd5, actualMd5);
        }
    }
}
