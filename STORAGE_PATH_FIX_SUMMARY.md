# 用户存储路径配置问题修复总结

## 🔍 问题分析

### 问题描述
在 `file-transfer-server.yml` 配置文件中为 demo 用户设置了 `storage-path: "./data/demo/files"`，但是这个文件存储路径配置没有生效，文件仍然存储在默认路径中。

### 根本原因
通过代码分析发现，`FileTransferService` 中的多个关键方法都硬编码使用了**默认配置的存储路径**，而没有根据当前用户获取对应的用户配置存储路径。

### 🚨 问题位置

#### 1. 文件路径构建方法
```java
// 问题代码 - 总是使用默认存储路径
private String buildNewFilePath(String fileId, String fileName) {
    String basePath = properties.getDefaultConfig().getStoragePath(); // ❌ 硬编码使用默认路径
    // ...
}
```

#### 2. 文件查找方法
```java
// 问题代码 - 查找文件时使用默认存储路径
private String tryFindFileByNewPathRule(String fileId) {
    String basePath = properties.getDefaultConfig().getStoragePath(); // ❌ 硬编码使用默认路径
    // ...
}
```

#### 3. 元数据创建
```java
// 问题代码 - 创建元数据时使用默认存储路径
String storagePath = properties.getDefaultConfig().getStoragePath(); // ❌ 硬编码使用默认路径
metadataService.createOrUpdateMetadata(record.getFileId(), record, 
                                       record.getOriginalFileName(), storagePath);
```

#### 4. 相对路径计算
```java
// 问题代码 - 计算相对路径时使用默认存储路径
String basePath = properties.getDefaultConfig().getStoragePath(); // ❌ 硬编码使用默认路径
String relativePath = calculateRelativePath(record.getFilePath(), basePath);
```

## ✅ 修复方案

### 1. 新增用户路径构建方法
```java
/**
 * 根据用户配置构建新的文件路径
 * 新的存储规则：${user-storage-path}/YYYYMM/fileId（ULID）/md5.{后缀名}
 */
private String buildNewFilePathForUser(String fileId, String fileName, String username) {
    // 获取用户配置的存储路径
    UserConfig userConfig = authService.getUserConfig(username);
    String basePath = userConfig.getStoragePath(); // ✅ 使用用户配置的存储路径
    
    // 从ULID提取年月信息
    String yearMonth = UlidUtils.extractYearMonth(fileId);
    if (yearMonth == null) {
        yearMonth = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMM"));
    }
    
    // 构建路径: user-storage-path/YYYYMM/fileId/fileName
    String safeName = sanitizeFileName(fileName);
    String fullPath = Paths.get(basePath, yearMonth, fileId, safeName).toString();
    
    log.debug("为用户 {} 构建文件路径: {}", username, fullPath);
    return fullPath;
}
```

### 2. 新增用户文件查找方法
```java
/**
 * 根据用户配置的存储路径查找文件
 */
private String tryFindFileByUserPathRule(String fileId, String username) {
    // 获取用户配置的存储路径
    UserConfig userConfig = authService.getUserConfig(username);
    String basePath = userConfig.getStoragePath(); // ✅ 使用用户配置的存储路径
    
    // 查找逻辑...
}
```

### 3. 修改文件上传初始化
```java
// 修复前
String filePath = buildNewFilePath(fileId, finalFileName);

// 修复后
String filePath = buildNewFilePathForUser(fileId, finalFileName, username); // ✅ 使用用户路径
```

### 4. 修改文件查找逻辑
```java
// 修复前
String filePath = tryFindFileByPathRule(fileId);

// 修复后
String filePath = tryFindFileByUserPathRule(fileId, username); // ✅ 使用用户路径查找
```

### 5. 修改元数据创建
```java
// 修复前
String storagePath = properties.getDefaultConfig().getStoragePath();

// 修复后
UserConfig userConfig = authService.getUserConfig(username);
String storagePath = userConfig.getStoragePath(); // ✅ 使用用户配置的存储路径
```

### 6. 修改相对路径计算
```java
// 修复前
String basePath = properties.getDefaultConfig().getStoragePath();

// 修复后
UserConfig userConfig = authService.getUserConfig(username);
String basePath = userConfig.getStoragePath(); // ✅ 使用用户配置的存储路径
```

### 7. 修改数据库回退服务
```java
// 在 DatabaseFallbackService 中
// 修复前
String storagePath = properties.getDefaultConfig().getStoragePath();

// 修复后
UserConfig userConfig = properties.getUserConfig(username);
String storagePath = userConfig.getStoragePath(); // ✅ 使用用户配置的存储路径
```

## 📋 影响范围

### 已修复的功能
1. ✅ **文件上传** - 现在使用用户配置的存储路径
2. ✅ **文件下载** - 现在从用户配置的存储路径查找文件
3. ✅ **元数据创建** - 现在在用户配置的存储路径创建 info.json
4. ✅ **相对路径计算** - 现在基于用户配置的存储路径计算
5. ✅ **数据库回退机制** - 现在使用用户配置的存储路径

### 需要注意的功能
1. ⚠️ **数据库重建功能** - 目前仍基于默认存储路径扫描，在多用户环境下需要进一步优化

## 🧪 验证方法

### 1. 配置验证
确认 `file-transfer-server.yml` 中的用户配置：
```yaml
file:
  transfer:
    server:
      users:
        demo:
          secret-key: "demo-secret-key-2024"
          storage-path: "./data/demo/files"  # 用户专用存储路径
```

### 2. 功能测试
1. 使用 demo 用户上传文件
2. 检查文件是否存储在 `./data/demo/files/YYYYMM/fileId/` 目录下
3. 使用 demo 用户下载文件，验证能否正确找到文件
4. 检查 info.json 元数据文件是否在正确的用户目录下

### 3. 单元测试
运行新增的测试类：
```bash
mvn test -Dtest=UserStoragePathTest
```

## 🎯 预期效果

修复后，demo 用户的文件将按以下结构存储：
```
./data/demo/files/
├── 202401/
│   └── 01HN8X9K2M3P4Q5R6S7T8U9V0W/
│       ├── abc123def456.txt
│       └── info.json
└── 202402/
    └── 01HN8X9K2M3P4Q5R6S7T8U9V0X/
        ├── def456ghi789.pdf
        └── info.json
```

而不是之前的默认路径：
```
./data/file-transfer/files/
```

## 📝 总结

这个问题的根本原因是代码中多处硬编码使用默认配置的存储路径，而没有考虑用户级别的配置。通过系统性地修改这些方法，现在用户配置的 `storage-path` 能够正确生效，实现了真正的多用户独立存储。
