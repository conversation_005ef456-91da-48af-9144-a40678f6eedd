package com.sdesrd.filetransfer.server.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sdesrd.filetransfer.server.entity.FileDownloadLog;

/**
 * 文件下载日志数据访问层
 * 
 * <AUTHOR> Transfer SDK
 * @since 1.0.0
 */
@Mapper
public interface FileDownloadLogMapper extends BaseMapper<FileDownloadLog> {
    
    /**
     * 根据用户名统计下载次数
     * 
     * @param username 用户名
     * @return 下载次数
     */
    @Select("SELECT COUNT(*) FROM file_download_log WHERE username = #{username}")
    long countByUsername(@Param("username") String username);
    
    /**
     * 根据文件ID统计下载次数
     * 
     * @param fileId 文件ID
     * @return 下载次数
     */
    @Select("SELECT COUNT(*) FROM file_download_log WHERE file_id = #{fileId}")
    long countByFileId(@Param("fileId") String fileId);
    
    /**
     * 统计指定时间范围内的下载次数
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 下载次数
     */
    @Select("SELECT COUNT(*) FROM file_download_log WHERE create_time >= #{startTime} AND create_time <= #{endTime}")
    long countByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
    
    /**
     * 统计下载状态分布
     * 
     * @return 状态分布统计
     */
    @Select("SELECT download_status, COUNT(*) as count FROM file_download_log GROUP BY download_status")
    List<Map<String, Object>> countByStatus();
    
    /**
     * 获取热门下载文件排行榜
     * 
     * @param limit 返回数量限制
     * @return 热门文件列表
     */
    @Select("SELECT file_id, file_name, COUNT(*) as download_count, " +
            "SUM(CASE WHEN download_status = 1 THEN 1 ELSE 0 END) as success_count " +
            "FROM file_download_log " +
            "GROUP BY file_id, file_name " +
            "ORDER BY download_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getPopularFiles(@Param("limit") int limit);
    
    /**
     * 获取用户下载统计
     * 
     * @param limit 返回数量限制
     * @return 用户下载统计列表
     */
    @Select("SELECT username, COUNT(*) as download_count, " +
            "SUM(CASE WHEN download_status = 1 THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN download_status = 1 THEN download_size ELSE 0 END) as total_download_size " +
            "FROM file_download_log " +
            "GROUP BY username " +
            "ORDER BY download_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getUserDownloadStats(@Param("limit") int limit);
    
    /**
     * 获取指定时间范围内的下载统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 下载统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_downloads, " +
            "SUM(CASE WHEN download_status = 1 THEN 1 ELSE 0 END) as success_downloads, " +
            "SUM(CASE WHEN download_status = 2 THEN 1 ELSE 0 END) as failed_downloads, " +
            "SUM(CASE WHEN download_status = 1 THEN download_size ELSE 0 END) as total_download_size, " +
            "AVG(CASE WHEN download_status = 1 AND duration_ms > 0 THEN download_speed ELSE NULL END) as avg_download_speed " +
            "FROM file_download_log " +
            "WHERE create_time >= #{startTime} AND create_time <= #{endTime}")
    Map<String, Object> getDownloadStatsInTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
    
    /**
     * 获取最近的下载记录
     * 
     * @param limit 返回数量限制
     * @return 最近的下载记录列表
     */
    @Select("SELECT * FROM file_download_log ORDER BY create_time DESC LIMIT #{limit}")
    List<FileDownloadLog> getRecentDownloads(@Param("limit") int limit);
    
    /**
     * 获取指定用户的下载历史
     * 
     * @param username 用户名
     * @param limit 返回数量限制
     * @return 用户下载历史列表
     */
    @Select("SELECT * FROM file_download_log WHERE username = #{username} ORDER BY create_time DESC LIMIT #{limit}")
    List<FileDownloadLog> getUserDownloadHistory(@Param("username") String username, @Param("limit") int limit);
    
    /**
     * 获取指定文件的下载历史
     * 
     * @param fileId 文件ID
     * @param limit 返回数量限制
     * @return 文件下载历史列表
     */
    @Select("SELECT * FROM file_download_log WHERE file_id = #{fileId} ORDER BY create_time DESC LIMIT #{limit}")
    List<FileDownloadLog> getFileDownloadHistory(@Param("fileId") String fileId, @Param("limit") int limit);
    
    /**
     * 删除指定时间之前的下载日志
     * 
     * @param expireTime 过期时间
     * @return 删除的记录数
     */
    @Select("DELETE FROM file_download_log WHERE create_time < #{expireTime}")
    int deleteExpiredLogs(@Param("expireTime") String expireTime);
    
    /**
     * 获取下载日志总数
     * 
     * @return 总记录数
     */
    @Select("SELECT COUNT(*) FROM file_download_log")
    long getTotalLogCount();
    
    /**
     * 获取最早的下载日志时间
     * 
     * @return 最早的创建时间
     */
    @Select("SELECT MIN(create_time) FROM file_download_log")
    String getEarliestLogTime();
    
    /**
     * 获取最新的下载日志时间
     * 
     * @return 最新的创建时间
     */
    @Select("SELECT MAX(create_time) FROM file_download_log")
    String getLatestLogTime();
    
    /**
     * 按日期统计下载量
     * 
     * @param days 统计天数
     * @return 每日下载统计
     */
    @Select("SELECT " +
            "DATE(create_time) as download_date, " +
            "COUNT(*) as download_count, " +
            "SUM(CASE WHEN download_status = 1 THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN download_status = 1 THEN download_size ELSE 0 END) as total_size " +
            "FROM file_download_log " +
            "WHERE create_time >= datetime('now', '-' || #{days} || ' days') " +
            "GROUP BY DATE(create_time) " +
            "ORDER BY download_date DESC")
    List<Map<String, Object>> getDailyDownloadStats(@Param("days") int days);
    
    /**
     * 获取下载失败的记录（用于问题分析）
     * 
     * @param limit 返回数量限制
     * @return 失败的下载记录列表
     */
    @Select("SELECT * FROM file_download_log WHERE download_status = 2 ORDER BY create_time DESC LIMIT #{limit}")
    List<FileDownloadLog> getFailedDownloads(@Param("limit") int limit);
    
    /**
     * 统计IP地址的下载次数（用于安全分析）
     * 
     * @param limit 返回数量限制
     * @return IP下载统计列表
     */
    @Select("SELECT client_ip, COUNT(*) as download_count, " +
            "COUNT(DISTINCT username) as user_count, " +
            "COUNT(DISTINCT file_id) as file_count " +
            "FROM file_download_log " +
            "GROUP BY client_ip " +
            "ORDER BY download_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getIpDownloadStats(@Param("limit") int limit);
}
