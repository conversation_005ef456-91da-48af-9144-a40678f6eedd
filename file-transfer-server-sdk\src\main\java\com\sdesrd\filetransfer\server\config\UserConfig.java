package com.sdesrd.filetransfer.server.config;

import lombok.Data;

/**
 * 单个用户配置
 */
@Data
public class UserConfig {

    /**
     * 用户角色类型枚举
     */
    public enum Role {
        /**
         * 普通用户角色 - 只能访问基本文件传输功能
         */
        USER("user"),

        /**
         * 管理员角色 - 可以访问所有功能包括管理接口
         */
        ADMIN("admin");

        private final String value;

        Role(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        /**
         * 从字符串值创建角色枚举
         *
         * @param value 角色字符串值
         * @return 对应的角色枚举，如果无效则返回USER
         */
        public static Role fromValue(String value) {
            if (value == null || value.trim().isEmpty()) {
                return USER;
            }

            for (Role role : values()) {
                if (role.value.equalsIgnoreCase(value.trim())) {
                    return role;
                }
            }

            return USER; // 默认返回普通用户角色
        }
    }

    /**
     * 用户密钥
     */
    private String secretKey;

    /**
     * 用户角色，默认为普通用户
     */
    private Role role = Role.USER;
    
    /**
     * 文件存储路径
     */
    private String storagePath;
    
    /**
     * 上传速度限制（字节/秒）
     */
    private Long uploadRateLimit;
    
    /**
     * 下载速度限制（字节/秒）
     */
    private Long downloadRateLimit;
    
    /**
     * 默认分块大小（字节）
     */
    private Long defaultChunkSize;
    
    /**
     * 最大文件大小（字节）
     */
    private Long maxFileSize;
    
    /**
     * 最大内存大小（字节）
     */
    private Long maxInMemorySize;
    
    /**
     * 是否启用秒传功能
     */
    private Boolean fastUploadEnabled;
    
    /**
     * 是否启用限流
     */
    private Boolean rateLimitEnabled;
} 