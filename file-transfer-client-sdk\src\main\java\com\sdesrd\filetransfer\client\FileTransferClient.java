package com.sdesrd.filetransfer.client;

import java.io.File;
import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.dto.ApiResult;
import com.sdesrd.filetransfer.client.dto.DownloadResult;
import com.sdesrd.filetransfer.client.dto.FileInfo;
import com.sdesrd.filetransfer.client.dto.FileUploadCompleteResponse;
import com.sdesrd.filetransfer.client.dto.FileUploadInitRequest;
import com.sdesrd.filetransfer.client.dto.FileUploadInitResponse;
import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.dto.UploadResult;
import com.sdesrd.filetransfer.client.exception.FileTransferException;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.util.AuthUtils;
import com.sdesrd.filetransfer.client.util.ConcurrentTransferManager;
import com.sdesrd.filetransfer.client.util.DownloadManager;
import com.sdesrd.filetransfer.client.util.FileUtils;
import com.sdesrd.filetransfer.client.util.RetryManager;
import com.sdesrd.filetransfer.client.util.UlidValidator;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 增强的文件传输客户端
 * 支持断点续传、传输限速、重试机制和并发控制
 */
@Slf4j
public class FileTransferClient implements AutoCloseable {

    private final ClientConfig config;
    private final OkHttpClient httpClient;
    private final ExecutorService executor;
    private final ConcurrentTransferManager transferManager;
    private final DownloadManager downloadManager;
    
    public FileTransferClient(ClientConfig config) {
        this.config = config;
        this.httpClient = createHttpClient();
        this.executor = Executors.newFixedThreadPool(config.getMaxConcurrentTransfers());
        this.transferManager = new ConcurrentTransferManager(config.getMaxConcurrentTransfers());
        this.downloadManager = new DownloadManager(config, httpClient);

        log.info("文件传输客户端初始化完成 - 服务器: {}, 最大并发数: {}",
                config.getServerUrl(), config.getMaxConcurrentTransfers());
    }
    
    /**
     * 创建HTTP客户端
     */
    private OkHttpClient createHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(Duration.ofSeconds(config.getConnectTimeoutSeconds()))
                .readTimeout(Duration.ofSeconds(config.getReadTimeoutSeconds()))
                .writeTimeout(Duration.ofSeconds(config.getWriteTimeoutSeconds()))
                .connectionPool(new ConnectionPool(
                        config.getMaxIdleConnections(),
                        config.getKeepAliveDurationMinutes(),
                        TimeUnit.MINUTES
                ))
                .retryOnConnectionFailure(true)
                .build();
    }
    

    /**
     * 上传文件（客户端指定fileId版本）
     * 服务端完全控制文件名格式，客户端只需提供原始文件名用于显示和扩展名提取
     *
     * @param localFilePath 本地文件路径
     * @param fileId        客户端指定的文件ID（ULID格式）
     * @param listener      传输监听器（可选）
     * @return 上传结果
     */
    public CompletableFuture<UploadResult> uploadFileWithId(String localFilePath, String fileId, TransferListener listener) {
        String taskId = "upload-" + System.currentTimeMillis() + "-" + Math.random();

        return transferManager.submitUploadTask(taskId, () -> {
            try {
                return uploadFileWithIdAndRetry(localFilePath, fileId, listener);
            } catch (Exception e) {
                log.error("上传任务失败 - 任务ID: {}, 文件: {}, fileId: {}", taskId, localFilePath, fileId, e);
                throw new RuntimeException(e);
            }
        });
    }


    /**
     * 带重试的上传文件（客户端指定fileId版本）
     */
    private UploadResult uploadFileWithIdAndRetry(String localFilePath, String fileId, TransferListener listener) throws Exception {
        return RetryManager.executeWithRetry(
                () -> {
                    try {
                        return uploadFileSyncWithId(localFilePath, fileId, listener);
                    } catch (FileTransferException e) {
                        throw new RuntimeException(e);
                    }
                },
                RetryManager.networkRetry().build(),
                "上传文件-" + localFilePath + "-fileId:" + fileId
        );
    }


    /**
     * 同步上传文件（客户端指定fileId版本）
     * 服务端完全控制文件名格式，客户端只需提供原始文件名用于显示和扩展名提取
     *
     * @param localFilePath 本地文件路径
     * @param fileId        客户端指定的文件ID（ULID格式）
     * @param listener      传输监听器（可选）
     * @return 上传结果
     * @throws FileTransferException 如果上传失败或fileId格式无效
     */
    public UploadResult uploadFileSyncWithId(String localFilePath, String fileId, TransferListener listener) throws FileTransferException {
        File file = new File(localFilePath);
        if (!file.exists() || !file.isFile()) {
            throw new FileTransferException("文件不存在或不是有效文件：" + localFilePath);
        }

        // 验证客户端提供的fileId格式
        String normalizedFileId;
        try {
            normalizedFileId = UlidValidator.validateAndNormalize(fileId);
            log.debug("客户端fileId验证通过: {} -> {}", fileId, normalizedFileId);
        } catch (FileTransferException e) {
            log.error("客户端fileId格式验证失败: {}", fileId, e);
            throw e;
        }

        try {
            // 通知开始传输
            if (listener != null) {
                TransferProgress startProgress = new TransferProgress();
                startProgress.setFileName(file.getName());
                startProgress.setTotalSize(file.length());
                startProgress.setTransferredSize(0L);
                startProgress.setProgress(0.0);
                startProgress.setCompleted(false);
                listener.onStart(startProgress);
            }

            // 计算文件MD5
            String fileMd5 = FileUtils.calculateMD5(file);

            // 提取原始文件名和文件后缀名
            String originalFileName = file.getName();
            String fileExtension = "";
            int lastDotIndex = originalFileName.lastIndexOf('.');
            if (lastDotIndex > 0 && lastDotIndex < originalFileName.length() - 1) {
                fileExtension = originalFileName.substring(lastDotIndex + 1);
            }

            // 初始化上传（使用客户端指定的fileId）
            FileUploadInitRequest initRequest = new FileUploadInitRequest();
            initRequest.setFileId(normalizedFileId); // 设置客户端指定的fileId
            initRequest.setOriginalFileName(originalFileName); // 传递完整的原始文件名
            initRequest.setFileExtension(fileExtension); // 传递后缀名
            initRequest.setFileSize(file.length());
            initRequest.setFileMd5(fileMd5);
            initRequest.setChunkSize(config.getChunkSize());

            ApiResult<FileUploadInitResponse> initResult = initUpload(initRequest);
            if (!initResult.isSuccess()) {
                throw new FileTransferException("初始化上传失败：" + initResult.getMessage());
            }

            FileUploadInitResponse initResponse = initResult.getData();

            // 验证服务端返回的fileId与客户端指定的一致
            if (!normalizedFileId.equals(initResponse.getFileId())) {
                log.warn("服务端返回的fileId与客户端指定的不一致: 客户端={}, 服务端={}",
                    normalizedFileId, initResponse.getFileId());
            }

            // 如果支持秒传
            if (initResponse.getFastUpload()) {
                log.info("文件秒传成功 - 原文件: {}, 服务器文件名: {}, fileId: {}",
                    originalFileName, initResponse.getFileName(), initResponse.getFileId());
                if (listener != null) {
                    TransferProgress progress = new TransferProgress();
                    progress.setTransferId(initResponse.getTransferId());
                    progress.setFileName(initResponse.getFileName()); // 使用服务器返回的文件名
                    progress.setTotalSize(file.length());
                    progress.setTransferredSize(file.length());
                    progress.setProgress(100.0);
                    progress.setCompleted(true);
                    listener.onProgress(progress);
                    listener.onCompleted(progress);
                }

                // 更新上传统计信息（秒传也算上传）
                ConcurrentTransferManager.addUploadedBytes(file.length());

                UploadResult result = new UploadResult();
                result.setSuccess(true);
                result.setTransferId(initResponse.getTransferId());
                result.setFileId(initResponse.getFileId()); // 使用服务端返回的fileId
                result.setFileName(initResponse.getFileName()); // 服务器返回的最终文件名
                result.setFileSize(file.length());
                result.setFastUpload(true);
                return result;
            }

            // 分块上传
            return uploadChunks(file, initResponse, listener);

        } catch (Exception e) {
            log.error("上传文件失败：{}, fileId: {}", localFilePath, normalizedFileId, e);
            throw new FileTransferException("上传文件失败：" + e.getMessage(), e);
        }
    }

    /**
     * 下载文件（增强版）
     *
     * @param fileId       文件ID
     * @param savePath     保存路径
     * @param listener     传输监听器（可选）
     * @return 下载结果
     */
    public CompletableFuture<DownloadResult> downloadFile(String fileId, String savePath, TransferListener listener) {
        String taskId = "download-" + System.currentTimeMillis() + "-" + Math.random();

        return transferManager.submitDownloadTask(taskId, () -> {
            try {
                return downloadFileWithRetry(fileId, savePath, listener);
            } catch (Exception e) {
                log.error("下载任务失败 - 任务ID: {}, 文件ID: {}", taskId, fileId, e);
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 带重试的下载文件
     */
    private DownloadResult downloadFileWithRetry(String fileId, String savePath, TransferListener listener) throws Exception {
        return RetryManager.executeWithRetry(
                () -> {
                    try {
                        return downloadFileSync(fileId, savePath, listener);
                    } catch (FileTransferException e) {
                        throw new RuntimeException(e);
                    }
                },
                RetryManager.networkRetry().build(),
                "下载文件-" + fileId
        );
    }
    
    /**
     * 同步下载文件
     */
    public DownloadResult downloadFileSync(String fileId, String savePath, TransferListener listener) throws FileTransferException {
        try {
            // 首先获取文件信息以获得文件大小
            FileInfo fileInfo = null;
            try {
                fileInfo = getFileInfo(fileId);
            } catch (Exception e) {
                log.warn("无法获取文件信息，将使用默认下载流程：{}", e.getMessage());
            }
            
            // 通知开始下载
            if (listener != null) {
                TransferProgress startProgress = new TransferProgress();
                // TransferProgress没有setFileId方法，使用fileName代替
                startProgress.setFileName(fileInfo != null ? fileInfo.getFileName() : "下载文件-" + fileId);
                startProgress.setTotalSize(fileInfo != null ? fileInfo.getFileSize() : 0L);
                startProgress.setTransferredSize(0L);
                startProgress.setProgress(0.0);
                startProgress.setCompleted(false);
                listener.onStart(startProgress);
            }

            // 注意：API路径前缀已通过config.getServerUrl()中的contextPath统一配置
            String url = config.getServerUrl() + "/api/file/download/" + fileId;
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "FileTransferClient/1.0.0");

            // 添加认证头
            addAuthHeaders(requestBuilder);
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new FileTransferException("下载请求失败：" + response.code() + " " + response.message());
                }
                
                // 保存文件
                File saveFile = new File(savePath);
                FileUtils.saveResponseToFile(response, saveFile, listener);
                
                // 确保通知下载完成
                if (listener != null) {
                    TransferProgress finalProgress = new TransferProgress();
                    finalProgress.setFileName(fileInfo != null ? fileInfo.getFileName() : "下载文件-" + fileId);
                    finalProgress.setTotalSize(saveFile.length());
                    finalProgress.setTransferredSize(saveFile.length());
                    finalProgress.setProgress(100.0);
                    finalProgress.setCompleted(true);
                    listener.onCompleted(finalProgress);
                }
                
                // 更新下载统计信息
                ConcurrentTransferManager.addDownloadedBytes(saveFile.length());
                
                DownloadResult result = new DownloadResult();
                result.setSuccess(true);
                result.setFileId(fileId);
                result.setLocalPath(savePath);
                result.setFileSize(saveFile.length());
                return result;
            }
            
        } catch (Exception e) {
            log.error("下载文件失败：{}", fileId, e);
            throw new FileTransferException("下载文件失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 查询传输进度
     */
    public TransferProgress queryProgress(String transferId) throws FileTransferException {
        try {
            String url = config.getServerUrl() + "/api/file/progress/" + transferId;
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "FileTransferClient/1.0.0");
            
            // 添加认证头
            addAuthHeaders(requestBuilder);
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new FileTransferException("查询进度失败：" + response.code() + " " + response.message());
                }
                
                String responseBody = response.body().string();
                ApiResult<TransferProgress> result = JSON.parseObject(responseBody, new TypeReference<ApiResult<TransferProgress>>() {});
                
                if (!result.isSuccess()) {
                    throw new FileTransferException("查询进度失败：" + result.getMessage());
                }
                
                return result.getData();
            }
            
        } catch (Exception e) {
            log.error("查询传输进度失败：{}", transferId, e);
            throw new FileTransferException("查询传输进度失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 获取文件详细信息
     * 
     * @param fileId 文件ID（MD5值）
     * @return 文件信息
     * @throws FileTransferException 如果获取失败
     */
    public FileInfo getFileInfo(String fileId) throws FileTransferException {
        try {
            String url = config.getServerUrl() + "/api/file/download/info/" + fileId;
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "FileTransferClient/1.0.0");
            
            // 添加认证头
            addAuthHeaders(requestBuilder);
            
            Request request = requestBuilder.build();
            
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new FileTransferException("获取文件信息失败：" + response.code() + " " + response.message());
                }
                
                String responseBody = response.body().string();
                ApiResult<FileInfo> result = JSON.parseObject(responseBody, new TypeReference<ApiResult<FileInfo>>() {});
                
                if (!result.isSuccess()) {
                    throw new FileTransferException("获取文件信息失败：" + result.getMessage());
                }
                
                return result.getData();
            }
            
        } catch (Exception e) {
            log.error("获取文件信息失败：{}", fileId, e);
            throw new FileTransferException("获取文件信息失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 异步获取文件详细信息
     * 
     * @param fileId 文件ID（MD5值）
     * @return 文件信息的CompletableFuture
     */
    public CompletableFuture<FileInfo> getFileInfoAsync(String fileId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getFileInfo(fileId);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, executor);
    }
    

    
    /**
     * 分块下载文件（增强版，支持多线程和智能断点续传）
     *
     * @param fileId 文件ID
     * @param savePath 保存路径
     * @param listener 传输监听器（可选）
     * @return 下载结果
     */
    public CompletableFuture<DownloadResult> downloadFileChunk(String fileId, String savePath, TransferListener listener) {
        return downloadManager.downloadFileChunked(fileId, savePath, listener);
    }

    /**
     * 分块下载文件（同步版本，增强版）
     *
     * @param fileId 文件ID
     * @param savePath 保存路径
     * @param listener 传输监听器（可选）
     * @return 下载结果
     */
    public DownloadResult downloadFileChunkSync(String fileId, String savePath, TransferListener listener) throws FileTransferException {
        return downloadManager.downloadFileChunkedSync(fileId, savePath, listener);
    }
    
    /**
     * 初始化上传
     */
    private ApiResult<FileUploadInitResponse> initUpload(FileUploadInitRequest request) throws IOException {
        String url = config.getServerUrl() + "/api/file/upload/init";
        String json = JSON.toJSONString(request);
        
        RequestBody body = RequestBody.create(json, MediaType.get("application/json; charset=utf-8"));
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("User-Agent", "FileTransferClient/1.0.0");
        
        // 添加认证头
        addAuthHeaders(requestBuilder);
        
        try (Response response = httpClient.newCall(requestBuilder.build()).execute()) {
            String responseBody = response.body().string();
            return JSON.parseObject(responseBody, new TypeReference<ApiResult<FileUploadInitResponse>>() {});
        }
    }
    
    /**
     * 分块上传
     */
    private UploadResult uploadChunks(File file, FileUploadInitResponse initResponse, TransferListener listener) throws Exception {
        long chunkSize = config.getChunkSize();
        long fileSize = file.length();
        int totalChunks = (int) Math.ceil((double) fileSize / chunkSize);

        // 通知开始传输（如果还没有通知过）
        if (listener != null) {
            TransferProgress startProgress = new TransferProgress();
            startProgress.setTransferId(initResponse.getTransferId());
            startProgress.setFileName(initResponse.getFileName());
            startProgress.setTotalSize(fileSize);
            startProgress.setTransferredSize(0L);
            startProgress.setProgress(0.0);
            startProgress.setTotalChunks(totalChunks);
            startProgress.setCompletedChunks(0);
            startProgress.setCompleted(false);
            listener.onStart(startProgress);
        }

        // 获取已上传的分块
        TransferProgress progress = queryProgress(initResponse.getTransferId());
        int startChunk = progress.getCompletedChunks();
        
        // 从断点继续上传
        for (int chunkIndex = startChunk; chunkIndex < totalChunks; chunkIndex++) {
            long offset = (long) chunkIndex * chunkSize;
            long currentChunkSize = Math.min(chunkSize, fileSize - offset);
            
            // 读取分块数据
            byte[] chunkData = FileUtils.readFileChunk(file, offset, currentChunkSize);
            String chunkMd5 = FileUtils.calculateMD5(chunkData);
            
            // 上传分块（带重试）
            uploadChunkWithRetry(initResponse.getTransferId(), chunkIndex, chunkData, chunkMd5);
            
            // 更新进度
            if (listener != null) {
                TransferProgress currentProgress = new TransferProgress();
                currentProgress.setTransferId(initResponse.getTransferId());
                currentProgress.setFileName(initResponse.getFileName());
                currentProgress.setTotalSize(fileSize);
                currentProgress.setTransferredSize(offset + currentChunkSize);
                currentProgress.setProgress((double) (offset + currentChunkSize) / fileSize * 100);
                currentProgress.setTotalChunks(totalChunks);
                currentProgress.setCompletedChunks(chunkIndex + 1);
                currentProgress.setCompleted(chunkIndex + 1 >= totalChunks);
                
                listener.onProgress(currentProgress);
                
                if (currentProgress.isCompleted()) {
                    listener.onCompleted(currentProgress);
                }
            }
        }
        
        // 完成上传并获取完整响应
        FileUploadCompleteResponse completeResponse = completeUpload(initResponse.getTransferId());

        // 更新上传统计信息
        ConcurrentTransferManager.addUploadedBytes(fileSize);

        UploadResult result = new UploadResult();
        result.setSuccess(true);
        result.setTransferId(completeResponse.getTransferId());
        result.setFileId(completeResponse.getFileId()); // ULID格式的fileId
        result.setFileName(completeResponse.getFileName()); // 服务器返回的最终文件名
        result.setFileSize(fileSize);
        result.setFastUpload(false);

        // 设置额外的信息
        if (completeResponse.getRelativePath() != null) {
            // 可以将相对路径存储在结果中，如果UploadResult支持的话
            log.info("文件上传完成 - fileId: {}, 相对路径: {}",
                completeResponse.getFileId(), completeResponse.getRelativePath());
        }

        return result;
    }

    /**
     * 上传单个分块（带重试）
     */
    private void uploadChunkWithRetry(String transferId, int chunkIndex, byte[] chunkData, String chunkMd5) throws Exception {
        RetryManager.executeWithRetry(
                () -> {
                    try {
                        uploadChunk(transferId, chunkIndex, chunkData, chunkMd5);
                        return null;
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                },
                RetryManager.quickRetry().build(),
                "上传分块-" + transferId + "-" + chunkIndex
        );
    }

    /**
     * 上传单个分块
     */
    private void uploadChunk(String transferId, int chunkIndex, byte[] chunkData, String chunkMd5) throws IOException {
        String url = config.getServerUrl() + "/api/file/upload/chunk";
        
        RequestBody formBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("transferId", transferId)
                .addFormDataPart("chunkIndex", String.valueOf(chunkIndex))
                .addFormDataPart("chunkMd5", chunkMd5)
                .addFormDataPart("chunk", "chunk", RequestBody.create(chunkData, MediaType.get("application/octet-stream")))
                .build();
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(formBody)
                .addHeader("User-Agent", "FileTransferClient/1.0.0");
        
        // 添加认证头
        addAuthHeaders(requestBuilder);
        
        Request request = requestBuilder.build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("分块上传失败：" + response.code() + " " + response.message());
            }
            
            String responseBody = response.body().string();
            ApiResult<?> result = JSON.parseObject(responseBody, ApiResult.class);
            if (!result.isSuccess()) {
                throw new IOException("分块上传失败：" + result.getMessage());
            }
        }
    }
    
    /**
     * 完成上传
     * 重构后的版本：返回完整的上传结果信息
     */
    private FileUploadCompleteResponse completeUpload(String transferId) throws IOException {
        String url = config.getServerUrl() + "/api/file/upload/complete/" + transferId;

        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(RequestBody.create("", MediaType.get("application/json")))
                .addHeader("User-Agent", "FileTransferClient/1.0.0");

        // 添加认证头
        addAuthHeaders(requestBuilder);

        Request request = requestBuilder.build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("完成上传失败：" + response.code() + " " + response.message());
            }

            String responseBody = response.body().string();
            ApiResult<FileUploadCompleteResponse> result = JSON.parseObject(responseBody,
                new TypeReference<ApiResult<FileUploadCompleteResponse>>() {});

            if (!result.isSuccess()) {
                throw new IOException("完成上传失败：" + result.getMessage());
            }

            return result.getData();
        }
    }
    
    /**
     * 添加认证头
     */
    private void addAuthHeaders(Request.Builder requestBuilder) {
        String username = config.getUser();
        String secretKey = config.getSecretKey();
        String authToken = AuthUtils.generateAuthToken(username, secretKey);

        requestBuilder.addHeader(AuthUtils.USER_HEADER, username);
        requestBuilder.addHeader(AuthUtils.AUTH_HEADER, authToken);
    }

    /**
     * 生成ULID格式的文件ID
     * 使用Crockford's Base32字符集（排除I、L、O、U）
     *
     * @return 26字符的ULID字符串
     */
    private String generateUlid() {
        // ULID字符集（Crockford's Base32，排除I、L、O、U）
        String chars = "0123456789ABCDEFGHJKMNPQRSTVWXYZ";

        // 使用当前时间戳生成时间戳部分（10字符）
        long timestamp = System.currentTimeMillis();
        StringBuilder timestampPart = new StringBuilder();
        long temp = timestamp;

        // 将时间戳转换为Base32格式
        for (int i = 0; i < 10; i++) {
            timestampPart.insert(0, chars.charAt((int) (temp % 32)));
            temp /= 32;
        }

        // 生成16位随机部分
        StringBuilder randomPart = new StringBuilder();
        for (int i = 0; i < 16; i++) {
            randomPart.append(chars.charAt((int) (Math.random() * chars.length())));
        }

        return timestampPart.toString() + randomPart.toString();
    }
    
    /**
     * 获取传输统计信息
     */
    public ConcurrentTransferManager.TransferStats getTransferStats() {
        return transferManager.getTransferStats();
    }

    /**
     * 取消传输任务
     *
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    public boolean cancelTransfer(String taskId) {
        return transferManager.cancelTransfer(taskId);
    }

    /**
     * 检查是否有可用的传输槽位
     */
    public boolean hasAvailableSlot() {
        return transferManager.hasAvailableSlot();
    }

    /**
     * 关闭客户端（增强版）
     */
    public void close() {
        log.info("正在关闭文件传输客户端...");

        // 关闭传输管理器
        if (transferManager != null) {
            transferManager.shutdown();
        }

        // 关闭下载管理器
        if (downloadManager != null) {
            downloadManager.shutdown();
        }

        // 关闭线程池
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 关闭HTTP客户端
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
            httpClient.connectionPool().evictAll();
        }

        log.info("文件传输客户端已关闭");
    }
} 