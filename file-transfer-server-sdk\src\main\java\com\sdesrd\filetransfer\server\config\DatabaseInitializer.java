package com.sdesrd.filetransfer.server.config;

import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.Statement;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库初始化器
 * 自动创建SQLite数据库和表结构
 */
@Slf4j
@Component
public class DatabaseInitializer implements ApplicationRunner {
    
    @Autowired
    private DataSource dataSource;
    
    private FileTransferProperties properties;
    
    /**
     * 无参构造函数（用于Spring Bean创建）
     */
    public DatabaseInitializer() {
        // Spring会通过setter或字段注入设置properties
    }
    
    /**
     * 有参构造函数（用于手动创建实例）
     */
    public DatabaseInitializer(FileTransferProperties properties) {
        this.properties = properties;
        ensureDatabaseDirectoryExists();
    }
    
    /**
     * 设置配置属性（Spring会调用此方法）
     */
    @Autowired
    public void setProperties(FileTransferProperties properties) {
        this.properties = properties;
        ensureDatabaseDirectoryExists();
    }
    
    /**
     * 确保数据库目录存在
     */
    private void ensureDatabaseDirectoryExists() {
        try {
            if (properties == null || properties.getDatabasePath() == null) {
                log.warn("数据库路径配置为空，使用默认路径");
                return;
            }

            String dbPath = properties.getDatabasePath();

            // 检查是否是内存数据库
            if (":memory:".equals(dbPath) || dbPath.contains(":memory:")) {
                log.info("使用内存数据库，跳过目录创建");
                return;
            }

            File dbFile = new File(dbPath);
            File dbDir = dbFile.getParentFile();

            if (dbDir != null && !dbDir.exists()) {
                boolean created = dbDir.mkdirs();
                if (created) {
                    log.info("创建数据库目录: {}", dbDir.getAbsolutePath());
                } else {
                    log.warn("无法创建数据库目录: {}", dbDir.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            log.error("创建数据库目录失败", e);
            // 不抛出异常，让数据源自己尝试处理
        }
    }
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        initializeDatabase();
    }
    
    /**
     * 初始化数据库
     */
    private void initializeDatabase() {
        try {
            // 再次确保数据库目录存在（双重保险）
            ensureDatabaseDirectoryExists();
            
            // 创建数据库表（使用 IF NOT EXISTS，所以重复创建不会有问题）
            try (Connection connection = dataSource.getConnection()) {
                createTables(connection);
                log.info("数据库表创建完成");
            }
            
        } catch (Exception e) {
            log.error("数据库初始化失败", e);
            throw new RuntimeException("数据库初始化失败", e);
        }
    }
    
    // 移除了 tableExists 方法，因为我们使用 CREATE TABLE IF NOT EXISTS
    
    /**
     * 创建数据库表
     */
    private void createTables(Connection connection) throws Exception {
        String createSql = getSqlScript();
        
        try (Statement statement = connection.createStatement()) {
            log.info("开始执行数据库初始化");
            
            // 预处理SQL：移除注释并重新格式化
            String processedSql = preprocessSql(createSql);
            log.info("预处理后的SQL长度: {}", processedSql.length());
            
            // 按分号分割并执行
            String[] statements = processedSql.split(";");
            log.info("分割后得到 {} 个SQL语句", statements.length);
            
            for (int i = 0; i < statements.length; i++) {
                String sql = statements[i].trim();
                if (!sql.isEmpty()) {
                    String logSql = sql.length() > 100 ? sql.substring(0, 100) + "..." : sql;
                    log.info("执行第{}个SQL: {}", i + 1, logSql);
                    try {
                        statement.execute(sql);
                        log.info("第{}个SQL执行成功", i + 1);
                    } catch (Exception e) {
                        log.error("第{}个SQL执行失败: {}", i + 1, e.getMessage());
                        throw e;
                    }
                }
            }
            
            log.info("数据库表创建完成");
        }
    }
    
    /**
     * 预处理SQL：移除注释和多余空白
     */
    private String preprocessSql(String sql) {
        StringBuilder result = new StringBuilder();
        String[] lines = sql.split("\n");
        boolean inMultiLineComment = false;
        
        for (String line : lines) {
            line = line.trim();
            
            // 跳过空行
            if (line.isEmpty()) {
                continue;
            }
            
            // 处理多行注释
            if (line.contains("/*")) {
                inMultiLineComment = true;
                continue;
            }
            if (line.contains("*/")) {
                inMultiLineComment = false;
                continue;
            }
            if (inMultiLineComment) {
                continue;
            }
            
            // 跳过单行注释
            if (line.startsWith("--")) {
                continue;
            }
            
            result.append(line).append(" ");
        }
        
        return result.toString();
    }
    
    /**
     * 获取SQL脚本内容
     */
    private String getSqlScript() throws Exception {
        try {
            ClassPathResource resource = new ClassPathResource("sql/init-sqlite.sql");
            try (InputStream is = resource.getInputStream()) {
                byte[] bytes = StreamUtils.copyToByteArray(is);
                return new String(bytes, StandardCharsets.UTF_8);
            }
        } catch (Exception e) {
            // 如果找不到SQL文件，使用内置的SQL
            return getDefaultSqlScript();
        }
    }
    
    /**
     * 获取默认的SQL脚本
     */
    private String getDefaultSqlScript() {
        return "-- 文件传输记录表\n" +
                "CREATE TABLE IF NOT EXISTS file_transfer_record (\n" +
                "    id TEXT PRIMARY KEY,\n" +
                "    file_id TEXT NOT NULL,\n" +
                "    file_name TEXT NOT NULL,\n" +
                "    file_size INTEGER NOT NULL,\n" +
                "    file_path TEXT,\n" +
                "    transferred_size INTEGER NOT NULL DEFAULT 0,\n" +
                "    total_chunks INTEGER NOT NULL DEFAULT 0,\n" +
                "    completed_chunks INTEGER NOT NULL DEFAULT 0,\n" +
                "    status INTEGER NOT NULL DEFAULT 0,\n" +
                "    client_ip TEXT,\n" +
                "    create_time TEXT NOT NULL,\n" +
                "    update_time TEXT NOT NULL,\n" +
                "    complete_time TEXT,\n" +
                "    fail_reason TEXT,\n" +
                "    file_type TEXT,\n" +
                "    ext_info TEXT\n" +
                ");\n" +
                "\n" +
                "-- 创建索引\n" +
                "CREATE INDEX IF NOT EXISTS idx_file_id ON file_transfer_record(file_id);\n" +
                "CREATE INDEX IF NOT EXISTS idx_status ON file_transfer_record(status);\n" +
                "CREATE INDEX IF NOT EXISTS idx_client_ip ON file_transfer_record(client_ip);\n" +
                "CREATE INDEX IF NOT EXISTS idx_create_time ON file_transfer_record(create_time);\n" +
                "\n" +
                "-- 文件分块记录表\n" +
                "CREATE TABLE IF NOT EXISTS file_chunk_record (\n" +
                "    id TEXT PRIMARY KEY,\n" +
                "    transfer_id TEXT NOT NULL,\n" +
                "    file_id TEXT NOT NULL,\n" +
                "    chunk_index INTEGER NOT NULL,\n" +
                "    chunk_size INTEGER,\n" +
                "    chunk_offset INTEGER NOT NULL,\n" +
                "    chunk_path TEXT,\n" +
                "    chunk_md5 TEXT,\n" +
                "    status INTEGER NOT NULL DEFAULT 0,\n" +
                "    retry_count INTEGER NOT NULL DEFAULT 0,\n" +
                "    create_time TEXT NOT NULL,\n" +
                "    complete_time TEXT,\n" +
                "    fail_reason TEXT\n" +
                ");\n" +
                "\n" +
                "-- 创建索引\n" +
                "CREATE UNIQUE INDEX IF NOT EXISTS uk_transfer_chunk ON file_chunk_record(transfer_id, chunk_index);\n" +
                "CREATE INDEX IF NOT EXISTS idx_transfer_id ON file_chunk_record(transfer_id);\n" +
                "CREATE INDEX IF NOT EXISTS idx_file_id_chunk ON file_chunk_record(file_id);\n" +
                "CREATE INDEX IF NOT EXISTS idx_status_chunk ON file_chunk_record(status);\n" +
                "CREATE INDEX IF NOT EXISTS idx_chunk_index ON file_chunk_record(chunk_index);";
    }
} 