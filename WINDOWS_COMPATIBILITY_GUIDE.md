# Windows兼容性指南

## 概述

本文档描述了为文件传输SDK项目实现的Windows兼容性改进，包括创建的Windows批处理脚本和修复的代码兼容性问题。

## 创建的Windows批处理脚本

### 1. scripts/set-java-env.bat

**功能：** 设置Java 8环境变量（Windows版本）

**主要特性：**
- 自动检测常见的Java安装路径
- 支持Amazon Corretto和Oracle JDK
- 设置MAVEN_OPTS环境变量
- 验证Java安装的完整性

**使用方法：**
```cmd
cd scripts
call set-java-env.bat
```

### 2. file-transfer-server-standalone/start-server.bat

**功能：** 文件传输独立服务端启动脚本（Windows版本）

**主要特性：**
- 服务器生命周期管理（启动、停止、重启、状态查看）
- 支持前台和后台运行模式
- 使用netstat和tasklist进行端口和进程管理
- 健康检查和超时控制
- 日志管理

**使用方法：**
```cmd
cd file-transfer-server-standalone

# 启动服务器
start-server.bat start

# 后台启动服务器
start-server.bat start --background

# 停止服务器
start-server.bat stop

# 查看服务器状态
start-server.bat status

# 查看帮助
start-server.bat --help
```

### 3. build-and-test.bat

**功能：** 文件传输SDK构建和测试脚本（Windows版本）

**主要特性：**
- 完整的Maven项目构建和测试流程
- 支持两种执行模式：build（仅构建）和build-test（构建+测试）
- 自动化的Java环境设置和验证
- 详细的日志记录和报告生成
- Windows特有的命令和路径处理

**使用方法：**
```cmd
# 完整构建和测试流程
build-and-test.bat

# 仅构建项目
build-and-test.bat build

# 使用指定的Java路径
build-and-test.bat --java-home "C:\Program Files\Java\jdk1.8.0_452"

# 查看帮助
build-and-test.bat --help
```

## 修复的代码兼容性问题

### 1. 文件路径处理

**问题：** 硬编码的路径分隔符导致Windows兼容性问题

**修复：**
- 在`FileTransferService.calculateRelativePath()`中使用`File.separator`
- 在`FileMetadataService.calculateRelativePath()`中使用`File.separator`
- 在`DatabaseFallbackService`中统一路径分隔符处理
- 在`scripts/java-environment-check.sh`中修复硬编码路径

**修复示例：**
```java
// 修复前
return relative.toString().replace("\\", "/");

// 修复后
return relative.toString().replace(File.separator, "/");
```

### 2. 跨平台路径处理

**改进：**
- 使用`Paths.get()`进行路径构建，确保跨平台兼容性
- 使用`Path.normalize()`标准化路径
- 统一使用正斜杠作为API返回的路径分隔符

## Windows特有的实现

### 1. 进程管理

**Linux方式：**
```bash
# 检查端口占用
lsof -ti:$port

# 杀死进程
kill -9 $pid
```

**Windows方式：**
```cmd
# 检查端口占用
netstat -an | findstr ":port "

# 杀死进程
taskkill /f /pid %pid%
```

### 2. 后台进程启动

**Linux方式：**
```bash
nohup $java_cmd > "$LOG_FILE" 2>&1 &
```

**Windows方式：**
```cmd
start /b "" %java_cmd% > "%LOG_FILE%" 2>&1
```

### 3. 环境变量处理

**Linux方式：**
```bash
export JAVA_HOME="$path"
export PATH="$JAVA_HOME/bin:$PATH"
```

**Windows方式：**
```cmd
set "JAVA_HOME=%path%"
set "PATH=%JAVA_HOME%\bin;%PATH%"
```

## 测试建议

### 1. 基本功能测试

1. **环境设置测试：**
   ```cmd
   cd scripts
   call set-java-env.bat
   java -version
   mvn -version
   ```

2. **构建测试：**
   ```cmd
   build-and-test.bat build
   ```

3. **服务器启动测试：**
   ```cmd
   cd file-transfer-server-standalone
   start-server.bat start --background
   start-server.bat status
   start-server.bat stop
   ```

### 2. 完整流程测试

1. **完整构建和测试：**
   ```cmd
   build-and-test.bat build-test
   ```

2. **客户端演示测试：**
   ```cmd
   cd file-transfer-client-demo
   mvn exec:java
   ```

### 3. 路径兼容性测试

1. 在不同的Windows版本上测试（Windows 10, Windows 11, Windows Server）
2. 测试包含空格的路径
3. 测试长路径名
4. 测试Unicode字符路径

## 注意事项

### 1. Java环境要求

- 需要Java 8或更高版本
- 建议使用Amazon Corretto 8或Oracle JDK 8
- 确保JAVA_HOME环境变量正确设置

### 2. 权限要求

- 某些操作可能需要管理员权限
- 确保对项目目录有读写权限
- 防火墙可能需要允许Java应用的网络访问

### 3. 路径限制

- Windows路径长度限制（260字符）
- 避免使用特殊字符作为文件名
- 注意大小写敏感性差异

## 已知限制

1. **集成测试简化：** Windows版本的build-and-test.bat中集成测试功能有所简化
2. **超时控制：** Windows批处理的超时控制不如Linux shell脚本精确
3. **信号处理：** Windows不支持Unix信号，使用taskkill替代

## 故障排除

### 1. 常见问题

**问题：** Java命令未找到
**解决：** 检查JAVA_HOME设置，运行set-java-env.bat

**问题：** Maven命令未找到
**解决：** 确保Maven已安装并在PATH中

**问题：** 端口被占用
**解决：** 使用netstat -an查看端口占用，使用taskkill结束占用进程

### 2. 日志查看

所有脚本都会生成详细的日志文件：
- 构建日志：`logs/build-and-test-*.log`
- 服务器日志：`file-transfer-server-standalone/logs/server.log`

## 总结

通过创建Windows批处理脚本和修复代码中的路径处理问题，文件传输SDK项目现在完全支持Windows环境。所有核心功能在Windows和Linux系统中都能正常工作，确保了真正的跨平台兼容性。
