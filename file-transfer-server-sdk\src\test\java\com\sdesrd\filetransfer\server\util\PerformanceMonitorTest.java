package com.sdesrd.filetransfer.server.util;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import lombok.extern.slf4j.Slf4j;

/**
 * 性能监控器测试
 */
@Slf4j
@DisplayName("性能监控器测试")
class PerformanceMonitorTest {
    
    /** 测试文件大小常量 */
    private static final long TEST_FILE_SIZE_1MB = 1024 * 1024L;
    private static final long TEST_FILE_SIZE_5MB = 5 * 1024 * 1024L;
    private static final long TEST_FILE_SIZE_10MB = 10 * 1024 * 1024L;
    
    @BeforeEach
    void setUp() {
        log.info("[测试准备] 启动性能监控");
        PerformanceMonitor.startMonitoring();
    }
    
    @AfterEach
    void tearDown() {
        log.info("[测试清理] 停止性能监控");
        PerformanceMonitor.stopMonitoring();
    }
    
    @Test
    @DisplayName("测试上传统计记录")
    void testUploadStatistics() {
        log.info("[测试开始] 测试上传统计记录");
        
        // 获取初始统计
        PerformanceMonitor.TransferStatistics initialStats = PerformanceMonitor.getTransferStatistics();
        log.info("初始统计: {}", initialStats);
        
        long initialUploads = initialStats.getTotalUploads();
        long initialUploadBytes = initialStats.getTotalUploadBytes();
        long initialSuccessful = initialStats.getSuccessfulTransfers();
        
        // 记录成功上传
        log.info("[测试步骤] 记录成功上传 - 大小: {}", FileUtils.formatFileSize(TEST_FILE_SIZE_1MB));
        PerformanceMonitor.recordUpload(TEST_FILE_SIZE_1MB, true);
        
        log.info("[测试步骤] 记录成功上传 - 大小: {}", FileUtils.formatFileSize(TEST_FILE_SIZE_5MB));
        PerformanceMonitor.recordUpload(TEST_FILE_SIZE_5MB, true);
        
        // 记录失败上传
        log.info("[测试步骤] 记录失败上传 - 大小: {}", FileUtils.formatFileSize(TEST_FILE_SIZE_10MB));
        PerformanceMonitor.recordUpload(TEST_FILE_SIZE_10MB, false);
        
        // 验证统计结果
        PerformanceMonitor.TransferStatistics finalStats = PerformanceMonitor.getTransferStatistics();
        log.info("最终统计: {}", finalStats);
        
        assertEquals(initialUploads + 3, finalStats.getTotalUploads(), "上传总数应该增加3");
        assertEquals(initialUploadBytes + TEST_FILE_SIZE_1MB + TEST_FILE_SIZE_5MB + TEST_FILE_SIZE_10MB, 
                finalStats.getTotalUploadBytes(), "上传字节数应该正确累加");
        assertEquals(initialSuccessful + 2, finalStats.getSuccessfulTransfers(), "成功传输数应该增加2");
        
        log.info("[测试完成] 上传统计记录测试通过");
    }
    
    @Test
    @DisplayName("测试下载统计记录")
    void testDownloadStatistics() {
        log.info("[测试开始] 测试下载统计记录");
        
        // 获取初始统计
        PerformanceMonitor.TransferStatistics initialStats = PerformanceMonitor.getTransferStatistics();
        log.info("初始统计: {}", initialStats);
        
        long initialDownloads = initialStats.getTotalDownloads();
        long initialDownloadBytes = initialStats.getTotalDownloadBytes();
        long initialSuccessful = initialStats.getSuccessfulTransfers();
        long initialFailed = initialStats.getFailedTransfers();
        
        // 记录成功下载
        log.info("[测试步骤] 记录成功下载 - 大小: {}", FileUtils.formatFileSize(TEST_FILE_SIZE_5MB));
        PerformanceMonitor.recordDownload(TEST_FILE_SIZE_5MB, true);
        
        // 记录失败下载
        log.info("[测试步骤] 记录失败下载 - 大小: {}", FileUtils.formatFileSize(TEST_FILE_SIZE_1MB));
        PerformanceMonitor.recordDownload(TEST_FILE_SIZE_1MB, false);
        
        // 验证统计结果
        PerformanceMonitor.TransferStatistics finalStats = PerformanceMonitor.getTransferStatistics();
        log.info("最终统计: {}", finalStats);
        
        assertEquals(initialDownloads + 2, finalStats.getTotalDownloads(), "下载总数应该增加2");
        assertEquals(initialDownloadBytes + TEST_FILE_SIZE_5MB + TEST_FILE_SIZE_1MB, 
                finalStats.getTotalDownloadBytes(), "下载字节数应该正确累加");
        assertEquals(initialSuccessful + 1, finalStats.getSuccessfulTransfers(), "成功传输数应该增加1");
        assertEquals(initialFailed + 1, finalStats.getFailedTransfers(), "失败传输数应该增加1");
        
        log.info("[测试完成] 下载统计记录测试通过");
    }
    
    @Test
    @DisplayName("测试成功率计算")
    void testSuccessRateCalculation() {
        log.info("[测试开始] 测试成功率计算");

        // 清空统计（通过重新启动监控）
        PerformanceMonitor.stopMonitoring();

        // 清理静态统计数据（需要添加清理方法）
        clearStatistics();

        PerformanceMonitor.startMonitoring();
        
        // 记录传输数据
        log.info("[测试步骤] 记录混合传输数据");
        PerformanceMonitor.recordUpload(TEST_FILE_SIZE_1MB, true);   // 成功
        PerformanceMonitor.recordUpload(TEST_FILE_SIZE_1MB, true);   // 成功
        PerformanceMonitor.recordUpload(TEST_FILE_SIZE_1MB, false);  // 失败
        PerformanceMonitor.recordDownload(TEST_FILE_SIZE_1MB, true); // 成功
        PerformanceMonitor.recordDownload(TEST_FILE_SIZE_1MB, false); // 失败
        
        // 验证成功率：3成功 / 5总数 = 60%
        PerformanceMonitor.TransferStatistics stats = PerformanceMonitor.getTransferStatistics();
        log.info("传输统计: {}", stats);
        
        assertEquals(5, stats.getTotalTransfers(), "总传输数应该是5");
        assertEquals(3, stats.getSuccessfulTransfers(), "成功传输数应该是3");
        assertEquals(2, stats.getFailedTransfers(), "失败传输数应该是2");
        assertEquals(60.0, stats.getSuccessRate(), 0.01, "成功率应该是60%");
        
        log.info("[测试完成] 成功率计算测试通过 - 成功率: {:.2f}%", stats.getSuccessRate());
    }
    
    @Test
    @DisplayName("测试性能指标收集")
    void testPerformanceMetricsCollection() {
        log.info("[测试开始] 测试性能指标收集");
        
        // 获取当前性能指标
        PerformanceMonitor.PerformanceMetrics metrics = PerformanceMonitor.getCurrentMetrics();
        assertNotNull(metrics, "性能指标不应该为空");
        
        log.info("性能指标: {}", metrics);
        
        // 验证基本指标
        assertTrue(metrics.getTimestamp() > 0, "时间戳应该大于0");
        assertTrue(metrics.getHeapMemoryUsed() > 0, "堆内存使用量应该大于0");
        assertTrue(metrics.getHeapMemoryMax() > 0, "堆内存最大值应该大于0");
        assertTrue(metrics.getAvailableProcessors() > 0, "可用处理器数应该大于0");
        assertTrue(metrics.getJvmUptime() > 0, "JVM运行时间应该大于0");
        assertTrue(metrics.getThreadCount() > 0, "线程数应该大于0");
        
        // 验证内存使用率计算
        double heapUsagePercent = metrics.getHeapMemoryUsagePercent();
        assertTrue(heapUsagePercent >= 0 && heapUsagePercent <= 100, 
                "堆内存使用率应该在0-100%之间，实际值: " + heapUsagePercent);
        
        log.info("[测试完成] 性能指标收集测试通过");
        log.info("  - 堆内存使用: {}/{} ({:.1f}%)", 
                FileUtils.formatFileSize(metrics.getHeapMemoryUsed()),
                FileUtils.formatFileSize(metrics.getHeapMemoryMax()),
                heapUsagePercent);
        log.info("  - 可用处理器: {}", metrics.getAvailableProcessors());
        log.info("  - 线程数: {} (守护线程: {})", 
                metrics.getThreadCount(), metrics.getDaemonThreadCount());
    }
    
    @Test
    @DisplayName("测试监控启停功能")
    void testMonitoringStartStop() {
        log.info("[测试开始] 测试监控启停功能");
        
        // 停止监控
        log.info("[测试步骤] 停止监控");
        PerformanceMonitor.stopMonitoring();
        
        // 重新启动监控
        log.info("[测试步骤] 重新启动监控");
        PerformanceMonitor.startMonitoring();
        
        // 验证监控正常工作
        PerformanceMonitor.PerformanceMetrics metrics = PerformanceMonitor.getCurrentMetrics();
        assertNotNull(metrics, "重新启动后应该能获取性能指标");
        
        log.info("[测试完成] 监控启停功能测试通过");
    }
    
    @Test
    @DisplayName("测试大量数据统计")
    void testLargeDataStatistics() {
        log.info("[测试开始] 测试大量数据统计");
        
        // 清空统计
        PerformanceMonitor.stopMonitoring();
        PerformanceMonitor.startMonitoring();
        
        // 模拟大量传输
        final int TRANSFER_COUNT = 100;
        final long LARGE_FILE_SIZE = 100 * 1024 * 1024L; // 100MB
        
        log.info("[测试步骤] 模拟{}次大文件传输，每个文件大小: {}", 
                TRANSFER_COUNT, FileUtils.formatFileSize(LARGE_FILE_SIZE));
        
        for (int i = 0; i < TRANSFER_COUNT; i++) {
            boolean success = (i % 10) != 0; // 90%成功率
            PerformanceMonitor.recordUpload(LARGE_FILE_SIZE, success);
            
            if ((i + 1) % 20 == 0) {
                log.debug("已处理 {}/{} 次传输", i + 1, TRANSFER_COUNT);
            }
        }
        
        // 验证统计结果
        PerformanceMonitor.TransferStatistics stats = PerformanceMonitor.getTransferStatistics();
        log.info("大量数据统计结果: {}", stats);
        
        assertEquals(TRANSFER_COUNT, stats.getTotalUploads(), "上传总数应该正确");
        assertEquals(TRANSFER_COUNT * LARGE_FILE_SIZE, stats.getTotalUploadBytes(), "总字节数应该正确");
        assertEquals(90, Math.round(stats.getSuccessRate()), "成功率应该约为90%");
        
        log.info("[测试完成] 大量数据统计测试通过");
        log.info("  - 总传输: {} 次", stats.getTotalTransfers());
        log.info("  - 总字节: {}", FileUtils.formatFileSize(stats.getTotalBytes()));
        log.info("  - 成功率: {:.1f}%", stats.getSuccessRate());
    }

    /**
     * 清理统计数据（通过反射访问私有字段）
     */
    private void clearStatistics() {
        try {
            // 使用反射清理静态统计字段
            java.lang.reflect.Field totalUploadCountField = PerformanceMonitor.class.getDeclaredField("TOTAL_UPLOAD_COUNT");
            totalUploadCountField.setAccessible(true);
            ((java.util.concurrent.atomic.AtomicLong) totalUploadCountField.get(null)).set(0);

            java.lang.reflect.Field totalDownloadCountField = PerformanceMonitor.class.getDeclaredField("TOTAL_DOWNLOAD_COUNT");
            totalDownloadCountField.setAccessible(true);
            ((java.util.concurrent.atomic.AtomicLong) totalDownloadCountField.get(null)).set(0);

            java.lang.reflect.Field totalUploadBytesField = PerformanceMonitor.class.getDeclaredField("TOTAL_UPLOAD_BYTES");
            totalUploadBytesField.setAccessible(true);
            ((java.util.concurrent.atomic.AtomicLong) totalUploadBytesField.get(null)).set(0);

            java.lang.reflect.Field totalDownloadBytesField = PerformanceMonitor.class.getDeclaredField("TOTAL_DOWNLOAD_BYTES");
            totalDownloadBytesField.setAccessible(true);
            ((java.util.concurrent.atomic.AtomicLong) totalDownloadBytesField.get(null)).set(0);

            java.lang.reflect.Field successfulTransfersField = PerformanceMonitor.class.getDeclaredField("SUCCESSFUL_TRANSFERS");
            successfulTransfersField.setAccessible(true);
            ((java.util.concurrent.atomic.AtomicLong) successfulTransfersField.get(null)).set(0);

            java.lang.reflect.Field failedTransfersField = PerformanceMonitor.class.getDeclaredField("FAILED_TRANSFERS");
            failedTransfersField.setAccessible(true);
            ((java.util.concurrent.atomic.AtomicLong) failedTransfersField.get(null)).set(0);

            log.debug("统计数据已清理");
        } catch (Exception e) {
            log.warn("清理统计数据失败", e);
        }
    }
}
