package com.sdesrd.filetransfer.server.util;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Random;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输测试工具类
 */
@Slf4j
public class FileTransferTestUtils {
    
    private static final Random RANDOM = new Random();
    
    /**
     * 创建测试文件
     * 
     * @param filePath 文件路径
     * @param sizeInBytes 文件大小（字节）
     * @return 创建的文件
     */
    public static File createTestFile(String filePath, long sizeInBytes) throws IOException {
        File file = new File(filePath);
        
        // 确保目录存在
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            Files.createDirectories(parentDir.toPath());
        }
        
        // 创建指定大小的文件
        try (FileOutputStream fos = new FileOutputStream(file)) {
            byte[] buffer = new byte[8192];
            long written = 0;
            
            while (written < sizeInBytes) {
                // 填充随机数据
                RANDOM.nextBytes(buffer);
                
                int toWrite = (int) Math.min(buffer.length, sizeInBytes - written);
                fos.write(buffer, 0, toWrite);
                written += toWrite;
            }
        }
        
        log.info("创建测试文件成功 - 路径: {}, 大小: {}", filePath, FileUtils.formatFileSize(sizeInBytes));
        return file;
    }
    
    /**
     * 创建小测试文件（1MB）
     */
    public static File createSmallTestFile(String filePath) throws IOException {
        return createTestFile(filePath, 1024 * 1024); // 1MB
    }
    
    /**
     * 创建中等测试文件（10MB）
     */
    public static File createMediumTestFile(String filePath) throws IOException {
        return createTestFile(filePath, 10 * 1024 * 1024); // 10MB
    }
    
    /**
     * 创建大测试文件（100MB）
     */
    public static File createLargeTestFile(String filePath) throws IOException {
        return createTestFile(filePath, 100 * 1024 * 1024); // 100MB
    }
    
    /**
     * 删除测试文件
     */
    public static boolean deleteTestFile(String filePath) {
        try {
            // 移除文件权限保护机制 - 直接删除文件，不再处理权限设置
            boolean deleted = Files.deleteIfExists(Paths.get(filePath));
            if (deleted) {
                log.info("删除测试文件成功 - 路径: {}", filePath);
            }
            return deleted;
        } catch (IOException e) {
            log.error("删除测试文件失败 - 路径: {}", filePath, e);
            return false;
        }
    }
    
    /**
     * 清理测试目录
     * 移除文件权限保护机制 - 直接删除文件，不再处理权限设置
     */
    public static void cleanupTestDirectory(String dirPath) {
        try {
            File dir = new File(dirPath);
            if (dir.exists() && dir.isDirectory()) {
                // 移除权限保护机制 - 直接删除文件，不再设置权限
                File[] files = dir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        if (file.isFile()) {
                            // 直接删除文件，不再设置可写权限
                            if (!file.delete()) {
                                log.warn("删除文件失败: {}", file.getAbsolutePath());
                            }
                        }
                    }
                }
            }
            log.info("清理测试目录完成 - 路径: {}", dirPath);
        } catch (Exception e) {
            log.error("清理测试目录失败 - 路径: {}", dirPath, e);
        }
    }
    
    /**
     * 递归删除目录及其所有内容
     * 在删除前会自动恢复所有文件和目录的可写权限
     *
     * @param dirPath 要删除的目录路径
     * @return 是否删除成功
     */
    public static boolean deleteDirectoryRecursively(String dirPath) {
        if (dirPath == null) {
            log.warn("目录路径为null，无法删除");
            return false;
        }
        return deleteDirectoryRecursively(new File(dirPath));
    }

    /**
     * 递归删除目录及其所有内容
     * 移除文件权限保护机制 - 直接删除文件和目录，不再处理权限设置
     *
     * @param directory 要删除的目录
     * @return 是否删除成功
     */
    public static boolean deleteDirectoryRecursively(File directory) {
        if (directory == null || !directory.exists()) {
            return true; // 目录不存在，认为删除成功
        }

        try {
            // 移除权限保护机制 - 直接删除文件和目录，不再设置权限
            // 递归删除子文件和子目录
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        // 递归删除子目录
                        deleteDirectoryRecursively(file);
                    } else {
                        // 直接删除文件，不再设置可写权限
                        if (!file.delete()) {
                            log.warn("删除文件失败: {}", file.getAbsolutePath());
                        }
                    }
                }
            }

            // 删除目录本身
            boolean deleted = directory.delete();
            if (deleted) {
                log.info("递归删除目录成功 - 路径: {}", directory.getAbsolutePath());
            } else {
                log.warn("删除目录失败 - 路径: {}", directory.getAbsolutePath());
            }
            return deleted;

        } catch (Exception e) {
            log.error("递归删除目录失败 - 路径: {}", directory.getAbsolutePath(), e);
            return false;
        }
    }

    /**
     * 验证两个文件是否相同（通过MD5比较）
     */
    public static boolean verifyFilesEqual(String file1Path, String file2Path) throws IOException {
        File file1 = new File(file1Path);
        File file2 = new File(file2Path);

        if (!file1.exists() || !file2.exists()) {
            return false;
        }

        if (file1.length() != file2.length()) {
            return false;
        }

        String md5_1 = FileUtils.calculateFileMD5(file1);
        String md5_2 = FileUtils.calculateFileMD5(file2);

        boolean equal = md5_1.equals(md5_2);

        log.info("文件比较结果 - 文件1: {}, 文件2: {}, 相同: {}", file1Path, file2Path, equal);

        return equal;
    }
}