# Maven构建产物
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IDE文件
## IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/

## Eclipse
.project
.classpath
.settings/
bin/
.metadata/

## Visual Studio Code
.vscode/

## NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# 操作系统文件
## macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.Spotlight-V100
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

## Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp

## Linux
*~
.nfs*

# Java编译产物
*.class
*.log
*.ctxt
.mtj.tmp/
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar
hs_err_pid*

# 日志文件
logs/
*.log
*.log.*
log/

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# Spring Boot
spring.log
# application-local.yml
# application-dev.yml

# 数据库文件
*.db
*.sqlite
*.sqlite3
database.db
/data/
data/

# 文件传输存储目录
/file-storage/
/upload/
/download/
/files/
file-transfer/

# 配置文件（可能包含敏感信息）
application-prod.yml
application-production.yml
*.env
.env
.env.local
.env.production

# 测试相关
test-output/
.tox/
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 文档生成
/docs/_build/
site/

# 打包文件
*.tar
*.gz
*.bz2
*.xz

# 其他
*.orig
.DS_Store?
Thumbs.db
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 本地开发配置
local.properties
gradle.properties 