package com.sdesrd.filetransfer.server.config;

import javax.annotation.PostConstruct;
import java.io.File;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输服务端自动配置类
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(FileTransferProperties.class)
@ComponentScan(basePackages = "com.sdesrd.filetransfer.server")
@MapperScan(basePackages = "com.sdesrd.filetransfer.server.mapper")
@ConditionalOnProperty(prefix = "file.transfer.server", name = "enabled", havingValue = "true", matchIfMissing = true)
public class FileTransferAutoConfiguration {
    
    @Autowired
    private FileTransferProperties properties;
    
    /**
     * 在配置类初始化时就创建必要的目录
     * 确保在数据源Bean创建之前目录已存在
     */
    @PostConstruct
    public void initDirectories() {
        try {
            // 创建数据库目录
            if (properties.getDatabasePath() != null) {
                File dbFile = new File(properties.getDatabasePath());
                File dbDir = dbFile.getParentFile();
                if (dbDir != null && !dbDir.exists()) {
                    boolean created = dbDir.mkdirs();
                    if (created) {
                        log.info("预创建数据库目录: {}", dbDir.getAbsolutePath());
                    } else {
                        log.warn("无法创建数据库目录: {}", dbDir.getAbsolutePath());
                    }
                }
            }
            
            // 创建默认存储目录
            if (properties.getDefaultConfig() != null && properties.getDefaultConfig().getStoragePath() != null) {
                File storageDir = new File(properties.getDefaultConfig().getStoragePath());
                if (!storageDir.exists()) {
                    boolean created = storageDir.mkdirs();
                    if (created) {
                        log.info("预创建默认存储目录: {}", storageDir.getAbsolutePath());
                    }
                }
            }
            
            // 创建用户存储目录
            if (properties.getUsers() != null) {
                properties.getUsers().forEach((username, userConfig) -> {
                    if (userConfig.getStoragePath() != null) {
                        File userStorageDir = new File(userConfig.getStoragePath());
                        if (!userStorageDir.exists()) {
                            boolean created = userStorageDir.mkdirs();
                            if (created) {
                                log.info("预创建用户 {} 存储目录: {}", username, userStorageDir.getAbsolutePath());
                            }
                        }
                    }
                });
            }
            
        } catch (Exception e) {
            log.error("初始化目录失败", e);
            // 不抛出异常，避免影响整个应用启动
        }
    }

    /**
     * 配置文件上传解析器
     */
    @Bean
    @ConditionalOnMissingBean(MultipartResolver.class)
    public MultipartResolver multipartResolver(FileTransferProperties properties) {
        CommonsMultipartResolver resolver = new CommonsMultipartResolver();
        
        // 使用默认配置
        UserConfig defaultConfig = properties.getDefaultConfig();
        
        // 设置最大上传文件大小
        resolver.setMaxUploadSize(defaultConfig.getMaxFileSize());
        
        // 设置最大内存大小
        resolver.setMaxInMemorySize(defaultConfig.getMaxInMemorySize().intValue());
        
        // 设置默认编码
        resolver.setDefaultEncoding("UTF-8");
        
        // 延迟解析文件
        resolver.setResolveLazily(true);
        
        log.info("文件传输服务端SDK - 文件上传解析器配置完成，最大文件大小: {}MB, 最大内存: {}MB", 
                defaultConfig.getMaxFileSize() / 1024 / 1024, 
                defaultConfig.getMaxInMemorySize() / 1024 / 1024);
        
        return resolver;
    }
    
    /**
     * 初始化数据库
     */
    @Bean
    @ConditionalOnMissingBean(DatabaseInitializer.class)
    public DatabaseInitializer databaseInitializer() {
        return new DatabaseInitializer();
    }
} 