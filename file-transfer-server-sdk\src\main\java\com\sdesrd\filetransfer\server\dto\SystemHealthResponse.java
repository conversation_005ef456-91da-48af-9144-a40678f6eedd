package com.sdesrd.filetransfer.server.dto;

import com.sdesrd.filetransfer.server.service.FileTransferMonitorService.TransferStatistics;
import lombok.Data;

/**
 * 系统健康检查响应DTO
 * 包含系统状态、JVM信息、磁盘信息和传输统计
 */
@Data
public class SystemHealthResponse {
    
    /**
     * 系统状态：UP/DOWN
     */
    private String status;
    
    /**
     * 检查时间戳
     */
    private Long timestamp;
    
    /**
     * JVM总内存（字节）
     */
    private Long totalMemory;
    
    /**
     * JVM空闲内存（字节）
     */
    private Long freeMemory;
    
    /**
     * JVM已使用内存（字节）
     */
    private Long usedMemory;
    
    /**
     * JVM最大内存（字节）
     */
    private Long maxMemory;
    
    /**
     * 磁盘总空间（字节）
     */
    private Long totalSpace;
    
    /**
     * 磁盘空闲空间（字节）
     */
    private Long freeSpace;
    
    /**
     * 磁盘可用空间（字节）
     * 与freeSpace的区别：usableSpace考虑了权限和其他限制
     */
    private Long usableSpace;
    
    /**
     * 传输统计信息
     */
    private TransferStatistics transferStats;
    
    /**
     * 获取内存使用率（百分比）
     */
    public double getMemoryUsagePercentage() {
        if (maxMemory == null || maxMemory == 0) {
            return 0.0;
        }
        return (double) usedMemory / maxMemory * 100;
    }
    
    /**
     * 获取磁盘使用率（百分比）
     */
    public double getDiskUsagePercentage() {
        if (totalSpace == null || totalSpace == 0) {
            return 0.0;
        }
        return (double) (totalSpace - freeSpace) / totalSpace * 100;
    }
    
    /**
     * 获取格式化的内存信息
     */
    public String getFormattedMemoryInfo() {
        return String.format("已使用: %dMB / 最大: %dMB (%.2f%%)", 
                usedMemory / 1024 / 1024, 
                maxMemory / 1024 / 1024, 
                getMemoryUsagePercentage());
    }
    
    /**
     * 获取格式化的磁盘信息
     */
    public String getFormattedDiskInfo() {
        if (totalSpace == null || freeSpace == null) {
            return "磁盘信息不可用";
        }
        return String.format("已使用: %.2fGB / 总计: %.2fGB (%.2f%%)", 
                (totalSpace - freeSpace) / (1024.0 * 1024.0 * 1024.0),
                totalSpace / (1024.0 * 1024.0 * 1024.0), 
                getDiskUsagePercentage());
    }
}
