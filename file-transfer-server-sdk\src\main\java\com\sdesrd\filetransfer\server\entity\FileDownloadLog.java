package com.sdesrd.filetransfer.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 文件下载日志实体类
 * 
 * 记录文件下载操作的详细信息，用于审计和统计分析
 * 
 * <AUTHOR> Transfer SDK
 * @since 1.0.0
 */
@Data
@TableName("file_download_log")
public class FileDownloadLog {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 文件标识符
     */
    @TableField("file_id")
    private String fileId;
    
    /**
     * 原始文件名
     */
    @TableField("file_name")
    private String fileName;
    
    /**
     * 用户名
     */
    @TableField("username")
    private String username;
    
    /**
     * 客户端IP地址
     */
    @TableField("client_ip")
    private String clientIp;
    
    /**
     * 用户代理字符串
     */
    @TableField("user_agent")
    private String userAgent;
    
    /**
     * 下载开始时间
     */
    @TableField("download_start_time")
    private String downloadStartTime;
    
    /**
     * 下载结束时间
     */
    @TableField("download_end_time")
    private String downloadEndTime;
    
    /**
     * 下载的字节数
     */
    @TableField("download_size")
    private Long downloadSize;
    
    /**
     * 文件总大小
     */
    @TableField("file_size")
    private Long fileSize;
    
    /**
     * 下载状态
     * 0: 进行中
     * 1: 成功
     * 2: 失败
     * 3: 取消
     */
    @TableField("download_status")
    private Integer downloadStatus;
    
    /**
     * 下载耗时（毫秒）
     */
    @TableField("duration_ms")
    private Long durationMs;
    
    /**
     * 下载速度（字节/秒）
     */
    @TableField("download_speed")
    private Long downloadSpeed;
    
    /**
     * 错误信息（如果下载失败）
     */
    @TableField("error_message")
    private String errorMessage;
    
    /**
     * HTTP状态码
     */
    @TableField("http_status")
    private Integer httpStatus;
    
    /**
     * 是否为断点续传
     */
    @TableField("is_resume")
    private Boolean isResume;
    
    /**
     * Range请求的起始位置
     */
    @TableField("range_start")
    private Long rangeStart;
    
    /**
     * Range请求的结束位置
     */
    @TableField("range_end")
    private Long rangeEnd;
    
    /**
     * 记录创建时间
     */
    @TableField("create_time")
    private String createTime;
    
    /**
     * 记录更新时间
     */
    @TableField("update_time")
    private String updateTime;
    
    /**
     * 扩展信息（JSON格式）
     */
    @TableField("ext_info")
    private String extInfo;
    
    // ==================== 常量定义 ====================
    
    /**
     * 下载状态：进行中
     */
    public static final int STATUS_IN_PROGRESS = 0;
    
    /**
     * 下载状态：成功
     */
    public static final int STATUS_SUCCESS = 1;
    
    /**
     * 下载状态：失败
     */
    public static final int STATUS_FAILED = 2;
    
    /**
     * 下载状态：取消
     */
    public static final int STATUS_CANCELLED = 3;
    
    // ==================== 便捷方法 ====================
    
    /**
     * 检查下载是否成功
     */
    public boolean isSuccess() {
        return STATUS_SUCCESS == downloadStatus;
    }
    
    /**
     * 检查下载是否失败
     */
    public boolean isFailed() {
        return STATUS_FAILED == downloadStatus;
    }
    
    /**
     * 检查下载是否正在进行
     */
    public boolean isInProgress() {
        return STATUS_IN_PROGRESS == downloadStatus;
    }
    
    /**
     * 检查下载是否被取消
     */
    public boolean isCancelled() {
        return STATUS_CANCELLED == downloadStatus;
    }
    
    /**
     * 计算下载完成百分比
     */
    public double getDownloadPercentage() {
        if (fileSize == null || fileSize <= 0 || downloadSize == null) {
            return 0.0;
        }
        return Math.min(100.0, (double) downloadSize / fileSize * 100);
    }
    
    /**
     * 获取格式化的下载速度
     */
    public String getFormattedDownloadSpeed() {
        if (downloadSpeed == null || downloadSpeed <= 0) {
            return "0 B/s";
        }
        
        if (downloadSpeed < 1024) {
            return downloadSpeed + " B/s";
        } else if (downloadSpeed < 1024 * 1024) {
            return String.format("%.2f KB/s", downloadSpeed / 1024.0);
        } else if (downloadSpeed < 1024 * 1024 * 1024) {
            return String.format("%.2f MB/s", downloadSpeed / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB/s", downloadSpeed / (1024.0 * 1024 * 1024));
        }
    }
    
    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSize == null || fileSize <= 0) {
            return "0 B";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.2f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", fileSize / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", fileSize / (1024.0 * 1024 * 1024));
        }
    }
    
    /**
     * 获取格式化的下载大小
     */
    public String getFormattedDownloadSize() {
        if (downloadSize == null || downloadSize <= 0) {
            return "0 B";
        }
        
        if (downloadSize < 1024) {
            return downloadSize + " B";
        } else if (downloadSize < 1024 * 1024) {
            return String.format("%.2f KB", downloadSize / 1024.0);
        } else if (downloadSize < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", downloadSize / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", downloadSize / (1024.0 * 1024 * 1024));
        }
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        switch (downloadStatus) {
            case STATUS_IN_PROGRESS:
                return "下载中";
            case STATUS_SUCCESS:
                return "成功";
            case STATUS_FAILED:
                return "失败";
            case STATUS_CANCELLED:
                return "已取消";
            default:
                return "未知";
        }
    }
}
