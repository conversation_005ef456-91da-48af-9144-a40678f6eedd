package com.sdesrd.filetransfer.client.config;

import lombok.Data;

/**
 * 客户端统一配置类
 * 包含连接、认证和传输等所有配置项，提供简洁统一的配置接口
 */
@Data
public class ClientConfig {
    
    // ==================== 服务器连接配置 ====================
    
    /**
     * 服务器地址，默认localhost
     */
    private String serverAddr = "localhost";

    /**
     * 服务器端口，默认49011
     */
    private int serverPort = 49011;

    /**
     * 服务器上下文路径，默认为"filetransfer"
     * 与服务端的server.servlet.context-path配置保持一致
     * 例如：如果服务部署在 /filetransfer 下，则设置为 "filetransfer"
     * 注意：不要包含开头和结尾的斜杠
     */
    private String contextPath = "filetransfer";

    /**
     * 是否使用HTTPS协议，默认false
     */
    private boolean useHttps = false;
    
    // ==================== 用户认证配置 ====================

    /**
     * 用户名（必填）
     */
    private String user;

    /**
     * 用户密钥（必填）
     */
    private String secretKey;
    
    // ==================== 传输性能配置 ====================
    
    /**
     * 分块大小（字节），默认2MB
     * 将文件拆分为固定大小的片段进行上传或下载，每个片段作为独立 HTTP 请求发送
     * 过小会产生过多请求开销，过大可能导致内存压力或重试代价增加
     */
    private long chunkSize = 2 * 1024 * 1024L;
    
    /**
     * 连接超时时间（秒），默认30秒
     * 在建立到服务器的 TCP 连接时，客户端等待对方响应的最长时间
     * 超时后将抛出连接超时异常，避免网络故障或服务器不可达时长时间阻塞
     */
    private long connectTimeoutSeconds = 30;
    
    /**
     * 读取超时时间（秒），默认60秒
     * 在发送请求后，等待服务器返回数据时，两次数据包之间允许的最大间隔
     * 超时后将抛出读取超时异常，防止因网络卡顿或服务器无响应而挂起
     */
    private long readTimeoutSeconds = 60;
    
    /**
     * 写入超时时间（秒），默认60秒
     * 在发送请求体（如文件分块）时，两次写入操作之间允许的最大间隔
     * 超时后将抛出写入超时异常，防止上传过程因网络阻塞而卡死
     */
    private long writeTimeoutSeconds = 60;
    
    // ==================== 并发控制配置 ====================
    
    /**
     * 最大并发传输数，默认3
     * 同时进行的上传或下载任务线程上限
     * 过高可能导致资源争用和网络拥塞，过低则无法充分利用带宽和多线程优势
     */
    private int maxConcurrentTransfers = 3;
    
    /**
     * 最大空闲连接数，默认5
     * OkHttp 连接池中保持的可复用空闲连接数量上限
     * 保持连接复用可减少握手开销，过多会占用资源，过少会频繁重建连接导致延迟
     */
    private int maxIdleConnections = 5;
    
    /**
     * 连接保活时间（分钟），默认5分钟
     * 空闲连接在连接池中保存可复用的最长时长
     * 超出后空闲连接将被清理，避免长时间占用系统资源
     */
    private long keepAliveDurationMinutes = 5;
    
    // ==================== 重试策略配置 ====================
    
    /**
     * 重试次数，默认3次
     * 当上传或下载操作失败且实现逻辑支持时，允许重新尝试的最大次数
     * 当前默认 RetryManager 策略使用指数退避和抖动，若需简单重试，可参考此参数
     */
    private int retryCount = 3;
    
    /**
     * 重试间隔（毫秒），默认1000毫秒
     * 在连续失败后等待再发起下次尝试的时间间隔
     * 配合指数退避或抖动策略，可有效减少高并发下的重试冲击
     */
    private long retryIntervalMs = 1000;



    // ==================== 业务方法 ====================

    /**
     * 获取完整的服务器URL
     *
     * @return 格式化后的服务器基础URL
     */
    public String getServerUrl() {
        // 验证必要参数
        if (serverAddr == null || serverAddr.trim().isEmpty()) {
            throw new IllegalStateException("服务器地址不能为空");
        }
        if (serverPort <= 0 || serverPort > 65535) {
            throw new IllegalStateException("服务器端口必须在1-65535范围内");
        }

        // 确保contextPath格式正确
        String path = normalizeContextPath(contextPath);

        // 选择协议
        String protocol = useHttps ? "https" : "http";

        // 构建基础URL
        String baseUrl = String.format("%s://%s:%d", protocol, serverAddr.trim(), serverPort);

        // 如果有contextPath，则添加
        if (!path.isEmpty()) {
            baseUrl += "/" + path;
        }

        return baseUrl;
    }

    /**
     * 标准化上下文路径
     *
     * @param contextPath 原始上下文路径
     * @return 标准化后的上下文路径
     */
    private String normalizeContextPath(String contextPath) {
        if (contextPath == null) {
            return "";
        }

        String path = contextPath.trim();

        // 移除开头的斜杠
        while (path.startsWith("/")) {
            path = path.substring(1);
        }

        // 移除结尾的斜杠
        while (path.endsWith("/")) {
            path = path.substring(0, path.length() - 1);
        }

        return path;
    }

    /**
     * 验证完整配置的有效性
     *
     * @throws IllegalStateException 如果配置不完整或无效
     */
    public void validateConfig() {
        // 验证服务器连接配置
        if (serverAddr == null || serverAddr.trim().isEmpty()) {
            throw new IllegalStateException("服务器地址不能为空");
        }
        if (serverPort <= 0 || serverPort > 65535) {
            throw new IllegalStateException("服务器端口必须在1-65535范围内");
        }
        
        // 验证认证配置
        if (user == null || user.trim().isEmpty()) {
            throw new IllegalStateException("用户名不能为空");
        }
        if (secretKey == null || secretKey.trim().isEmpty()) {
            throw new IllegalStateException("用户密钥不能为空");
        }

        // 验证传输配置
        if (chunkSize <= 0) {
            throw new IllegalStateException("分片大小必须大于0");
        }
        if (connectTimeoutSeconds <= 0) {
            throw new IllegalStateException("连接超时时间必须大于0秒");
        }
        if (readTimeoutSeconds <= 0) {
            throw new IllegalStateException("读取超时时间必须大于0秒");
        }
        if (writeTimeoutSeconds <= 0) {
            throw new IllegalStateException("写入超时时间必须大于0秒");
        }
        if (maxConcurrentTransfers <= 0) {
            throw new IllegalStateException("最大并发传输数必须大于0");
        }
        if (maxIdleConnections <= 0) {
            throw new IllegalStateException("最大空闲连接数必须大于0");
        }
        if (keepAliveDurationMinutes <= 0) {
            throw new IllegalStateException("连接保活时间必须大于0分钟");
        }
        if (retryCount < 0) {
            throw new IllegalStateException("重试次数不能为负数");
        }
        if (retryIntervalMs < 0) {
            throw new IllegalStateException("重试间隔不能为负数");
        }
    }


} 