package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.config.UserConfig;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;
import com.sdesrd.filetransfer.server.service.DatabaseManagementService.BackupFileInfo;
import com.sdesrd.filetransfer.server.service.DatabaseManagementService.DatabaseHealthInfo;
import com.sdesrd.filetransfer.server.service.DatabaseManagementService.DatabaseRebuildResult;

import javax.sql.DataSource;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 数据库管理服务测试
 */
@DisplayName("数据库管理服务测试")
class DatabaseManagementServiceTest {
    
    @TempDir
    Path tempDir;
    
    @Mock
    private FileTransferProperties properties;
    
    @Mock
    private DataSource dataSource;
    
    @Mock
    private FileTransferRecordMapper transferRecordMapper;

    @Mock
    private DatabaseFallbackService databaseFallbackService;

    @InjectMocks
    private DatabaseManagementService databaseManagementService;
    
    private File testDatabaseFile;
    private String testDatabasePath;
    
    @BeforeEach
    void setUp() throws IOException {
        MockitoAnnotations.openMocks(this);
        
        // 创建测试数据库文件
        testDatabaseFile = tempDir.resolve("test-database.db").toFile();
        testDatabasePath = testDatabaseFile.getAbsolutePath();
        Files.write(testDatabaseFile.toPath(), "测试数据库内容".getBytes());
        
        // 配置mock对象
        when(properties.getDatabasePath()).thenReturn(testDatabasePath);
    }
    
    @AfterEach
    void tearDown() {
        // 清理资源
        if (databaseManagementService != null) {
            try {
                databaseManagementService.destroy();
            } catch (Exception e) {
                // 忽略清理异常
            }
        }
    }
    
    @Test
    @DisplayName("检查数据库健康状态测试")
    void testCheckDatabaseHealth() throws Exception {
        // 模拟数据库连接
        when(dataSource.getConnection()).thenReturn(mock(java.sql.Connection.class));
        when(transferRecordMapper.selectCount(any())).thenReturn(100L);
        
        // 执行健康检查
        DatabaseHealthInfo healthInfo = databaseManagementService.checkDatabaseHealth();
        
        // 验证结果
        assertNotNull(healthInfo, "健康信息不应为null");
        assertTrue(healthInfo.isConnectionAvailable(), "数据库连接应可用");
        assertTrue(healthInfo.isDatabaseFileExists(), "数据库文件应存在");
        assertEquals(testDatabasePath, healthInfo.getDatabasePath(), "数据库路径应正确");
        assertTrue(healthInfo.getDatabaseFileSize() > 0, "数据库文件大小应大于0");
        assertEquals(100L, healthInfo.getTotalRecords(), "记录总数应正确");
        assertNotNull(healthInfo.getCheckTime(), "检查时间不应为null");
    }
    
    @Test
    @DisplayName("创建数据库备份测试")
    void testCreateBackup() throws IOException {
        // 执行备份
        String backupPath = databaseManagementService.createBackup();
        
        // 验证结果
        assertNotNull(backupPath, "备份路径不应为null");
        
        File backupFile = new File(backupPath);
        assertTrue(backupFile.exists(), "备份文件应存在");
        assertTrue(backupFile.isFile(), "备份应为文件");
        assertTrue(backupFile.length() > 0, "备份文件大小应大于0");
        assertTrue(backupPath.contains("database_"), "备份文件名应包含前缀");
        assertTrue(backupPath.endsWith(".backup.db"), "备份文件应有正确后缀");
    }
    
    @Test
    @DisplayName("获取备份文件列表测试")
    void testListBackups() throws IOException, InterruptedException {
        // 确保备份目录存在
        File dbFile = new File(testDatabasePath);
        File backupDir = new File(dbFile.getParentFile(), "backups");
        if (!backupDir.exists()) {
            backupDir.mkdirs();
        }

        // 先创建几个备份文件，增加时间间隔确保时间戳不同
        String backup1 = databaseManagementService.createBackup();
        assertNotNull(backup1, "第一个备份应创建成功");

        Thread.sleep(1000); // 增加时间间隔确保时间戳不同

        String backup2 = databaseManagementService.createBackup();
        assertNotNull(backup2, "第二个备份应创建成功");

        // 验证备份文件确实存在
        assertTrue(new File(backup1).exists(), "第一个备份文件应存在");
        assertTrue(new File(backup2).exists(), "第二个备份文件应存在");

        // 获取备份列表
        List<BackupFileInfo> backups = databaseManagementService.listBackups();

        // 验证结果
        assertNotNull(backups, "备份列表不应为null");

        // 添加调试信息
        System.out.println("备份目录: " + backupDir.getAbsolutePath());
        System.out.println("备份文件数量: " + backups.size());
        for (BackupFileInfo backup : backups) {
            System.out.println("备份文件: " + backup.getFileName() + " -> " + backup.getFilePath());
        }

        assertTrue(backups.size() >= 2,
            String.format("应至少有2个备份文件，实际数量: %d，备份目录: %s",
                backups.size(), backupDir.getAbsolutePath()));

        // 验证备份信息
        for (BackupFileInfo backup : backups) {
            assertNotNull(backup.getFileName(), "备份文件名不应为null");
            assertNotNull(backup.getFilePath(), "备份文件路径不应为null");
            assertTrue(backup.getFileSize() > 0, "备份文件大小应大于0");
            assertTrue(backup.getCreateTime() > 0, "备份创建时间应大于0");
            assertNotNull(backup.getFormattedSize(), "格式化大小不应为null");
            assertNotNull(backup.getFormattedTime(), "格式化时间不应为null");
        }

        // 验证按时间倒序排列
        if (backups.size() > 1) {
            assertTrue(backups.get(0).getCreateTime() >= backups.get(1).getCreateTime(),
                "备份应按创建时间倒序排列");
        }
    }
    
    @Test
    @DisplayName("获取备份文件测试")
    void testGetBackupFile() throws IOException {
        // 先创建备份
        String backupPath = databaseManagementService.createBackup();
        String backupFileName = new File(backupPath).getName();
        
        // 获取备份文件
        File backupFile = databaseManagementService.getBackupFile(backupFileName);
        
        // 验证结果
        assertNotNull(backupFile, "备份文件不应为null");
        assertTrue(backupFile.exists(), "备份文件应存在");
        assertEquals(backupFileName, backupFile.getName(), "文件名应匹配");
    }
    
    @Test
    @DisplayName("获取不存在的备份文件测试")
    void testGetNonExistentBackupFile() {
        // 尝试获取不存在的备份文件
        assertThrows(Exception.class, () -> {
            databaseManagementService.getBackupFile("non-existent.backup.db");
        }, "获取不存在的备份文件应抛出异常");
    }
    
    @Test
    @DisplayName("获取非法备份文件名测试")
    void testGetInvalidBackupFileName() {
        // 测试路径遍历攻击
        assertThrows(Exception.class, () -> {
            databaseManagementService.getBackupFile("../../../etc/passwd");
        }, "非法文件路径应抛出异常");
        
        // 测试非备份文件格式
        assertThrows(Exception.class, () -> {
            databaseManagementService.getBackupFile("normal-file.txt");
        }, "非备份文件格式应抛出异常");
    }
    
    @Test
    @DisplayName("重建数据库测试")
    void testRebuildDatabaseFromDisk() throws IOException {
        // 配置存储路径
        String storagePath = tempDir.resolve("storage").toString();
        File storageDir = new File(storagePath);
        storageDir.mkdirs();

        UserConfig defaultConfig = new UserConfig();
        defaultConfig.setStoragePath(storagePath);
        when(properties.getDefaultConfig()).thenReturn(defaultConfig);

        // 创建测试文件结构
        createTestFileStructure(storageDir);

        // Mock DatabaseFallbackService的扫描结果
        Map<String, Object> mockScanResult = new HashMap<>();
        mockScanResult.put("success", true);
        mockScanResult.put("scannedFiles", 5);
        mockScanResult.put("rebuiltRecords", 3);
        mockScanResult.put("skippedFiles", 2);
        mockScanResult.put("errors", new ArrayList<>());
        mockScanResult.put("userResults", new HashMap<>());

        when(databaseFallbackService.scanAndRebuildFromMetadata()).thenReturn(mockScanResult);

        // 执行重建
        DatabaseRebuildResult result = databaseManagementService.rebuildDatabaseFromDisk();

        // 验证结果
        assertNotNull(result, "重建结果不应为null");
        assertTrue(result.isSuccess(), "重建应成功");
        assertNotNull(result.getStartTime(), "开始时间不应为null");
        assertNotNull(result.getEndTime(), "结束时间不应为null");
        assertEquals(5, result.getScannedFiles(), "扫描文件数应正确");
        assertEquals(3, result.getRebuiltRecords(), "重建记录数应正确");
        assertEquals(2, result.getFailedFiles(), "失败文件数应正确");

        // 验证DatabaseFallbackService被调用
        verify(databaseFallbackService, times(1)).scanAndRebuildFromMetadata();
    }
    
    @Test
    @DisplayName("数据库健康信息数据结构测试")
    void testDatabaseHealthInfoDataStructure() {
        DatabaseHealthInfo healthInfo = new DatabaseHealthInfo();
        
        // 测试setter和getter
        healthInfo.setConnectionAvailable(true);
        assertTrue(healthInfo.isConnectionAvailable());
        
        healthInfo.setDatabaseFileExists(true);
        assertTrue(healthInfo.isDatabaseFileExists());
        
        healthInfo.setTablesAccessible(true);
        assertTrue(healthInfo.isTablesAccessible());
        
        healthInfo.setDatabasePath("/test/path");
        assertEquals("/test/path", healthInfo.getDatabasePath());
        
        healthInfo.setDatabaseFileSize(1024L);
        assertEquals(1024L, healthInfo.getDatabaseFileSize());
        
        healthInfo.setTotalRecords(100L);
        assertEquals(100L, healthInfo.getTotalRecords());
        
        healthInfo.setCheckTime("2023-12-31T00:00:00Z");
        assertEquals("2023-12-31T00:00:00Z", healthInfo.getCheckTime());
        
        healthInfo.setErrorMessage("测试错误");
        assertEquals("测试错误", healthInfo.getErrorMessage());
    }
    
    @Test
    @DisplayName("备份文件信息数据结构测试")
    void testBackupFileInfoDataStructure() {
        BackupFileInfo backupInfo = new BackupFileInfo();
        
        // 测试setter和getter
        backupInfo.setFileName("test.backup.db");
        assertEquals("test.backup.db", backupInfo.getFileName());
        
        backupInfo.setFilePath("/test/path/test.backup.db");
        assertEquals("/test/path/test.backup.db", backupInfo.getFilePath());
        
        backupInfo.setFileSize(2048L);
        assertEquals(2048L, backupInfo.getFileSize());
        
        backupInfo.setCreateTime(1703980800000L);
        assertEquals(1703980800000L, backupInfo.getCreateTime());
        
        backupInfo.setFormattedSize("2.0 KB");
        assertEquals("2.0 KB", backupInfo.getFormattedSize());
        
        backupInfo.setFormattedTime("2023-12-31 00:00:00");
        assertEquals("2023-12-31 00:00:00", backupInfo.getFormattedTime());
    }
    
    @Test
    @DisplayName("数据库重建结果数据结构测试")
    void testDatabaseRebuildResultDataStructure() {
        DatabaseRebuildResult result = new DatabaseRebuildResult();
        
        // 测试setter和getter
        result.setSuccess(true);
        assertTrue(result.isSuccess());
        
        result.setStartTime("2023-12-31T00:00:00Z");
        assertEquals("2023-12-31T00:00:00Z", result.getStartTime());
        
        result.setEndTime("2023-12-31T00:01:00Z");
        assertEquals("2023-12-31T00:01:00Z", result.getEndTime());
        
        result.setScannedFiles(100);
        assertEquals(100, result.getScannedFiles());
        
        result.setRebuiltRecords(80);
        assertEquals(80, result.getRebuiltRecords());
        
        result.setFailedFiles(20);
        assertEquals(20, result.getFailedFiles());
        
        result.setErrorMessage("测试错误");
        assertEquals("测试错误", result.getErrorMessage());
    }
    
    /**
     * 创建测试文件结构
     */
    private void createTestFileStructure(File storageDir) throws IOException {
        // 创建一些测试文件，模拟真实的存储结构
        // 这里简化处理，实际测试中可能需要更复杂的结构
        File testSubDir = new File(storageDir, "test");
        testSubDir.mkdirs();
        
        File testFile = new File(testSubDir, "test-file.txt");
        Files.write(testFile.toPath(), "测试文件内容".getBytes());
    }
}
