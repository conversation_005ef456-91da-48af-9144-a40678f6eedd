﻿# ================================================================================
# 文件传输SDK简化构建和测试脚本（PowerShell版本）
# ================================================================================

param(
    [string]$Mode = "build-test",
    [string]$JavaHome = "",
    [switch]$Help
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# ==================== 常量定义 ====================

# 脚本版本信息
$SCRIPT_VERSION = "3.0.0"
$SCRIPT_NAME = "文件传输SDK简化构建和测试脚本"

# 项目模块列表
$PROJECT_MODULES = @(
    "file-transfer-server-sdk",
    "file-transfer-client-sdk", 
    "file-transfer-client-demo"
)

# 需要单元测试的模块列表
$TEST_MODULES = @(
    "file-transfer-server-sdk",
    "file-transfer-client-sdk"
)

# 演示模块列表
$DEMO_MODULES = @("file-transfer-client-demo")

# 独立服务模块
$STANDALONE_MODULE = "file-transfer-server-standalone"

# 超时配置常量（秒）
$BUILD_TIMEOUT_SECONDS = 600
$TEST_TIMEOUT_SECONDS = 1200
$SERVER_STARTUP_TIMEOUT_SECONDS = 30
$SERVER_SHUTDOWN_TIMEOUT_SECONDS = 15
$DEMO_TEST_TIMEOUT_SECONDS = 300

# 端口配置
$TEST_SERVER_PORT = 49011

# 目录配置
$LOG_DIR = ".\logs"
$MAIN_LOG = "$LOG_DIR\build-and-test-$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
$SERVER_PID_FILE = "$LOG_DIR\test-server.pid"

# 执行模式常量
$MODE_BUILD = "build"
$MODE_BUILD_TEST = "build-test"

# ==================== 日志函数 ====================

function Write-InfoLog {
    param([string]$Message)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[INFO] $timestamp - $Message" -ForegroundColor Blue
    Add-Content -Path $MAIN_LOG -Value "[INFO] $timestamp - $Message"
}

function Write-SuccessLog {
    param([string]$Message)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[SUCCESS] $timestamp - $Message" -ForegroundColor Green
    Add-Content -Path $MAIN_LOG -Value "[SUCCESS] $timestamp - $Message"
}

function Write-WarningLog {
    param([string]$Message)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[WARNING] $timestamp - $Message" -ForegroundColor Yellow
    Add-Content -Path $MAIN_LOG -Value "[WARNING] $timestamp - $Message"
}

function Write-ErrorLog {
    param([string]$Message)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[ERROR] $timestamp - $Message" -ForegroundColor Red
    Add-Content -Path $MAIN_LOG -Value "[ERROR] $timestamp - $Message"
}

function Write-StepLog {
    param([int]$StepNumber, [string]$StepName)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[STEP $StepNumber] $timestamp - $StepName" -ForegroundColor Magenta
    Add-Content -Path $MAIN_LOG -Value "[STEP $StepNumber] $timestamp - $StepName"
    Add-Content -Path $MAIN_LOG -Value "========================================"
}

function Write-TestPhaseLog {
    param([string]$PhaseName)
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Write-Host "[TEST_PHASE] $timestamp - $PhaseName" -ForegroundColor Cyan
    Add-Content -Path $MAIN_LOG -Value "[TEST_PHASE] $timestamp - $PhaseName"
    Add-Content -Path $MAIN_LOG -Value "----------------------------------------"
}

# ==================== 工具函数 ====================

function Initialize-Logging {
    if (!(Test-Path $LOG_DIR)) {
        New-Item -ItemType Directory -Path $LOG_DIR -Force | Out-Null
    }
    New-Item -ItemType File -Path $MAIN_LOG -Force | Out-Null
    Write-InfoLog "主日志文件：$MAIN_LOG"
}

function Show-Header {
    Write-Host "========================================================" -ForegroundColor White
    Write-Host "    $SCRIPT_NAME" -ForegroundColor White
    Write-Host "    版本：$SCRIPT_VERSION" -ForegroundColor White
    Write-Host "    时间：$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
    Write-Host "========================================================" -ForegroundColor White
}

function Test-Command {
    param([string]$Command, [string]$Description)
    
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        Write-ErrorLog "$Description 未安装或未在PATH中：$Command"
        return $false
    }
}

function Test-PortAvailable {
    param([int]$Port)
    
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $Port -InformationLevel Quiet -WarningAction SilentlyContinue
        return !$connection
    } catch {
        return $true
    }
}

# ==================== 环境检查函数 ====================

function Repair-JdkStructure {
    param([string]$JavaHome)
    
    if (!$JavaHome -or !(Test-Path $JavaHome)) {
        return
    }
   
    # tzmappings 是 Java 时区系统的兼容性映射文件，用于将 传统的 Windows 时区 ID 映射到 标准的 IANA 时区 ID
    # linux 下一般无需此文件
    $requiredFiles = @("currency.data", "tzdb.dat", "tzmappings")
    $filesToCopy = @()
    
    # 检查lib目录下是否缺少必需文件
    foreach ($file in $requiredFiles) {
        $libFilePath = Join-Path "$JavaHome\lib" $file
        $jreFilePath = Join-Path "$JavaHome\jre\lib" $file
        
        if (!(Test-Path $libFilePath) -and (Test-Path $jreFilePath)) {
            $filesToCopy += @{
                Source = $jreFilePath
                Target = $libFilePath
                FileName = $file
            }
        }
    }
    
    if ($filesToCopy.Count -gt 0) {
        Write-InfoLog "检测到JDK结构不完整，需要修复以下文件："
        
        foreach ($fileInfo in $filesToCopy) {
            Write-InfoLog "  缺少文件：$($fileInfo.FileName)"
            Write-InfoLog "  源文件：$($fileInfo.Source)"
            Write-InfoLog "  目标位置：$($fileInfo.Target)"
            
            try {
                # 确保目标目录存在
                $targetDir = Split-Path $fileInfo.Target -Parent
                if (!(Test-Path $targetDir)) {
                    New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
                }
                
                # 复制文件
                Copy-Item -Path $fileInfo.Source -Destination $fileInfo.Target -Force
                Write-SuccessLog "成功复制文件：$($fileInfo.FileName)"
                
                # 验证复制结果
                if (Test-Path $fileInfo.Target) {
                    $sourceSize = (Get-Item $fileInfo.Source).Length
                    $targetSize = (Get-Item $fileInfo.Target).Length
                    if ($sourceSize -eq $targetSize) {
                        Write-InfoLog "文件完整性验证通过：$($fileInfo.FileName) ($sourceSize 字节)"
                    } else {
                        Write-WarningLog "文件大小不匹配：$($fileInfo.FileName)"
                    }
                } else {
                    Write-ErrorLog "文件复制失败：$($fileInfo.FileName)"
                }
            } catch {
                Write-ErrorLog "复制文件失败：$($fileInfo.FileName) - $($_.Exception.Message)"
            }
        }
        
        Write-SuccessLog "JDK结构修复完成，共处理 $($filesToCopy.Count) 个文件"
    } else {
        # 检查文件是否存在
        $missingFiles = @()
        foreach ($file in $requiredFiles) {
            $libFilePath = Join-Path "$JavaHome\lib" $file
            if (!(Test-Path $libFilePath)) {
                $missingFiles += $file
            }
        }
        
        if ($missingFiles.Count -eq 0) {
            Write-InfoLog "JDK结构检查通过，所有必需文件都存在"
        } else {
            Write-WarningLog "JDK结构检查发现缺少文件：$($missingFiles -join ', ')"
            Write-WarningLog "这可能导致时区或货币相关功能异常"
        }
    }
}

function Setup-JavaEnvironment {
    param([string]$CustomJavaHome)
    
    Write-StepLog 1 "设置Java环境"
    
    if ($CustomJavaHome) {
        if ((Test-Path $CustomJavaHome) -and (Test-Path "$CustomJavaHome\bin\java.exe")) {
            $env:JAVA_HOME = $CustomJavaHome
            $env:PATH = "`"$CustomJavaHome\bin`";$env:PATH"
            $env:MAVEN_OPTS = "-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
            $env:MAVEN_OPTS = "$env:MAVEN_OPTS -Djava.security.policy=all.policy"
            $env:MAVEN_OPTS = "$env:MAVEN_OPTS `"-Djava.home=$env:JAVA_HOME`""
            
            # 检查并修复JDK结构问题
            Repair-JdkStructure $CustomJavaHome
            
            Write-InfoLog "使用指定的Java JDK：$CustomJavaHome"
        } else {
            Write-ErrorLog "指定的Java JDK路径无效：$CustomJavaHome"
            return $false
        }
    } else {
        # 使用系统JAVA_HOME环境变量
        if ($env:JAVA_HOME -and (Test-Path "$env:JAVA_HOME\bin\java.exe")) {
            Write-InfoLog "使用系统JAVA_HOME环境变量：$env:JAVA_HOME"
            $env:MAVEN_OPTS = "-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
            $env:MAVEN_OPTS = "$env:MAVEN_OPTS `"-Djava.home=$env:JAVA_HOME`""
            
            # 检查并修复JDK结构问题
            Repair-JdkStructure $env:JAVA_HOME
            
            # 检查是否有javadoc命令
            if (!(Test-Path "$env:JAVA_HOME\bin\javadoc.exe")) {
                Write-InfoLog "javadoc命令不可用，将跳过Javadoc生成"
                $env:MAVEN_OPTS = "$env:MAVEN_OPTS -Dmaven.javadoc.skip=true"
            }
        } else {
            Write-InfoLog "JAVA_HOME未设置或无效，使用系统默认Java环境"
            # 设置基本的Maven选项，让Maven使用PATH中的Java
            $env:MAVEN_OPTS = "-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
            $env:MAVEN_OPTS = "$env:MAVEN_OPTS -Dmaven.javadoc.skip=true"
        }
    }
    
    if (!(Test-Command "java" "Java运行时")) {
        return $false
    }
    
    try {
        $javaVersionOutput = & java -version 2>&1
        $javaVersion = $javaVersionOutput[0] -replace '^.*\"(.*)\".*$', '$1'
        Write-InfoLog "当前Java版本：$javaVersion"
        
        if ($javaVersion -match "1\.8\.") {
            Write-SuccessLog "使用Java 8，完全兼容"
        } elseif ($javaVersion -match "(11|17|21)\.") {
            Write-InfoLog "使用Java $($Matches[0])，项目配置为Java 8，但应该向后兼容"
        } else {
            Write-InfoLog "当前Java版本：$javaVersion"
        }
    } catch {
        Write-InfoLog "Java命令可用，但无法解析版本信息"
    }
    
    return $true
}

function Test-MavenEnvironment {
    Write-StepLog 2 "检查Maven环境"
    
    if (!(Test-Command "mvn" "Apache Maven")) {
        return $false
    }
    
    try {
        $mavenVersion = & mvn -version | Select-Object -First 1
        Write-InfoLog "Maven版本：$mavenVersion"
    } catch {
        Write-WarningLog "无法获取Maven版本信息"
    }
    
    if (!$env:MAVEN_OPTS) {
        Write-InfoLog "MAVEN_OPTS未设置，使用默认配置"
        $env:MAVEN_OPTS = "-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
        
        if ($env:JAVA_HOME) {
            # 检查并修复JDK结构问题
            Repair-JdkStructure $env:JAVA_HOME
            
            $env:MAVEN_OPTS = "$env:MAVEN_OPTS `"-Djava.home=$env:JAVA_HOME`""
            Write-InfoLog "Maven配置使用Java：$env:JAVA_HOME"
        }
        
        if ($env:JAVA_HOME -and !(Test-Path "$env:JAVA_HOME\bin\javadoc.exe")) {
            Write-WarningLog "javadoc命令不可用，将跳过Javadoc生成"
            $env:MAVEN_OPTS = "$env:MAVEN_OPTS -Dmaven.javadoc.skip=true"
        } elseif (!$env:JAVA_HOME) {
            Write-WarningLog "JAVA_HOME未设置，将跳过Javadoc生成"
            $env:MAVEN_OPTS = "$env:MAVEN_OPTS -Dmaven.javadoc.skip=true"
        }
    } else {
        Write-InfoLog "使用已设置的MAVEN_OPTS"
    }
    
    Write-InfoLog "Maven选项：$env:MAVEN_OPTS"
    Write-SuccessLog "Maven环境检查完成"
    
    return $true
}

function Test-ProjectStructure {
    Write-StepLog 3 "验证项目结构"
    
    if (!(Test-Path "pom.xml")) {
        Write-ErrorLog "根目录pom.xml文件不存在"
        return $false
    }
    Write-InfoLog "根目录pom.xml文件存在"
    
    $missingModules = @()
    foreach ($module in $PROJECT_MODULES) {
        if (!(Test-Path $module)) {
            $missingModules += $module
        } else {
            Write-InfoLog "模块目录存在：$module"
            
            if (!(Test-Path "$module\pom.xml")) {
                Write-WarningLog "模块pom.xml不存在：$module\pom.xml"
            }
        }
    }
    
    if (!(Test-Path $STANDALONE_MODULE)) {
        Write-WarningLog "独立服务模块不存在：$STANDALONE_MODULE"
        Write-WarningLog "在build-test模式下将无法启动测试服务器"
    } else {
        Write-InfoLog "独立服务模块存在：$STANDALONE_MODULE"
    }
    
    if ($missingModules.Count -gt 0) {
        Write-WarningLog "以下模块目录不存在：$($missingModules -join ', ')"
        Write-WarningLog "将跳过这些模块的编译"
    }
    
    Write-SuccessLog "项目结构验证完成"
    return $true
}

function Clear-Environment {
    Write-StepLog 4 "清理构建和测试环境"
    
    Write-InfoLog "清理Maven构建缓存..."
    
    # 先停止所有可能的测试服务器进程
    Stop-AllTestServers
    
    # 等待进程完全结束
    Start-Sleep -Seconds 3
    
    if (Test-Path "target") {
        try {
            Remove-Item "target" -Recurse -Force
            Write-InfoLog "清理根目录target目录"
        } catch {
            Write-WarningLog "清理根目录target目录时发生错误：$($_.Exception.Message)"
        }
    }
    
    foreach ($module in $PROJECT_MODULES) {
        if ((Test-Path $module) -and (Test-Path "$module\target")) {
            try {
                Remove-Item "$module\target" -Recurse -Force
                Write-InfoLog "清理模块target目录：$module"
            } catch {
                Write-WarningLog "清理模块 $module target目录时发生错误：$($_.Exception.Message)"
            }
        }
    }
    
    if ((Test-Path $STANDALONE_MODULE) -and (Test-Path "$STANDALONE_MODULE\target")) {
        try {
            Remove-Item "$STANDALONE_MODULE\target" -Recurse -Force
            Write-InfoLog "清理独立服务模块target目录：$STANDALONE_MODULE"
        } catch {
            Write-WarningLog "清理独立服务模块target目录时发生错误：$($_.Exception.Message)"
            Write-InfoLog "文件可能被进程占用，跳过此目录的清理"
        }
    }
    
    $testDataDirs = @(".\test-data", ".\data", ".\$STANDALONE_MODULE\data")
    foreach ($dir in $testDataDirs) {
        if (Test-Path $dir) {
            Remove-Item $dir -Recurse -Force
            Write-InfoLog "清理测试数据目录：$dir"
        }
    }
    
    Get-ChildItem -Recurse -Filter "*.tmp" | Remove-Item -Force 2>$null
    Get-ChildItem -Recurse -Filter "test-*.dat" | Remove-Item -Force 2>$null
    Get-ChildItem -Recurse -Filter "*.test" | Remove-Item -Force 2>$null
    
    Stop-AllTestServers
    
    Write-SuccessLog "环境清理完成"
    return $true
}

function Stop-AllTestServers {
    Write-InfoLog "停止所有可能运行的测试服务器进程..."
    
    try {
        $processes = Get-NetTCPConnection -LocalPort $TEST_SERVER_PORT -ErrorAction SilentlyContinue | 
                    Select-Object -ExpandProperty OwningProcess | 
                    ForEach-Object { Get-Process -Id $_ -ErrorAction SilentlyContinue }
        
        if ($processes) {
            Write-InfoLog "停止占用端口 $TEST_SERVER_PORT 的进程：$($processes.Id -join ', ')"
            $processes | Stop-Process -Force
            Start-Sleep -Seconds 2
        }
    } catch {
        # 忽略错误
    }
    
    if (Test-Path $SERVER_PID_FILE) {
        Remove-Item $SERVER_PID_FILE -Force
        Write-InfoLog "清理服务器PID文件"
    }
}

# ==================== 构建功能函数 ====================

function Invoke-ProjectCompile {
    Write-StepLog 5 "编译项目"
    
    $startTime = Get-Date
    
    Write-InfoLog "开始编译整个项目..."
    Write-InfoLog "编译命令：mvn clean compile -T 1C"
    
    try {
        $process = Start-Process -FilePath "mvn" -ArgumentList @(
            "clean", "compile", "-T", "1C",
            "-Dmaven.compiler.source=1.8",
            "-Dmaven.compiler.target=1.8",
            "-Dmaven.compiler.encoding=UTF-8",
            "-Dproject.build.sourceEncoding=UTF-8"
        ) -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\compile-stdout.log" -RedirectStandardError "$LOG_DIR\compile-stderr.log"
        
        Get-Content "$LOG_DIR\compile-stdout.log", "$LOG_DIR\compile-stderr.log" | Add-Content -Path $MAIN_LOG
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        
        if ($process.ExitCode -eq 0) {
            Write-SuccessLog "项目编译成功，耗时：$([math]::Round($duration))秒"
            return $true
        } else {
            Write-ErrorLog "项目编译失败，耗时：$([math]::Round($duration))秒"
            Write-ErrorLog "详细错误信息请查看日志文件：$MAIN_LOG"
            return $false
        }
    } catch {
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        Write-ErrorLog "项目编译失败，耗时：$([math]::Round($duration))秒"
        Write-ErrorLog "错误：$($_.Exception.Message)"
        return $false
    }
}

function Invoke-ProjectInstall {
    Write-StepLog 6 "安装项目到本地Maven仓库"
    
    $startTime = Get-Date
    
    Write-InfoLog "开始安装项目到本地Maven仓库..."
    Write-InfoLog "安装命令：mvn install -DskipTests -T 1C"
    
    try {
        $process = Start-Process -FilePath "mvn" -ArgumentList @(
            "install", "-DskipTests", "-T", "1C",
            "-Dmaven.compiler.source=1.8",
            "-Dmaven.compiler.target=1.8",
            "-Dmaven.compiler.encoding=UTF-8",
            "-Dproject.build.sourceEncoding=UTF-8"
        ) -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\install-stdout.log" -RedirectStandardError "$LOG_DIR\install-stderr.log"
        
        Get-Content "$LOG_DIR\install-stdout.log", "$LOG_DIR\install-stderr.log" | Add-Content -Path $MAIN_LOG
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        
        if ($process.ExitCode -eq 0) {
            Write-SuccessLog "项目安装成功，耗时：$([math]::Round($duration))秒"
            return $true
        } else {
            Write-ErrorLog "项目安装失败，耗时：$([math]::Round($duration))秒"
            Write-ErrorLog "详细错误信息请查看日志文件：$MAIN_LOG"
            return $false
        }
    } catch {
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        Write-ErrorLog "项目安装失败，耗时：$([math]::Round($duration))秒"
        Write-ErrorLog "错误：$($_.Exception.Message)"
        return $false
    }
}

function Test-BuildResults {
    Write-StepLog 7 "验证构建结果"
    
    $successCount = 0
    $totalCount = 0
    
    foreach ($module in $PROJECT_MODULES) {
        if (!(Test-Path $module)) {
            continue
        }
        
        $totalCount++
        
        if (Test-Path "$module\target\classes") {
            $classFiles = Get-ChildItem -Path "$module\target\classes" -Filter "*.class" -Recurse
            if ($classFiles.Count -gt 0) {
                Write-InfoLog "模块编译成功：$module (生成 $($classFiles.Count) 个class文件)"
                $successCount++
            } else {
                Write-WarningLog "模块编译异常：$module (未生成class文件)"
            }
        } else {
            Write-WarningLog "模块编译失败：$module (target\classes目录不存在)"
        }
        
        if (Test-Path "$module\target") {
            $jarFiles = Get-ChildItem -Path "$module\target" -Filter "*.jar"
            if ($jarFiles.Count -gt 0) {
                Write-InfoLog "模块JAR文件生成：$module ($($jarFiles.Count) 个JAR文件)"
            }
        }
    }
    
    Write-InfoLog "编译验证结果：$successCount/$totalCount 个模块编译成功"
    
    if ($successCount -eq $totalCount) {
        Write-SuccessLog "所有模块编译验证通过"
        return $true
    } else {
        Write-WarningLog "部分模块编译验证失败"
        return $true  # 允许部分失败
    }
}

# ==================== 测试功能函数 ====================

function Invoke-UnitTests {
    Write-StepLog 8 "运行单元测试"
    Write-TestPhaseLog "开始单元测试阶段"
    
    $totalModules = 0
    $successModules = 0
    $startTime = Get-Date
    
    foreach ($module in $TEST_MODULES) {
        if (!(Test-Path $module)) {
            Write-WarningLog "模块目录不存在，跳过：$module"
            continue
        }
        
        $totalModules++
        
        Write-TestPhaseLog "运行模块单元测试：$module"
        
        try {
            $process = Start-Process -FilePath "mvn" -ArgumentList @(
                "test", "-pl", $module,
                "-Dmaven.compiler.source=1.8",
                "-Dmaven.compiler.target=1.8", 
                "-Dtest=!**/*IntegrationTest,!**/*EndToEndTest"
            ) -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\test-$module-stdout.log" -RedirectStandardError "$LOG_DIR\test-$module-stderr.log"
            
            Get-Content "$LOG_DIR\test-$module-stdout.log", "$LOG_DIR\test-$module-stderr.log" | Add-Content -Path $MAIN_LOG
            
            if ($process.ExitCode -eq 0) {
                Write-SuccessLog "模块单元测试通过：$module"
                $successModules++
            } else {
                Write-ErrorLog "模块单元测试失败：$module"
            }
        } catch {
            Write-ErrorLog "模块单元测试失败：$module - $($_.Exception.Message)"
        }
    }
    
    foreach ($demoModule in $DEMO_MODULES) {
        if (Test-Path $demoModule) {
            Write-InfoLog "跳过演示模块单元测试：$demoModule（演示模块将在集成测试阶段执行）"
        }
    }
    
    $endTime = Get-Date
    $duration = ($endTime - $startTime).TotalSeconds
    
    Write-TestPhaseLog "单元测试阶段完成"
    Write-InfoLog "单元测试结果：$successModules/$totalModules 个模块通过，耗时：$([math]::Round($duration))秒"
    
    if ($successModules -eq $totalModules) {
        Write-SuccessLog "所有单元测试通过"
        return $true
    } else {
        Write-ErrorLog "部分单元测试失败"
        return $false
    }
}

# ==================== 服务器管理函数 ====================

function Start-TestServer {
    Write-TestPhaseLog "启动测试服务器"
    Write-InfoLog "启动测试服务器（端口：$TEST_SERVER_PORT）..."
    
    if (!(Test-Path $STANDALONE_MODULE)) {
        Write-ErrorLog "独立服务器模块不存在，无法启动测试服务器：$STANDALONE_MODULE"
        return $false
    }
    
    if (!(Test-Path "$STANDALONE_MODULE\start-server.ps1")) {
        Write-ErrorLog "独立服务器启动脚本不存在，无法启动测试服务器"
        return $false
    }
    
    # 先尝试停止可能运行的测试服务器
    Write-InfoLog "清理可能运行的测试服务器..."
    Stop-AllTestServers
    
    # 等待端口释放
    Start-Sleep -Seconds 2
    
    # 检查端口占用情况并强制清理
    Write-InfoLog "检查端口 $TEST_SERVER_PORT 的占用情况..."
    try {
        $processes = Get-NetTCPConnection -LocalPort $TEST_SERVER_PORT -ErrorAction SilentlyContinue | 
                    Select-Object -ExpandProperty OwningProcess | 
                    ForEach-Object { Get-Process -Id $_ -ErrorAction SilentlyContinue }
        
        if ($processes) {
            Write-WarningLog "发现占用端口 $TEST_SERVER_PORT 的进程：$($processes.Id -join ', ')"
            Write-InfoLog "强制停止这些进程..."
            $processes | Stop-Process -Force
            Start-Sleep -Seconds 3
            
            # 再次检查
            $remainingProcesses = Get-NetTCPConnection -LocalPort $TEST_SERVER_PORT -ErrorAction SilentlyContinue
            if ($remainingProcesses) {
                Write-ErrorLog "无法释放端口 $TEST_SERVER_PORT，仍有进程占用"
                return $false
            } else {
                Write-InfoLog "端口 $TEST_SERVER_PORT 已成功释放"
            }
        } else {
            Write-InfoLog "端口 $TEST_SERVER_PORT 当前未被占用"
        }
    } catch {
        Write-InfoLog "端口检查完成（可能未被占用）"
    }
    
    Write-InfoLog "确保独立服务器模块已编译..."
    if (!(Test-Path "$STANDALONE_MODULE\target\file-transfer-server-standalone-1.0.0.jar")) {
        Write-InfoLog "编译独立服务器模块..."
        try {
            $process = Start-Process -FilePath "mvn" -ArgumentList @(
                "package", "-pl", $STANDALONE_MODULE, "-DskipTests", "-q"
            ) -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\standalone-compile-stdout.log" -RedirectStandardError "$LOG_DIR\standalone-compile-stderr.log"
            
            Get-Content "$LOG_DIR\standalone-compile-stdout.log", "$LOG_DIR\standalone-compile-stderr.log" | Add-Content -Path $MAIN_LOG
            
            if ($process.ExitCode -ne 0) {
                Write-ErrorLog "独立服务器模块编译失败"
                return $false
            }
        } catch {
            Write-ErrorLog "独立服务器模块编译失败：$($_.Exception.Message)"
            return $false
        }
    }
    
    Write-InfoLog "使用独立服务器启动脚本启动测试服务器..."
    Push-Location $STANDALONE_MODULE
    
    try {
        # 构建启动命令，正确处理包含空格的路径
        $scriptCommand = "& '.\start-server.ps1' start -Port $TEST_SERVER_PORT -Background"
        
        # 如果设置了自定义JavaHome，添加到命令中
        if ($env:JAVA_HOME) {
            $scriptCommand += " -JavaHome '$env:JAVA_HOME'"
        }
        
        Write-InfoLog "启动命令：$scriptCommand"
        
        # 使用异步作业方式启动，避免进程卡住
        Write-InfoLog "启动服务器启动作业..."
        $job = Start-Job -ScriptBlock {
            param($Command, $WorkingDir)
            try {
                Set-Location $WorkingDir
                $result = Invoke-Expression $Command
                return @{
                    Success = $true
                    Output = $result
                    Error = $null
                }
            } catch {
                return @{
                    Success = $false
                    Output = $null
                    Error = $_.Exception.Message
                }
            }
        } -ArgumentList $scriptCommand, (Get-Location).Path
        
        Write-InfoLog "等待启动作业完成（最多90秒）..."
        $completed = Wait-Job $job -Timeout 90
        
        if ($completed) {
            $jobResult = Receive-Job $job
            if ($jobResult.Success) {
                Write-InfoLog "服务器启动脚本执行成功"
                if ($jobResult.Output) {
                    $jobResult.Output | Add-Content -Path $MAIN_LOG
                }
                $exitCode = 0
            } else {
                Write-ErrorLog "服务器启动脚本执行失败：$($jobResult.Error)"
                $exitCode = 1
            }
        } else {
            Write-WarningLog "服务器启动脚本执行超时（90秒），停止作业"
            Stop-Job $job
            $exitCode = 1
        }
        
        Remove-Job $job -Force
        $processResult = @{ ExitCode = $exitCode }
        
        # 无论脚本退出码如何，都尝试验证服务器是否真正启动
        Pop-Location
        Write-InfoLog "验证测试服务器是否成功启动..."
        
        # 等待服务器完全启动
        Start-Sleep -Seconds 3
        
        # 尝试连接服务器健康检查端点
        $serverStarted = $false
        $waitCount = 0
        $maxWaitTime = 45  # 最多等待45秒
        
        while ($waitCount -lt $maxWaitTime) {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:$TEST_SERVER_PORT/filetransfer/actuator/health" -TimeoutSec 2 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    $serverStarted = $true
                    break
                }
            } catch {
                # 忽略连接错误，继续等待
            }
            
            if ($waitCount % 10 -eq 0 -and $waitCount -gt 0) {
                Write-InfoLog "等待服务器响应中... ($waitCount/$maxWaitTime 秒)"
            }
            
            Start-Sleep -Seconds 1
            $waitCount++
        }
        
        if ($serverStarted) {
            Write-SuccessLog "测试服务器启动成功并响应健康检查"
            Write-InfoLog "服务器地址: http://localhost:$TEST_SERVER_PORT"
            Write-InfoLog "API文档: http://localhost:$TEST_SERVER_PORT/filetransfer/doc.html"
            Write-InfoLog "健康检查: http://localhost:$TEST_SERVER_PORT/filetransfer/actuator/health"
            
            # 尝试获取并保存PID
            Push-Location $STANDALONE_MODULE
            if (Test-Path "logs\server.pid") {
                $serverPid = Get-Content "logs\server.pid"
                Set-Content -Path "..\$SERVER_PID_FILE" -Value $serverPid
                Write-InfoLog "测试服务器PID：$serverPid"
            }
            Pop-Location
            
            return $true
        } else {
            Write-ErrorLog "测试服务器启动失败或无法响应健康检查（等待 $maxWaitTime 秒）"
            Write-InfoLog "尝试清理可能的残留进程..."
            
            # 尝试停止服务器
            Push-Location $STANDALONE_MODULE
            try {
                & ".\start-server.ps1" stop 2>&1 | Add-Content -Path $MAIN_LOG
            } catch {
                Write-WarningLog "停止服务器失败：$($_.Exception.Message)"
            }
            Pop-Location
            
            return $false
        }
    } catch {
        Pop-Location
        Write-ErrorLog "启动测试服务器时发生错误：$($_.Exception.Message)"
        return $false
    }
}

function Stop-TestServer {
    Write-TestPhaseLog "停止测试服务器"
    Write-InfoLog "停止测试服务器..."
    
    if ((Test-Path $STANDALONE_MODULE) -and (Test-Path "$STANDALONE_MODULE\start-server.ps1")) {
        Write-InfoLog "使用独立服务器启动脚本停止测试服务器..."
        Push-Location $STANDALONE_MODULE
        try {
            & ".\start-server.ps1" stop 2>&1 | Add-Content -Path $MAIN_LOG
        } catch {
            Write-WarningLog "使用启动脚本停止服务器失败：$($_.Exception.Message)"
        }
        Pop-Location
    }
    
    if (Test-Path $SERVER_PID_FILE) {
        try {
            $serverPid = Get-Content $SERVER_PID_FILE
            $process = Get-Process -Id $serverPid -ErrorAction SilentlyContinue
            if ($process) {
                Write-InfoLog "通过PID停止测试服务器（PID：$serverPid）..."
                Stop-Process -Id $serverPid -Force
                
                $waitCount = 0
                while ($waitCount -lt $SERVER_SHUTDOWN_TIMEOUT_SECONDS) {
                    $proc = Get-Process -Id $serverPid -ErrorAction SilentlyContinue
                    if (!$proc) {
                        break
                    }
                    Start-Sleep -Seconds 1
                    $waitCount++
                }
                
                if (Get-Process -Id $serverPid -ErrorAction SilentlyContinue) {
                    Write-WarningLog "强制停止测试服务器进程（PID：$serverPid）"
                    Stop-Process -Id $serverPid -Force
                }
                
                Write-InfoLog "测试服务器已停止（PID：$serverPid）"
            }
            Remove-Item $SERVER_PID_FILE -Force
        } catch {
            Write-WarningLog "停止测试服务器时发生错误：$($_.Exception.Message)"
        }
    }
    
    try {
        $processes = Get-NetTCPConnection -LocalPort $TEST_SERVER_PORT -ErrorAction SilentlyContinue | 
                    Select-Object -ExpandProperty OwningProcess | 
                    ForEach-Object { Get-Process -Id $_ -ErrorAction SilentlyContinue }
        
        if ($processes) {
            Write-WarningLog "强制停止占用端口 $TEST_SERVER_PORT 的进程：$($processes.Id -join ', ')"
            $processes | Stop-Process -Force
            Start-Sleep -Seconds 2
        }
    } catch {
        # 忽略错误
    }
    
    Write-SuccessLog "测试服务器停止完成"
}

function Invoke-ClientDemoTest {
    Write-TestPhaseLog "运行客户端演示测试"
    
    if (!(Test-Path "file-transfer-client-demo")) {
        Write-WarningLog "客户端演示模块不存在，跳过演示测试"
        return $false
    }
    
    $demoStartTime = Get-Date
    $demoSuccess = $true
    
    $demoServerHost = "localhost"
    $demoServerPort = $TEST_SERVER_PORT
    
    Write-InfoLog "启动客户端演示测试（服务器：${demoServerHost}:${demoServerPort}）"
    
    try {
        $process = Start-Process -FilePath "mvn" -ArgumentList @(
            "exec:java", "-pl", "file-transfer-client-demo",
            "-Dexec.mainClass=com.sdesrd.filetransfer.demo.FileTransferClientDemo",
            "-Ddemo.server.host=$demoServerHost",
            "-Ddemo.server.port=$demoServerPort",
            "-Ddemo.user.name=demo",
            "-Ddemo.user.secret=demo-secret-key-2024",
            "-Ddemo.upload.dir=demo-files/upload",
            "-Ddemo.download.dir=demo-files/download",
            "-Dmaven.compiler.source=1.8",
            "-Dmaven.compiler.target=1.8"
        ) -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\demo-stdout.log" -RedirectStandardError "$LOG_DIR\demo-stderr.log"
        
        Get-Content "$LOG_DIR\demo-stdout.log", "$LOG_DIR\demo-stderr.log" | Add-Content -Path $MAIN_LOG
        
        if ($process.ExitCode -eq 0) {
            Write-SuccessLog "客户端演示测试执行成功"
        } else {
            Write-WarningLog "客户端演示测试执行失败或超时（${DEMO_TEST_TIMEOUT_SECONDS}秒）"
            $demoSuccess = $false
        }
    } catch {
        Write-WarningLog "客户端演示测试执行失败：$($_.Exception.Message)"
        $demoSuccess = $false
    }
    
    $demoEndTime = Get-Date
    $demoDuration = ($demoEndTime - $demoStartTime).TotalSeconds
    
    Write-InfoLog "客户端演示测试耗时：$([math]::Round($demoDuration))秒"
    
    if (Test-Path "demo-files") {
        Remove-Item "demo-files" -Recurse -Force
        Write-InfoLog "清理演示文件目录"
    }
    
    return $demoSuccess
}

function Invoke-IntegrationTests {
    Write-StepLog 9 "运行集成测试"
    Write-TestPhaseLog "开始集成测试阶段"
    
    $startTime = Get-Date
    $integrationSuccess = $true
    
    if (!(Start-TestServer)) {
        Write-ErrorLog "无法启动测试服务器，跳过集成测试"
        return $false
    }
    
    Write-InfoLog "等待测试服务器稳定..."
    Start-Sleep -Seconds 3
    
    Write-TestPhaseLog "检查并运行端到端测试"
    $endToEndTestFound = $false
    foreach ($module in $PROJECT_MODULES) {
        if (Test-Path "$module\src\test\java") {
            $testFiles = Get-ChildItem -Path "$module\src\test\java" -Recurse -Include "*EndToEndTest.java", "*EndToEndTransferTest.java", "*IntegrationTest.java"
            if ($testFiles.Count -gt 0) {
                Write-TestPhaseLog "在模块 $module 中发现端到端/集成测试，正在执行..."
                try {
                    $process = Start-Process -FilePath "mvn" -ArgumentList @(
                        "test", "-pl", $module,
                        "-Dtest=*EndToEndTest,*EndToEndTransferTest,*IntegrationTest",
                        "-Dserver.port=$TEST_SERVER_PORT",
                        "-Dtest.server.host=localhost",
                        "-Dtest.server.port=$TEST_SERVER_PORT",
                        "-Dtest.user.name=demo",
                        "-Dtest.user.secret=demo-secret-key-2024",
                        "-Dmaven.compiler.source=1.8",
                        "-Dmaven.compiler.target=1.8"
                    ) -Wait -PassThru -RedirectStandardOutput "$LOG_DIR\integration-$module-stdout.log" -RedirectStandardError "$LOG_DIR\integration-$module-stderr.log"
                    
                    Get-Content "$LOG_DIR\integration-$module-stdout.log", "$LOG_DIR\integration-$module-stderr.log" | Add-Content -Path $MAIN_LOG
                    
                    if ($process.ExitCode -eq 0) {
                        Write-SuccessLog "模块 $module 端到端/集成测试通过"
                        $endToEndTestFound = $true
                    } else {
                        Write-ErrorLog "模块 $module 端到端/集成测试失败"
                        $integrationSuccess = $false
                    }
                } catch {
                    Write-ErrorLog "模块 $module 端到端/集成测试失败：$($_.Exception.Message)"
                    $integrationSuccess = $false
                }
            }
        }
    }
    
    if (Invoke-ClientDemoTest) {
        Write-SuccessLog "客户端演示测试通过"
    } else {
        Write-WarningLog "客户端演示测试失败，但不影响整体测试结果"
    }
    
    Stop-TestServer
    
    $endTime = Get-Date
    $duration = ($endTime - $startTime).TotalSeconds
    
    Write-TestPhaseLog "集成测试阶段完成"
    Write-InfoLog "集成测试耗时：$([math]::Round($duration))秒"
    
    if ($integrationSuccess) {
        Write-SuccessLog "集成测试通过"
        return $true
    } else {
        Write-ErrorLog "集成测试失败"
        return $false
    }
}

# ==================== 报告生成函数 ====================

function Get-TestResults {
    $totalTests = 0
    $failedTests = 0
    $skippedTests = 0
    
    $uniqueModules = $TEST_MODULES
    
    foreach ($module in $uniqueModules) {
        if (!(Test-Path "$module\target\surefire-reports")) {
            continue
        }
        
        $moduleTestFiles = Get-ChildItem -Path "$module\target\surefire-reports" -Filter "TEST-*.xml"
        
        if ($moduleTestFiles.Count -gt 0) {
            $moduleTests = 0
            $moduleFailures = 0
            $moduleErrors = 0
            $moduleSkipped = 0
            
            foreach ($xmlFile in $moduleTestFiles) {
                try {
                    [xml]$xmlContent = Get-Content $xmlFile.FullName
                    $testsuite = $xmlContent.testsuite
                    
                    $tests = [int]$testsuite.tests
                    $failures = [int]$testsuite.failures
                    $errors = [int]$testsuite.errors
                    $skipped = [int]$testsuite.skipped
                    
                    $moduleTests += $tests
                    $moduleFailures += $failures
                    $moduleErrors += $errors
                    $moduleSkipped += $skipped
                } catch {
                    Write-WarningLog "无法解析测试结果文件：$($xmlFile.FullName)"
                }
            }
            
            $moduleFailed = $moduleFailures + $moduleErrors
            
            $totalTests += $moduleTests
            $failedTests += $moduleFailed
            $skippedTests += $moduleSkipped
            
            Write-InfoLog "$module`: $moduleTests 个测试，$moduleFailed 个失败，$moduleSkipped 个跳过"
        } else {
            Write-WarningLog "$module`: 未找到测试结果文件"
        }
    }
    
    $passedTests = $totalTests - $failedTests - $skippedTests
    
    Write-Host ""
    Write-Host "==========================================" -ForegroundColor White
    Write-Host "           测试结果汇总" -ForegroundColor White
    Write-Host "==========================================" -ForegroundColor White
    Write-Host "总测试数: $totalTests"
    Write-Host "通过测试: $passedTests"
    Write-Host "失败测试: $failedTests"
    Write-Host "跳过测试: $skippedTests"
    
    if ($failedTests -eq 0) {
        Write-Host "测试结果: " -NoNewline
        Write-Host "全部通过" -ForegroundColor Green
        return $true
    } else {
        Write-Host "测试结果: " -NoNewline
        Write-Host "有失败" -ForegroundColor Red
        return $false
    }
}

function New-FinalReport {
    param([string]$ExecutionMode)
    
    Write-StepLog 10 "生成最终报告"
    
    $reportFile = "$LOG_DIR\final-report-$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
    
    $reportContent = @"
========================================================
        文件传输SDK构建和测试最终报告
========================================================
执行时间：$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
脚本版本：$SCRIPT_VERSION
Java版本：$(try { & java -version 2>&1 | Select-Object -First 1 } catch { "未知" })
Maven版本：$(try { & mvn -version | Select-Object -First 1 } catch { "未知" })

执行模式：
"@

    if ($ExecutionMode -eq $MODE_BUILD) {
        $reportContent += "  构建模式：仅编译项目`n"
    } else {
        $reportContent += "  完整模式：构建 + 测试`n"
    }
    
    $reportContent += "`n项目模块：`n"
    foreach ($module in $PROJECT_MODULES) {
        if (Test-Path $module) {
            $reportContent += "  ✓ $module`n"
        } else {
            $reportContent += "  ✗ $module (目录不存在)`n"
        }
    }
    
    $reportContent += "`n构建结果：`n"
    foreach ($module in $PROJECT_MODULES) {
        if (!(Test-Path $module)) {
            continue
        }
        
        if (Test-Path "$module\target\classes") {
            $classCount = (Get-ChildItem -Path "$module\target\classes" -Filter "*.class" -Recurse).Count
            $reportContent += "  $module`: 编译成功 ($classCount 个class文件)`n"
        } else {
            $reportContent += "  $module`: 编译失败`n"
        }
    }
    
    if ($ExecutionMode -eq $MODE_BUILD_TEST) {
        $reportContent += "`n测试结果详情：`n"
        
        foreach ($module in $TEST_MODULES) {
            if (!(Test-Path "$module\target\surefire-reports")) {
                $reportContent += "  $module`: 未运行测试`n"
                continue
            }
            
            $testFiles = (Get-ChildItem -Path "$module\target\surefire-reports" -Filter "TEST-*.xml").Count
            if ($testFiles -gt 0) {
                $reportContent += "  $module`: $testFiles 个测试套件`n"
            } else {
                $reportContent += "  $module`: 无测试结果`n"
            }
        }
        
        foreach ($demoModule in $DEMO_MODULES) {
            if (Test-Path $demoModule) {
                $reportContent += "  $demoModule`: 演示模块（通过集成测试中的演示程序执行验证）`n"
            }
        }
    }
    
    $reportContent += "`n详细日志：$MAIN_LOG`n"
    $reportContent += "========================================================"
    
    Set-Content -Path $reportFile -Value $reportContent -Encoding UTF8
    
    Write-InfoLog "最终报告已生成：$reportFile"
    
    # 显示报告内容
    Get-Content $reportFile | ForEach-Object { Write-Host $_ }
    
    return $true
}

# ==================== 帮助信息 ====================

function Show-Help {
    Write-Host "========================================================" -ForegroundColor White
    Write-Host "    $SCRIPT_NAME" -ForegroundColor White
    Write-Host "    版本：$SCRIPT_VERSION" -ForegroundColor White
    Write-Host "========================================================" -ForegroundColor White
    Write-Host ""
    Write-Host "用法: .\build-and-test.ps1 [模式] [选项]"
    Write-Host ""
    Write-Host "执行模式："
    Write-Host "  -Mode build              仅执行构建（编译+安装），不运行测试"
    Write-Host "  -Mode build-test         执行完整流程（构建+测试）[默认]"
    Write-Host ""
    Write-Host "Java环境选项："
    Write-Host "  -JavaHome PATH           指定Java JDK路径（可选，默认使用系统Java）"
    Write-Host ""
    Write-Host "其他选项："
    Write-Host "  -Help                    显示此帮助信息"
    Write-Host ""
    Write-Host "使用示例："
    Write-Host "  .\build-and-test.ps1                              # 完整构建和测试流程"
    Write-Host "  .\build-and-test.ps1 -Mode build                  # 仅构建项目"
    Write-Host "  .\build-and-test.ps1 -Mode build-test             # 构建并测试项目"
    Write-Host "  .\build-and-test.ps1 -JavaHome 'C:\Java\jdk8'     # 使用指定的Java路径"
    Write-Host ""
    Write-Host "默认配置："
    Write-Host "  Java路径: 使用系统JAVA_HOME环境变量（可通过-JavaHome参数指定）"
    Write-Host "  测试端口: $TEST_SERVER_PORT"
    Write-Host "  日志目录: $LOG_DIR"
    Write-Host ""
    Write-Host "说明："
    Write-Host "  - build模式：编译项目并安装到本地Maven仓库"
    Write-Host "  - build-test模式：在build基础上运行单元测试和集成测试"
    Write-Host "  - 集成测试会自动启动和停止file-transfer-standalone服务"
    Write-Host "  - 脚本会优先使用JAVA_HOME环境变量，如果无效则使用系统默认Java"
    Write-Host "  - 所有日志都会记录到日志文件中，便于问题排查"
    Write-Host ""
}

# ==================== 主程序 ====================

function Invoke-Cleanup {
    param([int]$ExitCode)
    
    # 停止测试服务器
    Stop-TestServer
    
    if ($ExitCode -ne 0) {
        Write-ErrorLog "执行过程中发生错误，退出码：$ExitCode"
        Write-InfoLog "详细错误信息请查看日志文件：$MAIN_LOG"
    }
}

function Main {
    # 显示帮助信息
    if ($Help) {
        Show-Help
        exit 0
    }
    
    # 验证执行模式
    if ($Mode -notin @($MODE_BUILD, $MODE_BUILD_TEST)) {
        Write-ErrorLog "未知模式: $Mode"
        Write-Host ""
        Show-Help
        exit 1
    }
    
    # 显示脚本头部信息
    Show-Header
    
    # 初始化日志
    Initialize-Logging
    
    Write-InfoLog "执行模式：$Mode"
    
    # 执行主要流程
    $executionFailed = $false
    
    try {
        # 步骤1-4：环境检查和准备
        if (!(Setup-JavaEnvironment $JavaHome)) {
            $executionFailed = $true
        } elseif (!$executionFailed -and !(Test-MavenEnvironment)) {
            $executionFailed = $true
        } elseif (!$executionFailed -and !(Test-ProjectStructure)) {
            $executionFailed = $true
        } elseif (!$executionFailed -and !(Clear-Environment)) {
            $executionFailed = $true
        # 步骤5-7：构建流程
        } elseif (!$executionFailed -and !(Invoke-ProjectCompile)) {
            $executionFailed = $true
        } elseif (!$executionFailed -and !(Invoke-ProjectInstall)) {
            $executionFailed = $true
        } elseif (!$executionFailed -and !(Test-BuildResults)) {
            $executionFailed = $true
        }
        
        # 步骤8-11：测试流程（仅在build-test模式下执行）
        if (!$executionFailed -and ($Mode -eq $MODE_BUILD_TEST)) {
            Write-TestPhaseLog "开始测试流程"
            
            if (!(Invoke-UnitTests)) {
                $executionFailed = $true
            } elseif (!(Invoke-IntegrationTests)) {
                $executionFailed = $true
            }
            
            # 收集测试结果
            if (!(Get-TestResults)) {
                $executionFailed = $true
            }
        }
        
        # 生成最终报告
        New-FinalReport $Mode | Out-Null
        
    } catch {
        Write-ErrorLog "执行过程中发生未捕获的错误：$($_.Exception.Message)"
        $executionFailed = $true
    } finally {
        # 清理资源
        Invoke-Cleanup $(if ($executionFailed) { 1 } else { 0 })
    }
    
    # 返回结果
    if ($executionFailed) {
        Write-ErrorLog "执行失败"
        exit 1
    } else {
        if ($Mode -eq $MODE_BUILD) {
            Write-SuccessLog "构建成功完成"
        } else {
            Write-SuccessLog "构建和测试成功完成"
        }
        exit 0
    }
}

# 执行主函数
Main 