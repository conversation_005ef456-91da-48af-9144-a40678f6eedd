package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sdesrd.filetransfer.server.config.CleanupConstants;
import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.entity.FileChunkRecord;
import com.sdesrd.filetransfer.server.entity.FileTransferRecord;
import com.sdesrd.filetransfer.server.mapper.FileChunkRecordMapper;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;
import com.sdesrd.filetransfer.server.service.CleanupStatisticsService.CleanupOperationInfo;
import com.sdesrd.filetransfer.server.util.UlidUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输监控服务
 *
 * 负责定时清理过期的传输记录和分块记录，监控传输状态，
 * 并提供详细的清理统计信息
 */
@Slf4j
@Service
public class FileTransferMonitorService {

    @Autowired
    private FileTransferRecordMapper transferRecordMapper;

    @Autowired
    private FileChunkRecordMapper chunkRecordMapper;

    @Autowired
    private FileTransferProperties properties;

    @Autowired
    private CleanupStatisticsService cleanupStatisticsService;

    @Autowired
    private DatabaseManagementService databaseManagementService;

    private ScheduledExecutorService scheduler;
    
    @PostConstruct
    public void init() {
        // 验证清理配置
        try {
            properties.validateCleanupConfig();
        } catch (IllegalArgumentException e) {
            log.error("清理配置验证失败: {}", e.getMessage());
            throw e;
        }

        // 检查清理功能是否启用
        if (!properties.isCleanupEnabled()) {
            log.info("清理功能已禁用，跳过清理任务启动");
            return;
        }

        scheduler = Executors.newScheduledThreadPool(CleanupConstants.CLEANUP_THREAD_POOL_SIZE);

        // 启动清理任务
        scheduler.scheduleAtFixedRate(this::cleanupExpiredRecords,
                CleanupConstants.CLEANUP_INITIAL_DELAY_SECONDS,
                properties.getCleanupIntervalSeconds(),
                TimeUnit.SECONDS);

        // 启动监控任务
        scheduler.scheduleAtFixedRate(this::monitorTransfers,
                CleanupConstants.MONITOR_INITIAL_DELAY_SECONDS,
                CleanupConstants.MONITOR_INTERVAL_SECONDS,
                TimeUnit.SECONDS);

        log.info("文件传输监控服务启动完成 - 清理间隔: {}秒, 记录过期时间: {}秒",
                properties.getCleanupIntervalSeconds(), properties.getRecordExpireTimeSeconds());
    }
    
    @PreDestroy
    public void destroy() {
        if (scheduler != null && !scheduler.isShutdown()) {
            log.info("正在关闭文件传输监控服务...");
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(CleanupConstants.THREAD_POOL_SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                    log.warn("清理任务未能在{}秒内正常关闭，强制关闭", CleanupConstants.THREAD_POOL_SHUTDOWN_TIMEOUT_SECONDS);
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.warn("等待清理任务关闭时被中断，强制关闭");
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("文件传输监控服务关闭完成");
    }
    
    /**
     * 清理过期的传输记录和分块记录
     */
    private void cleanupExpiredRecords() {
        if (!properties.isCleanupEnabled()) {
            return;
        }

        // 检查是否有数据库重建操作正在进行
        if (isRebuildInProgress()) {
            log.debug("跳过清理操作：数据库重建正在进行中");
            return;
        }

        CleanupOperationInfo operation = cleanupStatisticsService.startCleanupOperation();

        try {
            long cleanedTransferRecords = cleanupExpiredTransferRecords();
            long cleanedChunkRecords = cleanupExpiredChunkRecords();
            long cleanedPhysicalFiles = 0;

            // 如果启用了物理文件删除，则执行物理文件清理
            if (properties.isDeletePhysicalFiles()) {
                cleanedPhysicalFiles = cleanupOrphanedPhysicalFiles();
            }

            cleanupStatisticsService.completeCleanupOperation(operation,
                    cleanedTransferRecords, cleanedChunkRecords, cleanedPhysicalFiles);

        } catch (Exception e) {
            cleanupStatisticsService.failCleanupOperation(operation, e.getMessage());
            log.error("清理过期记录任务执行失败", e);
        }
    }

    /**
     * 清理过期的传输记录
     *
     * @return 清理的记录数
     */
    private long cleanupExpiredTransferRecords() {
        Instant expireTime = Instant.now().minus(properties.getRecordExpireTime(), ChronoUnit.MILLIS);
        String expireTimeStr = expireTime.toString();

        // 智能清理策略：优先保留可能用于秒传的记录
        // 查询过期的传输记录，但排除可能用于跨用户秒传的记录
        QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
        query.lt("create_time", expireTimeStr)
             .in("status", CleanupConstants.TRANSFER_STATUS_COMPLETED, CleanupConstants.TRANSFER_STATUS_FAILED);

        List<FileTransferRecord> expiredRecords = transferRecordMapper.selectList(query);

        if (expiredRecords.isEmpty()) {
            log.debug("没有找到过期的传输记录");
            return 0;
        }

        // 过滤出真正可以安全删除的记录
        List<FileTransferRecord> safeToDeleteRecords = filterSafeToDeleteRecords(expiredRecords);

        if (safeToDeleteRecords.isEmpty()) {
            log.debug("所有过期记录都可能用于秒传，暂不清理");
            return 0;
        }

        long cleanedCount = 0;
        int batchSize = Math.min(properties.getMaxBatchDeleteSize(), safeToDeleteRecords.size());

        log.debug("开始清理过期传输记录，总数: {}, 安全清理数: {}, 批次大小: {}",
                expiredRecords.size(), safeToDeleteRecords.size(), batchSize);

        for (int i = 0; i < safeToDeleteRecords.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, safeToDeleteRecords.size());
            List<FileTransferRecord> batch = safeToDeleteRecords.subList(i, endIndex);

            for (FileTransferRecord record : batch) {
                try {
                    // 检查是否存在info.json文件，确保容错机制可用
                    if (hasMetadataFile(record)) {
                        // 删除数据库记录，但保留物理文件和info.json
                        transferRecordMapper.deleteById(record.getId());
                        cleanedCount++;

                        log.debug("清理传输记录（保留物理文件） - ID: {}, 文件: {}",
                                record.getId(), record.getFileName());
                    } else {
                        log.warn("跳过清理，缺少元数据文件 - ID: {}, 路径: {}",
                                record.getId(), record.getFilePath());
                    }

                } catch (Exception e) {
                    log.error("清理传输记录失败 - ID: {}, 错误: {}", record.getId(), e.getMessage());
                }
            }
        }

        if (cleanedCount > 0) {
            log.info("清理过期传输记录完成，清理数量: {} / 总过期数: {}", cleanedCount, expiredRecords.size());
        }

        return cleanedCount;
    }

    /**
     * 清理过期的分块记录
     * 使用智能清理策略：优先清理已完成传输的分块记录
     *
     * @return 清理的记录数
     */
    private long cleanupExpiredChunkRecords() {
        long totalCleaned = 0;

        // 1. 清理已完成传输的分块记录
        totalCleaned += cleanupCompletedTransferChunks();

        // 2. 清理孤立的分块记录（对应的传输记录已被删除）
        totalCleaned += cleanupOrphanedChunks();

        // 3. 清理过期的失败分块记录
        totalCleaned += cleanupExpiredFailedChunks();

        return totalCleaned;
    }

    /**
     * 清理已完成传输的分块记录
     * 对于已完成的传输，分块记录已无用处，可以立即清理
     */
    private long cleanupCompletedTransferChunks() {
        // 查找已完成且过期的传输记录
        Instant expireTime = Instant.now().minus(properties.getRecordExpireTime(), ChronoUnit.MILLIS);
        String expireTimeStr = expireTime.toString();

        QueryWrapper<FileTransferRecord> transferQuery = new QueryWrapper<>();
        transferQuery.eq("status", CleanupConstants.TRANSFER_STATUS_COMPLETED)
                     .lt("complete_time", expireTimeStr);

        List<FileTransferRecord> completedTransfers = transferRecordMapper.selectList(transferQuery);

        if (completedTransfers.isEmpty()) {
            log.debug("没有找到已完成的过期传输记录");
            return 0;
        }

        // 删除对应的分块记录
        long cleanedCount = 0;
        for (FileTransferRecord transfer : completedTransfers) {
            try {
                QueryWrapper<FileChunkRecord> chunkQuery = new QueryWrapper<>();
                chunkQuery.eq("transfer_id", transfer.getId());

                int deletedChunks = chunkRecordMapper.delete(chunkQuery);
                cleanedCount += deletedChunks;

                if (deletedChunks > 0) {
                    log.debug("清理已完成传输的分块记录 - 传输ID: {}, 清理数量: {}",
                            transfer.getId(), deletedChunks);
                }

            } catch (Exception e) {
                log.error("清理已完成传输分块记录失败 - 传输ID: {}, 错误: {}",
                        transfer.getId(), e.getMessage());
            }
        }

        if (cleanedCount > 0) {
            log.info("清理已完成传输的分块记录完成，清理数量: {}", cleanedCount);
        }

        return cleanedCount;
    }

    /**
     * 清理孤立的分块记录
     * 这些分块记录对应的传输记录已被删除
     */
    private long cleanupOrphanedChunks() {
        // 查找所有分块记录的传输ID
        QueryWrapper<FileChunkRecord> chunkQuery = new QueryWrapper<>();
        chunkQuery.select("DISTINCT transfer_id");
        List<FileChunkRecord> allChunks = chunkRecordMapper.selectList(chunkQuery);

        if (allChunks.isEmpty()) {
            return 0;
        }

        long cleanedCount = 0;

        // 检查每个传输ID是否还存在对应的传输记录
        for (FileChunkRecord chunk : allChunks) {
            String transferId = chunk.getTransferId();

            // 检查传输记录是否存在
            FileTransferRecord transferRecord = transferRecordMapper.selectById(transferId);
            if (transferRecord == null) {
                // 传输记录不存在，删除孤立的分块记录
                try {
                    QueryWrapper<FileChunkRecord> deleteQuery = new QueryWrapper<>();
                    deleteQuery.eq("transfer_id", transferId);

                    int deletedChunks = chunkRecordMapper.delete(deleteQuery);
                    cleanedCount += deletedChunks;

                    if (deletedChunks > 0) {
                        log.debug("清理孤立分块记录 - 传输ID: {}, 清理数量: {}",
                                transferId, deletedChunks);
                    }

                } catch (Exception e) {
                    log.error("清理孤立分块记录失败 - 传输ID: {}, 错误: {}",
                            transferId, e.getMessage());
                }
            }
        }

        if (cleanedCount > 0) {
            log.info("清理孤立分块记录完成，清理数量: {}", cleanedCount);
        }

        return cleanedCount;
    }

    /**
     * 清理过期的失败分块记录
     */
    private long cleanupExpiredFailedChunks() {
        Instant expireTime = Instant.now().minus(properties.getChunkExpireTime(), ChronoUnit.MILLIS);
        String expireTimeStr = expireTime.toString();

        // 查询过期的失败分块记录
        QueryWrapper<FileChunkRecord> query = new QueryWrapper<>();
        query.eq("status", CleanupConstants.CHUNK_STATUS_FAILED)
             .lt("create_time", expireTimeStr);

        List<FileChunkRecord> expiredFailedChunks = chunkRecordMapper.selectList(query);

        if (expiredFailedChunks.isEmpty()) {
            log.debug("没有找到过期的失败分块记录");
            return 0;
        }

        long cleanedCount = 0;
        int batchSize = Math.min(properties.getMaxBatchDeleteSize(), expiredFailedChunks.size());

        log.debug("开始清理过期失败分块记录，总数: {}, 批次大小: {}", expiredFailedChunks.size(), batchSize);

        for (int i = 0; i < expiredFailedChunks.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, expiredFailedChunks.size());
            List<FileChunkRecord> batch = expiredFailedChunks.subList(i, endIndex);

            for (FileChunkRecord chunk : batch) {
                try {
                    chunkRecordMapper.deleteById(chunk.getId());
                    cleanedCount++;

                    log.debug("清理过期失败分块记录 - ID: {}, 传输ID: {}, 分块索引: {}",
                            chunk.getId(), chunk.getTransferId(), chunk.getChunkIndex());

                } catch (Exception e) {
                    log.error("清理过期失败分块记录失败 - ID: {}, 错误: {}", chunk.getId(), e.getMessage());
                }
            }
        }

        if (cleanedCount > 0) {
            log.info("清理过期失败分块记录完成，清理数量: {}", cleanedCount);
        }

        return cleanedCount;
    }

    /**
     * 清理孤立的物理文件
     * 删除数据库中没有对应记录的物理文件
     *
     * @return 清理的文件数
     */
    private long cleanupOrphanedPhysicalFiles() {
        // TODO: 实现物理文件清理逻辑
        // 这是一个复杂的操作，需要遍历存储目录并检查每个文件是否在数据库中有对应记录
        // 为了安全起见，暂时返回0，后续可以根据需要实现
        log.debug("物理文件清理功能暂未实现");
        return 0;
    }

    /**
     * 监控传输状态
     */
    private void monitorTransfers() {
        try {
            // 统计各状态的传输数量
            long pendingCount = countByStatus(CleanupConstants.TRANSFER_STATUS_PENDING);
            long transferringCount = countByStatus(CleanupConstants.TRANSFER_STATUS_TRANSFERRING);
            long completedCount = countByStatus(CleanupConstants.TRANSFER_STATUS_COMPLETED);
            long failedCount = countByStatus(CleanupConstants.TRANSFER_STATUS_FAILED);
            long pausedCount = countByStatus(CleanupConstants.TRANSFER_STATUS_PAUSED);

            log.info("传输状态统计 - 待传输: {}, 传输中: {}, 已完成: {}, 已失败: {}, 已暂停: {}",
                    pendingCount, transferringCount, completedCount, failedCount, pausedCount);

            // 检查长时间无进展的传输
            checkStuckTransfers();

            // 检查失败率
            checkFailureRate(completedCount, failedCount);

        } catch (Exception e) {
            log.error("监控传输状态任务执行失败", e);
        }
    }
    
    /**
     * 检查卡住的传输
     */
    private void checkStuckTransfers() {
        Instant stuckTime = Instant.now().minus(CleanupConstants.STUCK_TRANSFER_THRESHOLD_MS, ChronoUnit.MILLIS);
        String stuckTimeStr = stuckTime.toString();

        QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
        query.eq("status", CleanupConstants.TRANSFER_STATUS_TRANSFERRING)
             .lt("update_time", stuckTimeStr);

        List<FileTransferRecord> stuckTransfers = transferRecordMapper.selectList(query);

        if (!stuckTransfers.isEmpty()) {
            log.warn("发现卡住的传输 - 数量: {}, 超时阈值: {}小时",
                    stuckTransfers.size(), CleanupConstants.STUCK_TRANSFER_THRESHOLD_HOURS);

            for (FileTransferRecord record : stuckTransfers) {
                log.warn("卡住的传输 - ID: {}, 文件: {}, 最后更新: {}, 已传输: {}/{}",
                        record.getId(), record.getFileName(), record.getUpdateTime(),
                        record.getTransferredSize(), record.getFileSize());

                // 可以选择将其标记为失败
                // markTransferAsFailed(record.getId(), "传输超时");
            }
        }
    }
    
    /**
     * 检查失败率
     */
    private void checkFailureRate(long completedCount, long failedCount) {
        long totalCount = completedCount + failedCount;
        if (totalCount > 0) {
            double failureRate = (double) failedCount / totalCount * 100;

            if (failureRate > CleanupConstants.FAILURE_RATE_WARNING_THRESHOLD) {
                log.warn("传输失败率过高: {:.2f}% ({}/{}), 警告阈值: {:.1f}%",
                        failureRate, failedCount, totalCount, CleanupConstants.FAILURE_RATE_WARNING_THRESHOLD);
            } else {
                log.debug("传输失败率正常: {:.2f}% ({}/{})", failureRate, failedCount, totalCount);
            }
        }
    }
    
    /**
     * 按状态统计传输数量
     */
    private long countByStatus(int status) {
        QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
        query.eq("status", status);
        return transferRecordMapper.selectCount(query);
    }
    
    /**
     * 手动触发清理操作
     *
     * @return 清理操作信息
     */
    public CleanupOperationInfo manualCleanup() {
        log.info("手动触发清理操作");

        CleanupOperationInfo operation = cleanupStatisticsService.startCleanupOperation();

        try {
            long cleanedTransferRecords = cleanupExpiredTransferRecords();
            long cleanedChunkRecords = cleanupExpiredChunkRecords();
            long cleanedPhysicalFiles = 0;

            if (properties.isDeletePhysicalFiles()) {
                cleanedPhysicalFiles = cleanupOrphanedPhysicalFiles();
            }

            cleanupStatisticsService.completeCleanupOperation(operation,
                    cleanedTransferRecords, cleanedChunkRecords, cleanedPhysicalFiles);

            log.info("手动清理操作完成 - 传输记录: {}, 分块记录: {}, 物理文件: {}",
                    cleanedTransferRecords, cleanedChunkRecords, cleanedPhysicalFiles);

        } catch (Exception e) {
            cleanupStatisticsService.failCleanupOperation(operation, e.getMessage());
            log.error("手动清理操作失败", e);
        }

        return operation;
    }

    /**
     * 获取传输统计信息
     */
    public TransferStatistics getTransferStatistics() {
        TransferStatistics stats = new TransferStatistics();
        stats.setPendingCount(countByStatus(CleanupConstants.TRANSFER_STATUS_PENDING));
        stats.setTransferringCount(countByStatus(CleanupConstants.TRANSFER_STATUS_TRANSFERRING));
        stats.setCompletedCount(countByStatus(CleanupConstants.TRANSFER_STATUS_COMPLETED));
        stats.setFailedCount(countByStatus(CleanupConstants.TRANSFER_STATUS_FAILED));
        stats.setPausedCount(countByStatus(CleanupConstants.TRANSFER_STATUS_PAUSED));

        // 计算成功率
        long totalCount = stats.getCompletedCount() + stats.getFailedCount();
        if (totalCount > 0) {
            stats.setSuccessRate((double) stats.getCompletedCount() / totalCount * 100);
        }

        return stats;
    }
    
    /**
     * 传输统计信息
     */
    public static class TransferStatistics {
        private long pendingCount;
        private long transferringCount;
        private long completedCount;
        private long failedCount;
        private long pausedCount;
        private double successRate;

        // Getters and Setters
        public long getPendingCount() { return pendingCount; }
        public void setPendingCount(long pendingCount) { this.pendingCount = pendingCount; }

        public long getTransferringCount() { return transferringCount; }
        public void setTransferringCount(long transferringCount) { this.transferringCount = transferringCount; }

        public long getCompletedCount() { return completedCount; }
        public void setCompletedCount(long completedCount) { this.completedCount = completedCount; }

        public long getFailedCount() { return failedCount; }
        public void setFailedCount(long failedCount) { this.failedCount = failedCount; }

        public long getPausedCount() { return pausedCount; }
        public void setPausedCount(long pausedCount) { this.pausedCount = pausedCount; }

        public double getSuccessRate() { return successRate; }
        public void setSuccessRate(double successRate) { this.successRate = successRate; }

        /**
         * 获取总传输数量
         */
        public long getTotalCount() {
            return pendingCount + transferringCount + completedCount + failedCount + pausedCount;
        }

        /**
         * 获取活跃传输数量（待传输 + 传输中）
         */
        public long getActiveCount() {
            return pendingCount + transferringCount;
        }
    }

    /**
     * 过滤出可以安全删除的记录
     * 保留可能用于跨用户秒传的记录
     *
     * @param expiredRecords 过期的记录列表
     * @return 可以安全删除的记录列表
     */
    private List<FileTransferRecord> filterSafeToDeleteRecords(List<FileTransferRecord> expiredRecords) {
        // 简化策略：如果记录数量不多，可以全部保留以支持秒传
        // 如果记录数量过多，则按一定比例保留

        if (expiredRecords.size() <= 100) {
            // 记录数量较少，暂不清理以保持秒传能力
            return Collections.emptyList();
        }

        // 记录数量较多时，可以清理一部分
        // 这里采用简单策略：保留最近的50%记录用于秒传
        int keepCount = expiredRecords.size() / 2;

        // 按创建时间排序，保留较新的记录
        expiredRecords.sort((r1, r2) -> {
            if (r1.getCreateTime() == null || r2.getCreateTime() == null) {
                return 0;
            }
            return r2.getCreateTime().compareTo(r1.getCreateTime()); // 降序，新的在前
        });

        // 返回可以删除的记录（较旧的记录）
        return expiredRecords.subList(keepCount, expiredRecords.size());
    }

    /**
     * 检查记录是否有对应的元数据文件
     *
     * @param record 传输记录
     * @return 如果存在元数据文件则返回true
     */
    private boolean hasMetadataFile(FileTransferRecord record) {
        if (record == null || record.getFileId() == null || record.getFilePath() == null) {
            return false;
        }

        try {
            // 根据文件路径推断元数据文件路径
            File physicalFile = new File(record.getFilePath());
            File parentDir = physicalFile.getParentFile();

            if (parentDir != null && parentDir.exists()) {
                File metadataFile = new File(parentDir, "info.json");
                return metadataFile.exists() && metadataFile.isFile();
            }

            return false;

        } catch (Exception e) {
            log.debug("检查元数据文件失败 - fileId: {}, 错误: {}", record.getFileId(), e.getMessage());
            return false;
        }
    }

    /**
     * 检查数据库重建操作是否正在进行
     *
     * @return 如果重建正在进行则返回true
     */
    private boolean isRebuildInProgress() {
        try {
            Map<String, Object> rebuildStatus = databaseManagementService.getRebuildStatus();
            return Boolean.TRUE.equals(rebuildStatus.get("inProgress"));
        } catch (Exception e) {
            log.debug("检查重建状态失败，假设未在进行: {}", e.getMessage());
            return false;
        }
    }
}